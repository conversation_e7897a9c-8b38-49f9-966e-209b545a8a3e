"""
重新理解IHDP数据结构
"""
import pandas as pd
import numpy as np

# 加载数据
data = pd.read_csv('./data/ihdp.csv')

print("=== IHDP数据结构重新分析 ===")
print("列名:", list(data.columns))

# 检查前几行
print("\n前3行数据:")
for i in range(3):
    row = data.iloc[i]
    print(f"样本 {i}:")
    print(f"  treatment: {row['treatment']}")
    print(f"  y_factual: {row['y_factual']:.3f}")
    print(f"  y_cfactual: {row['y_cfactual']:.3f}")
    print(f"  mu0: {row['mu0']:.3f}")
    print(f"  mu1: {row['mu1']:.3f}")
    
    # 检查逻辑
    if row['treatment'] == 1:
        print(f"  治疗组: y_factual应该接近mu1? {abs(row['y_factual'] - row['mu1']):.3f}")
        print(f"  治疗组: y_cfactual应该接近mu0? {abs(row['y_cfactual'] - row['mu0']):.3f}")
    else:
        print(f"  控制组: y_factual应该接近mu0? {abs(row['y_factual'] - row['mu0']):.3f}")
        print(f"  控制组: y_cfactual应该接近mu1? {abs(row['y_cfactual'] - row['mu1']):.3f}")
    print()

# 统计分析
treatment = data['treatment']
y_factual = data['y_factual']
y_cfactual = data['y_cfactual']
mu0 = data['mu0']
mu1 = data['mu1']

print("=== 数据一致性分析 ===")

# 对于治疗组
t1_mask = treatment == 1
if np.sum(t1_mask) > 0:
    # y_factual vs mu1
    diff_factual_mu1 = np.abs(y_factual[t1_mask] - mu1[t1_mask])
    # y_cfactual vs mu0  
    diff_cfactual_mu0 = np.abs(y_cfactual[t1_mask] - mu0[t1_mask])
    
    print(f"治疗组 (n={np.sum(t1_mask)}):")
    print(f"  |y_factual - mu1| 平均: {diff_factual_mu1.mean():.6f}")
    print(f"  |y_cfactual - mu0| 平均: {diff_cfactual_mu0.mean():.6f}")

# 对于控制组
t0_mask = treatment == 0
if np.sum(t0_mask) > 0:
    # y_factual vs mu0
    diff_factual_mu0 = np.abs(y_factual[t0_mask] - mu0[t0_mask])
    # y_cfactual vs mu1
    diff_cfactual_mu1 = np.abs(y_cfactual[t0_mask] - mu1[t0_mask])
    
    print(f"控制组 (n={np.sum(t0_mask)}):")
    print(f"  |y_factual - mu0| 平均: {diff_factual_mu0.mean():.6f}")
    print(f"  |y_cfactual - mu1| 平均: {diff_cfactual_mu1.mean():.6f}")

print("\n=== 正确的数据映射应该是 ===")
print("对于每个样本:")
print("  如果 treatment = 1: Y(0) = y_cfactual, Y(1) = y_factual")
print("  如果 treatment = 0: Y(0) = y_factual, Y(1) = y_cfactual")
print("而不是直接使用 mu0 和 mu1!")

# 验证正确映射
print("\n=== 验证正确映射 ===")
correct_y0 = np.where(treatment == 1, y_cfactual, y_factual)
correct_y1 = np.where(treatment == 1, y_factual, y_cfactual)

correct_ate = np.mean(correct_y1 - correct_y0)
mu_ate = np.mean(mu1 - mu0)

print(f"使用 y_factual/y_cfactual 计算的ATE: {correct_ate:.6f}")
print(f"使用 mu0/mu1 计算的ATE: {mu_ate:.6f}")

print(f"\nmu0 和 mu1 可能是期望值，而 y_factual/y_cfactual 是实际观测值（带噪声）")

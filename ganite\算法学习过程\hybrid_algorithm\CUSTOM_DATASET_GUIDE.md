# 自定义数据集使用指南

## 数据格式要求

你的数据集需要包含以下列：
- **特征列 (X)**：协变量/特征
- **治疗列 (T)**：二元治疗变量 (0/1)
- **Y(0)列**：控制组潜在结果
- **Y(1)列**：治疗组潜在结果
- **事实结果列 (可选)**：观测到的结果

## 支持的文件格式

### 1. CSV格式
```csv
x1,x2,x3,x4,treatment,factual_y,y0,y1
1.2,0.5,2.1,0.8,1,0.7,0.3,0.7
0.9,1.1,1.5,1.2,0,0.4,0.4,0.8
...
```

### 2. NPZ格式
```python
import numpy as np

# 保存数据
np.savez('your_data.npz', 
         x=features,      # shape: (n_samples, n_features)
         t=treatment,     # shape: (n_samples,)
         y0=y0_outcomes,  # shape: (n_samples,)
         y1=y1_outcomes)  # shape: (n_samples,)
```

### 3. NPY格式
```python
# 数据应该是一个2D数组，列顺序为：[features..., treatment, factual_y, y0, y1]
data = np.column_stack([features, treatment, factual_y, y0, y1])
np.save('your_data.npy', data)
```

## 使用方法

### 基本用法
```bash
python main.py --data_name custom --data_path ./your_data.csv --file_format csv
```

### 指定列索引
```bash
python main.py \
    --data_name custom \
    --data_path ./your_data.csv \
    --file_format csv \
    --x_cols "0,1,2,3,4" \
    --t_col 5 \
    --factual_y_col 6 \
    --y0_col 7 \
    --y1_col 8
```

### NPZ文件示例
```bash
python main.py \
    --data_name custom \
    --data_path ./your_data.npz \
    --file_format npz
```

## 参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--data_name` | - | 设置为 'custom' |
| `--data_path` | - | 数据文件路径 |
| `--file_format` | 'csv' | 文件格式 (csv/npz/npy) |
| `--x_cols` | None | 特征列索引，逗号分隔 |
| `--t_col` | -4 | 治疗列索引 |
| `--factual_y_col` | -3 | 事实结果列索引 |
| `--y0_col` | -2 | Y(0)列索引 |
| `--y1_col` | -1 | Y(1)列索引 |

## 数据预处理

系统会自动进行以下预处理：
1. **特征标准化**：Z-score标准化 (可选)
2. **数据类型转换**：转换为float32
3. **形状调整**：确保正确的张量形状
4. **训练/测试划分**：默认8:2比例

## 完整示例

假设你有一个包含因果推断数据的CSV文件：

```python
# 创建示例数据
import numpy as np
import pandas as pd

n_samples = 1000
n_features = 10

# 生成特征
X = np.random.randn(n_samples, n_features)

# 生成治疗分配
treatment_prob = 1 / (1 + np.exp(-X[:, 0]))  # 基于第一个特征
T = np.random.binomial(1, treatment_prob)

# 生成潜在结果
Y0 = X[:, 1] + 0.5 * X[:, 2] + np.random.normal(0, 0.1, n_samples)
Y1 = Y0 + 0.3 + 0.2 * X[:, 0]  # 治疗效应

# 生成观测结果
Y_obs = T * Y1 + (1 - T) * Y0

# 创建DataFrame
data = pd.DataFrame(X, columns=[f'x{i}' for i in range(n_features)])
data['treatment'] = T
data['y_observed'] = Y_obs
data['y0'] = Y0
data['y1'] = Y1

# 保存
data.to_csv('example_causal_data.csv', index=False)
```

运行训练：
```bash
python main.py \
    --data_name custom \
    --data_path ./example_causal_data.csv \
    --file_format csv \
    --x_cols "0,1,2,3,4,5,6,7,8,9" \
    --t_col 10 \
    --factual_y_col 11 \
    --y0_col 12 \
    --y1_col 13 \
    --stage1_epochs 200 \
    --stage2_epochs 200
```

## 注意事项

1. **数据质量**：确保Y(0)和Y(1)是真实的潜在结果
2. **特征选择**：选择与结果相关的重要特征
3. **样本大小**：建议至少1000个样本以获得稳定结果
4. **治疗平衡**：治疗组和控制组样本数量不要过于不平衡
5. **缺失值**：确保数据中没有缺失值

## 验证数据加载

可以添加以下代码验证数据是否正确加载：

```python
# 在main.py中添加数据验证
print(f"Training samples: {len(train_x)}")
print(f"Test samples: {len(test_x)}")
print(f"Feature dimension: {train_x.shape[1]}")
print(f"Treatment ratio: {np.mean(train_t):.3f}")
print(f"Outcome ratio: {np.mean(train_y):.3f}")
print(f"True ATE: {np.mean(train_potential_y[:, 1] - train_potential_y[:, 0]):.3f}")
```

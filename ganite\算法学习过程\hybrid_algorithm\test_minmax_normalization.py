"""
测试Min-Max标准化的效果
"""
import sys
import os
sys.path.append(os.path.dirname(__file__))

from data_loader import load_custom_dataset, preprocess_data
import numpy as np

print("=== 测试Min-Max标准化 vs Z-score标准化 ===")

# 加载原始数据
x_cols = [5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29]
result = load_custom_dataset(
    data_path='./data/ihdp.csv',
    train_rate=0.8,
    file_format='csv',
    x_cols=x_cols,
    t_col=0,
    y0_col=3,
    y1_col=4,
    factual_y_col=1
)

train_x, train_t, train_y, train_potential_y, test_x, test_t, test_y, test_potential_y = result

print("原始数据范围:")
print(f"  Y范围: {train_y.min():.3f} 到 {train_y.max():.3f}")
print(f"  Y(0)范围: {train_potential_y[:, 0].min():.3f} 到 {train_potential_y[:, 0].max():.3f}")
print(f"  Y(1)范围: {train_potential_y[:, 1].min():.3f} 到 {train_potential_y[:, 1].max():.3f}")
print(f"  真实ATE: {np.mean(train_potential_y[:, 1] - train_potential_y[:, 0]):.6f}")

# 测试Z-score标准化
print("\n=== Z-score标准化 ===")
processed_data_zscore = preprocess_data(
    train_x, train_t, train_y, train_potential_y,
    test_x, test_t, test_y, test_potential_y,
    normalize_x=True,
    normalize_y=True,
    normalization_method='zscore'
)

_, _, proc_train_y_z, proc_train_potential_y_z, _, _, _, _ = processed_data_zscore

print(f"  Y范围: {proc_train_y_z.min():.3f} 到 {proc_train_y_z.max():.3f}")
print(f"  Y均值: {np.mean(proc_train_y_z):.6f}")
print(f"  Y标准差: {np.std(proc_train_y_z):.6f}")
print(f"  Y(0)范围: {proc_train_potential_y_z[:, 0].min():.3f} 到 {proc_train_potential_y_z[:, 0].max():.3f}")
print(f"  Y(1)范围: {proc_train_potential_y_z[:, 1].min():.3f} 到 {proc_train_potential_y_z[:, 1].max():.3f}")
print(f"  标准化后ATE: {np.mean(proc_train_potential_y_z[:, 1] - proc_train_potential_y_z[:, 0]):.6f}")

# 测试Min-Max标准化
print("\n=== Min-Max标准化 ===")
processed_data_minmax = preprocess_data(
    train_x, train_t, train_y, train_potential_y,
    test_x, test_t, test_y, test_potential_y,
    normalize_x=True,
    normalize_y=True,
    normalization_method='minmax'
)

_, _, proc_train_y_mm, proc_train_potential_y_mm, _, _, _, _ = processed_data_minmax

print(f"  Y范围: {proc_train_y_mm.min():.3f} 到 {proc_train_y_mm.max():.3f}")
print(f"  Y均值: {np.mean(proc_train_y_mm):.6f}")
print(f"  Y标准差: {np.std(proc_train_y_mm):.6f}")
print(f"  Y(0)范围: {proc_train_potential_y_mm[:, 0].min():.3f} 到 {proc_train_potential_y_mm[:, 0].max():.3f}")
print(f"  Y(1)范围: {proc_train_potential_y_mm[:, 1].min():.3f} 到 {proc_train_potential_y_mm[:, 1].max():.3f}")
print(f"  标准化后ATE: {np.mean(proc_train_potential_y_mm[:, 1] - proc_train_potential_y_mm[:, 0]):.6f}")

# 计算标准化参数
y_min = np.min(train_y)
y_max = np.max(train_y)
y_range = y_max - y_min

print(f"\nMin-Max标准化参数:")
print(f"  Y最小值: {y_min:.6f}")
print(f"  Y最大值: {y_max:.6f}")
print(f"  Y范围: {y_range:.6f}")

print(f"\n=== 对比分析 ===")
print("Min-Max标准化优势:")
print("  - 输出范围固定在[0, 1]，与sigmoid激活函数完美匹配")
print("  - 保持数据的原始分布形状")
print("  - 没有负值，适合概率解释")

print("\nZ-score标准化特点:")
print("  - 输出范围不固定，可能有负值")
print("  - 标准化后均值为0，标准差为1")
print("  - 对异常值更敏感")

"""
调试数据预处理过程
"""
import sys
import os
sys.path.append(os.path.dirname(__file__))

from data_loader import load_custom_dataset, preprocess_data
import numpy as np

print("=== 完整数据加载和预处理测试 ===")

# 1. 加载原始数据
x_cols = [5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29]
result = load_custom_dataset(
    data_path='./data/ihdp.csv',
    train_rate=0.8,
    file_format='csv',
    x_cols=x_cols,
    t_col=0,
    y0_col=3,
    y1_col=4,
    factual_y_col=1
)

train_x, train_t, train_y, train_potential_y, test_x, test_t, test_y, test_potential_y = result

print("原始数据:")
print(f"  训练集真实ATE: {np.mean(train_potential_y[:, 1] - train_potential_y[:, 0]):.6f}")
print(f"  测试集真实ATE: {np.mean(test_potential_y[:, 1] - test_potential_y[:, 0]):.6f}")
print(f"  训练集事实结果范围: {train_y.min():.3f} 到 {train_y.max():.3f}")

# 2. 预处理数据
processed_data = preprocess_data(
    train_x, train_t, train_y, train_potential_y,
    test_x, test_t, test_y, test_potential_y,
    normalize_x=True,
    normalize_y=False
)

proc_train_x, proc_train_t, proc_train_y, proc_train_potential_y, proc_test_x, proc_test_t, proc_test_y, proc_test_potential_y = processed_data

print("\n预处理后数据:")
print(f"  训练集真实ATE: {np.mean(proc_train_potential_y[:, 1] - proc_train_potential_y[:, 0]):.6f}")
print(f"  测试集真实ATE: {np.mean(proc_test_potential_y[:, 1] - proc_test_potential_y[:, 0]):.6f}")
print(f"  训练集事实结果范围: {proc_train_y.min():.3f} 到 {proc_train_y.max():.3f}")

# 3. 检查特征标准化
print(f"\n特征标准化检查:")
print(f"  原始特征范围: {train_x.min():.3f} 到 {train_x.max():.3f}")
print(f"  标准化后范围: {proc_train_x.min():.3f} 到 {proc_train_x.max():.3f}")
print(f"  训练集特征均值: {np.mean(proc_train_x, axis=0)[:5]}")  # 前5个特征
print(f"  训练集特征标准差: {np.std(proc_train_x, axis=0)[:5]}")  # 前5个特征

# 4. 检查数据类型和形状
print(f"\n数据类型和形状:")
print(f"  train_x: {proc_train_x.dtype}, {proc_train_x.shape}")
print(f"  train_t: {proc_train_t.dtype}, {proc_train_t.shape}")
print(f"  train_y: {proc_train_y.dtype}, {proc_train_y.shape}")
print(f"  train_potential_y: {proc_train_potential_y.dtype}, {proc_train_potential_y.shape}")

# 5. 检查潜在结果一致性（预处理后）
print(f"\n预处理后潜在结果一致性:")
treatment_mask = proc_train_t.flatten() == 1
control_mask = proc_train_t.flatten() == 0

if np.sum(treatment_mask) > 0:
    diff_t1 = proc_train_y[treatment_mask].flatten() - proc_train_potential_y[treatment_mask, 1]
    print(f"  治疗组: 事实结果 vs Y(1) 差异范围: {diff_t1.min():.6f} 到 {diff_t1.max():.6f}")

if np.sum(control_mask) > 0:
    diff_t0 = proc_train_y[control_mask].flatten() - proc_train_potential_y[control_mask, 0]
    print(f"  控制组: 事实结果 vs Y(0) 差异范围: {diff_t0.min():.6f} 到 {diff_t0.max():.6f}")

# 6. 检查是否有异常值
print(f"\n异常值检查:")
print(f"  训练集中是否有NaN: {np.isnan(proc_train_x).any()}")
print(f"  训练集中是否有Inf: {np.isinf(proc_train_x).any()}")
print(f"  潜在结果中是否有NaN: {np.isnan(proc_train_potential_y).any()}")

# 7. 模拟模型输入数据统计
print(f"\n模型输入数据统计:")
print(f"  特征维度: {proc_train_x.shape[1]}")
print(f"  治疗比例: {np.mean(proc_train_t):.3f}")
print(f"  结果均值: {np.mean(proc_train_y):.3f}")
print(f"  Y(0)均值: {np.mean(proc_train_potential_y[:, 0]):.3f}")
print(f"  Y(1)均值: {np.mean(proc_train_potential_y[:, 1]):.3f}")

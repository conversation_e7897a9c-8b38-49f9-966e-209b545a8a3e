"""
调试自定义数据加载函数
"""
import sys
import os
sys.path.append(os.path.dirname(__file__))

from data_loader import load_custom_dataset
import numpy as np

print("=== 测试自定义数据加载函数 ===")

# 使用与main.py相同的参数
x_cols = [5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29]
t_col = 0
factual_y_col = 1
y0_col = 3
y1_col = 4

try:
    result = load_custom_dataset(
        data_path='./data/ihdp.csv',
        train_rate=0.8,
        file_format='csv',
        x_cols=x_cols,
        t_col=t_col,
        y0_col=y0_col,
        y1_col=y1_col,
        factual_y_col=factual_y_col
    )
    
    train_x, train_t, train_y, train_potential_y, test_x, test_t, test_y, test_potential_y = result
    
    print("加载成功！")
    print(f"训练集: {len(train_x)} 样本")
    print(f"测试集: {len(test_x)} 样本")
    print(f"特征维度: {train_x.shape[1]}")
    
    print("\n=== 训练集数据检查 ===")
    print(f"治疗比例: {np.mean(train_t):.3f}")
    print(f"事实结果范围: {train_y.min():.3f} 到 {train_y.max():.3f}")
    print(f"Y(0)范围: {train_potential_y[:, 0].min():.3f} 到 {train_potential_y[:, 0].max():.3f}")
    print(f"Y(1)范围: {train_potential_y[:, 1].min():.3f} 到 {train_potential_y[:, 1].max():.3f}")
    
    # 检查潜在结果一致性
    print("\n=== 潜在结果一致性检查 ===")
    treatment_mask = train_t.flatten() == 1
    control_mask = train_t.flatten() == 0
    
    if np.sum(treatment_mask) > 0:
        diff_t1 = train_y[treatment_mask].flatten() - train_potential_y[treatment_mask, 1]
        print(f"治疗组: 事实结果 vs Y(1) 差异范围: {diff_t1.min():.6f} 到 {diff_t1.max():.6f}")
    
    if np.sum(control_mask) > 0:
        diff_t0 = train_y[control_mask].flatten() - train_potential_y[control_mask, 0]
        print(f"控制组: 事实结果 vs Y(0) 差异范围: {diff_t0.min():.6f} 到 {diff_t0.max():.6f}")
    
    # 计算真实ATE
    true_ate = np.mean(train_potential_y[:, 1] - train_potential_y[:, 0])
    print(f"\n训练集真实ATE: {true_ate:.6f}")
    
    # 检查测试集
    test_true_ate = np.mean(test_potential_y[:, 1] - test_potential_y[:, 0])
    print(f"测试集真实ATE: {test_true_ate:.6f}")
    
    print("\n=== 特征统计 ===")
    print(f"特征均值范围: {np.mean(train_x, axis=0).min():.3f} 到 {np.mean(train_x, axis=0).max():.3f}")
    print(f"特征标准差范围: {np.std(train_x, axis=0).min():.3f} 到 {np.std(train_x, axis=0).max():.3f}")
    
except Exception as e:
    print(f"加载失败: {e}")
    import traceback
    traceback.print_exc()

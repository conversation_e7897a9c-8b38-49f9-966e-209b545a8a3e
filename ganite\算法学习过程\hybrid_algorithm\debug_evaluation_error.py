"""
检查评估错误：是否需要反标准化
"""
import sys
import os
sys.path.append(os.path.dirname(__file__))

from data_loader import load_custom_dataset, preprocess_data
import numpy as np

print("=== 评估错误检查 ===")

# 加载和预处理数据
x_cols = [5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29]
result = load_custom_dataset(
    data_path='./data/ihdp.csv',
    train_rate=0.8,
    file_format='csv',
    x_cols=x_cols,
    t_col=0,
    y0_col=3,
    y1_col=4,
    factual_y_col=1
)

train_x, train_t, train_y, train_potential_y, test_x, test_t, test_y, test_potential_y = result

# 标准化
processed_data = preprocess_data(
    train_x, train_t, train_y, train_potential_y,
    test_x, test_t, test_y, test_potential_y,
    normalize_x=True,
    normalize_y=True,
    normalization_method='minmax'
)

proc_train_x, proc_train_t, proc_train_y, proc_train_potential_y, proc_test_x, proc_test_t, proc_test_y, proc_test_potential_y = processed_data

print("1. 标准化参数")
y_min = np.min(train_y)
y_max = np.max(train_y)
y_range = y_max - y_min
print(f"Y_min: {y_min:.6f}")
print(f"Y_max: {y_max:.6f}")
print(f"Y_range: {y_range:.6f}")

print("\n2. 原始数据范围")
print(f"训练集Y: [{train_y.min():.3f}, {train_y.max():.3f}]")
print(f"测试集Y: [{test_y.min():.3f}, {test_y.max():.3f}]")
print(f"训练集潜在结果: Y(0)[{train_potential_y[:, 0].min():.3f}, {train_potential_y[:, 0].max():.3f}], Y(1)[{train_potential_y[:, 1].min():.3f}, {train_potential_y[:, 1].max():.3f}]")
print(f"测试集潜在结果: Y(0)[{test_potential_y[:, 0].min():.3f}, {test_potential_y[:, 0].max():.3f}], Y(1)[{test_potential_y[:, 1].min():.3f}, {test_potential_y[:, 1].max():.3f}]")

print("\n3. 标准化后数据范围")
print(f"训练集Y: [{proc_train_y.min():.3f}, {proc_train_y.max():.3f}]")
print(f"测试集Y: [{proc_test_y.min():.3f}, {proc_test_y.max():.3f}]")
print(f"训练集潜在结果: Y(0)[{proc_train_potential_y[:, 0].min():.3f}, {proc_train_potential_y[:, 0].max():.3f}], Y(1)[{proc_train_potential_y[:, 1].min():.3f}, {proc_train_potential_y[:, 1].max():.3f}]")
print(f"测试集潜在结果: Y(0)[{proc_test_potential_y[:, 0].min():.3f}, {proc_test_potential_y[:, 0].max():.3f}], Y(1)[{proc_test_potential_y[:, 1].min():.3f}, {proc_test_potential_y[:, 1].max():.3f}]")

print("\n4. 问题分析")
print("⚠️ 关键问题：")
print(f"  - 测试集标准化后最大值: {proc_test_potential_y.max():.3f} > 1.0")
print(f"  - 模型sigmoid输出范围: [0, 1]")
print(f"  - 模型无法预测超出1.0的值")

print("\n5. 模拟模型预测")
# 模拟一个"完美"的模型预测（实际上被截断到[0,1]）
simulated_pred = np.clip(proc_test_potential_y, 0, 1)

print("模拟预测（截断到[0,1]）:")
print(f"  预测Y(0): [{simulated_pred[:, 0].min():.3f}, {simulated_pred[:, 0].max():.3f}]")
print(f"  预测Y(1): [{simulated_pred[:, 1].min():.3f}, {simulated_pred[:, 1].max():.3f}]")

# 计算"虚假"的好性能
mse_y0 = np.mean((proc_test_potential_y[:, 0] - simulated_pred[:, 0]) ** 2)
mse_y1 = np.mean((proc_test_potential_y[:, 1] - simulated_pred[:, 1]) ** 2)

print(f"\n模拟MSE (截断后):")
print(f"  MSE Y(0): {mse_y0:.6f}")
print(f"  MSE Y(1): {mse_y1:.6f}")

print("\n6. 正确的解决方案")
print("应该:")
print("1. 确保测试集Y值在训练集范围内，或")
print("2. 使用不同的输出激活函数（如线性），或")
print("3. 在评估时反标准化到原始尺度")

print("\n7. 反标准化测试")
def denormalize_minmax(normalized_data, data_min, data_max):
    """反标准化"""
    return normalized_data * (data_max - data_min) + data_min

# 反标准化预测结果
denorm_pred = denormalize_minmax(simulated_pred, y_min, y_max)
print("反标准化后的预测:")
print(f"  预测Y(0): [{denorm_pred[:, 0].min():.3f}, {denorm_pred[:, 0].max():.3f}]")
print(f"  预测Y(1): [{denorm_pred[:, 1].min():.3f}, {denorm_pred[:, 1].max():.3f}]")

# 在原始尺度上计算MSE
orig_mse_y0 = np.mean((test_potential_y[:, 0] - denorm_pred[:, 0]) ** 2)
orig_mse_y1 = np.mean((test_potential_y[:, 1] - denorm_pred[:, 1]) ** 2)

print(f"\n原始尺度MSE:")
print(f"  MSE Y(0): {orig_mse_y0:.6f}")
print(f"  MSE Y(1): {orig_mse_y1:.6f}")

print("\n=== 结论 ===")
print("当前的'好结果'是虚假的，因为:")
print("1. 测试集数据超出了训练集范围")
print("2. 模型输出被sigmoid截断到[0,1]")
print("3. 评估在标准化空间进行，掩盖了真实误差")
print("4. 需要修复数据分割或评估方法")

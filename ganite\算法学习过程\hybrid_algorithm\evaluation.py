"""
评估模块：使用VGANITE的评估指标PEHE和ATE
"""

import numpy as np
import tensorflow as tf
import sys
import os

# 添加vganite路径以导入评估函数
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'vganite'))

try:
    from metrics import PEHE, ATE
except ImportError:
    print("Warning: Could not import from vganite metrics. Using fallback implementation.")
    
    def PEHE(y_true, y_pred):
        """Fallback PEHE implementation"""
        return np.sqrt(np.mean((y_true - y_pred) ** 2))
    
    def ATE(y_true, y_pred):
        """Fallback ATE implementation"""
        return np.abs(np.mean(y_true) - np.mean(y_pred))

def evaluate_model(model, test_x, test_t, test_y, test_potential_y, stage='stage1'):
    """
    评估模型性能
    
    Args:
        model: 训练好的模型
        test_x, test_t, test_y: 测试数据
        test_potential_y: 真实的潜在结果 [Y(0), Y(1)]
        stage: 模型阶段 ('stage1' 或 'stage2')
    
    Returns:
        评估结果字典
    """
    
    # 预测潜在结果
    if stage == 'stage1':
        # 阶段一模型：输出两种治疗结果的logits
        outputs = model([test_x, test_t, test_y], training=False)
        y_pred_logits = outputs['y_logits']
        y_pred_probs = tf.nn.sigmoid(y_pred_logits)
    else:
        # 阶段二模型：精炼后的潜在结果
        y_pred_logits = model(test_x, training=False)
        y_pred_probs = tf.nn.sigmoid(y_pred_logits)

    # 转换为numpy
    y_pred_probs = y_pred_probs.numpy()
    test_potential_y = np.array(test_potential_y)

    # 确保维度正确：y_pred_probs应该是[n_samples, 2]，表示[Y(0), Y(1)]
    if y_pred_probs.shape[1] != 2:
        raise ValueError(f"预测结果维度错误：期望[n_samples, 2]，实际{y_pred_probs.shape}")

    # 计算PEHE（VGANITE的PEHE函数期望潜在结果矩阵[Y(0), Y(1)]）
    pehe_score = PEHE(test_potential_y, y_pred_probs)

    # 计算ATE误差（VGANITE的ATE函数也期望潜在结果矩阵[Y(0), Y(1)]）
    ate_error = ATE(test_potential_y, y_pred_probs)

    # 计算真实和预测的ITE（用于其他指标）
    true_ite = test_potential_y[:, 1] - test_potential_y[:, 0]
    pred_ite = y_pred_probs[:, 1] - y_pred_probs[:, 0]
    
    # 计算其他指标
    mse_y0 = np.mean((test_potential_y[:, 0] - y_pred_probs[:, 0]) ** 2)
    mse_y1 = np.mean((test_potential_y[:, 1] - y_pred_probs[:, 1]) ** 2)
    
    # 计算事实预测准确性
    test_t_np = np.array(test_t).flatten()
    test_y_np = np.array(test_y).flatten()
    
    # 获取事实预测
    factual_pred = test_t_np * y_pred_probs[:, 1] + (1 - test_t_np) * y_pred_probs[:, 0]
    factual_mse = np.mean((test_y_np - factual_pred) ** 2)
    
    # 计算y0和y1预测值的统计信息
    y0_pred_stats = {
        'mean': float(np.mean(y_pred_probs[:, 0])),
        'std': float(np.std(y_pred_probs[:, 0])),
        'min': float(np.min(y_pred_probs[:, 0])),
        'max': float(np.max(y_pred_probs[:, 0]))
    }

    y1_pred_stats = {
        'mean': float(np.mean(y_pred_probs[:, 1])),
        'std': float(np.std(y_pred_probs[:, 1])),
        'min': float(np.min(y_pred_probs[:, 1])),
        'max': float(np.max(y_pred_probs[:, 1]))
    }

    results = {
        'pehe': float(pehe_score),
        'ate_error': float(ate_error),
        'mse_y0': float(mse_y0),
        'mse_y1': float(mse_y1),
        'factual_mse': float(factual_mse),
        'true_ate': float(np.mean(true_ite)),
        'pred_ate': float(np.mean(pred_ite)),
        'y0_pred_stats': y0_pred_stats,
        'y1_pred_stats': y1_pred_stats
    }
    
    return results

def evaluate_stage1_losses(model, x, t, y, config):
    """
    评估阶段一模型的各项损失
    
    Args:
        model: 阶段一模型
        x, t, y: 数据
        config: 配置对象
    
    Returns:
        损失字典
    """
    
    # 前向传播
    outputs = model([x, t, y], training=False)
    rep_I = outputs['rep_I']
    rep_C = outputs['rep_C']
    rep_A = outputs['rep_A']
    y_logits = outputs['y_logits']
    d_logit = outputs['d_logit']
    
    # 计算各项损失
    losses = {}
    
    # 调整变量损失 L_A
    ipm_A = model.compute_ipm_loss(rep_A, t)
    # 这里简化，实际应该包括预测损失
    losses['L_A'] = float(ipm_A)
    
    # 工具变量损失 L_I
    ipm_I = model.compute_instrumental_loss(rep_I, t, y)
    # 这里简化，实际应该包括预测损失
    losses['L_I'] = float(ipm_I)
    
    # 混淆变量平衡损失 L_C_B
    ipm_C = model.compute_ipm_loss(rep_C, t)
    losses['L_C_B'] = float(ipm_C)
    
    # 正交损失 L_O
    orthogonal_loss = model.compute_orthogonal_loss()
    losses['L_O'] = float(orthogonal_loss)
    
    # 事实损失
    factual_loss = model.compute_factual_loss(y_logits, y, t)
    losses['factual_loss'] = float(factual_loss)
    
    # 对抗损失
    adversarial_loss = tf.reduce_mean(
        tf.nn.sigmoid_cross_entropy_with_logits(labels=1-t, logits=d_logit)
    )
    losses['adversarial_loss'] = float(adversarial_loss)
    
    return losses

def print_evaluation_results(results, stage='stage1', epoch=None):
    """
    打印评估结果

    Args:
        results: 评估结果字典
        stage: 模型阶段
        epoch: 当前轮次
    """

    if epoch is not None:
        print(f"\n=== {stage.upper()} Evaluation Results (Epoch {epoch}) ===")
    else:
        print(f"\n=== {stage.upper()} Final Evaluation Results ===")

    print(f"PEHE: {results['pehe']:.6f}")
    print(f"ATE Error: {results['ate_error']:.6f}")
    print(f"MSE Y(0): {results['mse_y0']:.6f}")
    print(f"MSE Y(1): {results['mse_y1']:.6f}")
    print(f"Factual MSE: {results['factual_mse']:.6f}")
    print(f"True ATE: {results['true_ate']:.6f}")
    print(f"Predicted ATE: {results['pred_ate']:.6f}")

    # 打印y0和y1预测值的统计信息
    if 'y0_pred_stats' in results and 'y1_pred_stats' in results:
        print(f"\nY(0) Prediction Statistics:")
        print(f"  Mean: {results['y0_pred_stats']['mean']:.6f}")
        print(f"  Std:  {results['y0_pred_stats']['std']:.6f}")
        print(f"  Min:  {results['y0_pred_stats']['min']:.6f}")
        print(f"  Max:  {results['y0_pred_stats']['max']:.6f}")

        print(f"\nY(1) Prediction Statistics:")
        print(f"  Mean: {results['y1_pred_stats']['mean']:.6f}")
        print(f"  Std:  {results['y1_pred_stats']['std']:.6f}")
        print(f"  Min:  {results['y1_pred_stats']['min']:.6f}")
        print(f"  Max:  {results['y1_pred_stats']['max']:.6f}")

    print("=" * 50)

def save_evaluation_results(results, save_path, stage='stage1', epoch=None):
    """
    保存评估结果

    Args:
        results: 评估结果字典
        save_path: 保存路径
        stage: 模型阶段
        epoch: 当前轮次
    """
    
    import json
    import os
    
    # 创建保存目录
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    
    # 添加元信息
    results_with_meta = {
        'stage': stage,
        'epoch': epoch,
        'metrics': results
    }
    
    # 保存为JSON
    with open(save_path, 'w') as f:
        json.dump(results_with_meta, f, indent=2)
    
    print(f"Evaluation results saved to {save_path}")

def compare_models(results1, results2, model1_name='Model 1', model2_name='Model 2'):
    """
    比较两个模型的性能
    
    Args:
        results1, results2: 评估结果字典
        model1_name, model2_name: 模型名称
    
    Returns:
        比较结果字典
    """
    
    comparison = {}
    
    for metric in ['pehe', 'ate_error', 'mse_y0', 'mse_y1', 'factual_mse']:
        if metric in results1 and metric in results2:
            diff = results1[metric] - results2[metric]
            better = model2_name if diff > 0 else model1_name
            comparison[metric] = {
                f'{model1_name}': results1[metric],
                f'{model2_name}': results2[metric],
                'difference': diff,
                'better': better
            }
    
    return comparison

def print_comparison(comparison, model1_name='Model 1', model2_name='Model 2'):
    """
    打印模型比较结果
    
    Args:
        comparison: 比较结果字典
        model1_name, model2_name: 模型名称
    """
    
    print(f"\n=== Model Comparison: {model1_name} vs {model2_name} ===")
    
    for metric, values in comparison.items():
        print(f"\n{metric.upper()}:")
        print(f"  {model1_name}: {values[model1_name]:.6f}")
        print(f"  {model2_name}: {values[model2_name]:.6f}")
        print(f"  Difference: {values['difference']:.6f}")
        print(f"  Better: {values['better']}")
    
    print("=" * 50)

if __name__ == "__main__":
    # 测试评估函数
    print("Testing evaluation functions...")
    
    # 创建模拟数据
    n_samples = 100
    true_ite = np.random.normal(0, 1, n_samples)
    pred_ite = true_ite + np.random.normal(0, 0.1, n_samples)  # 添加一些噪声
    
    # 计算指标
    pehe_score = PEHE(true_ite, pred_ite)
    ate_error = ATE(true_ite, pred_ite)
    
    print(f"Test PEHE: {pehe_score:.6f}")
    print(f"Test ATE Error: {ate_error:.6f}")
    
    print("Evaluation functions test completed!")

"""
阶段一模型：DeR-CFR增强的表征分解 + GAN生成
结合DeR-CFR的损失函数约束和GAN的生成能力
"""

import tensorflow as tf
from tensorflow import keras
import numpy as np
from .networks import RepresentationNetwork, Generator, VGANITEDiscriminator

class Stage1Model(keras.Model):
    """阶段一模型：表征分解 + 初步生成"""
    
    def __init__(self, config, name='stage1_model'):
        super(Stage1Model, self).__init__(name=name)
        
        self.config = config
        
        # 表征网络（使用可配置的rep_dim和rep_layers）
        self.rep_I = RepresentationNetwork(
            input_dim=config.x_dim,
            output_dim=config.rep_dim,  # 使用config.rep_dim
            num_layers=config.rep_layers,  # 使用config.rep_layers
            hidden_dim=config.rep_dim,  # 锁定宽度 = rep_dim
            batch_norm=config.batch_norm,
            dropout_rate=config.dropout_rate,
            name='Instrumental'
        )

        self.rep_C = RepresentationNetwork(
            input_dim=config.x_dim,
            output_dim=config.rep_dim,  # 使用config.rep_dim
            num_layers=config.rep_layers,  # 使用config.rep_layers
            hidden_dim=config.rep_dim,  # 锁定宽度 = rep_dim
            batch_norm=config.batch_norm,
            dropout_rate=config.dropout_rate,
            name='Confounder'
        )

        self.rep_A = RepresentationNetwork(
            input_dim=config.x_dim,
            output_dim=config.rep_dim,  # 使用config.rep_dim
            num_layers=config.rep_layers,  # 使用config.rep_layers
            hidden_dim=config.rep_dim,  # 锁定宽度 = rep_dim
            batch_norm=config.batch_norm,
            dropout_rate=config.dropout_rate,
            name='Adjustment'
        )
        
        # 生成器：使用C和A表征生成潜在结果（完全按照VGANITE的Stage1 Generator设计）
        self.G_h1 = tf.keras.layers.Dense(units=config.h_dim * 4, name='stage1_G_h1')
        self.G_bn1 = tf.keras.layers.BatchNormalization(name='stage1_G_bn1')
        self.G_act1 = tf.keras.layers.LeakyReLU(alpha=0.2, name='stage1_G_act1')
        self.dropout7 = tf.keras.layers.Dropout(0.2, name='stage1_dropout7')
        self.G_h2 = tf.keras.layers.Dense(units=config.h_dim * 2, name='stage1_G_h2')
        self.G_bn2 = tf.keras.layers.BatchNormalization(name='stage1_G_bn2')
        self.G_act2 = tf.keras.layers.LeakyReLU(alpha=0.2, name='stage1_G_act2')
        self.G_h31 = tf.keras.layers.Dense(units=config.h_dim, name='stage1_G_h31')
        self.G_bn3 = tf.keras.layers.BatchNormalization(name='stage1_G_bn3')
        self.G_act3 = tf.keras.layers.LeakyReLU(alpha=0.2, name='stage1_G_act3')
        self.G_logit = tf.keras.layers.Dense(units=2, name='stage1_G_logit')  # 输出[Y(0), Y(1)]的logits
        
        # 判别器（VGANITE风格：判断"真假混合"的潜在结果对）
        self.discriminator = VGANITEDiscriminator(
            hidden_dim=config.h_dim,
            name='stage1_discriminator'
        )
        
        # 预测网络（DeR-CFR的关键组件：防止信息泄漏）
        # gI网络：从I表征预测Treatment (I→T)
        gI_layers = [keras.layers.Dense(config.gI_dim, activation='relu', name=f'gI_h{i}')
                     for i in range(config.gI_layers)]
        gI_layers.append(keras.layers.Dense(1, name='gI_logit'))  # 输出treatment logit
        self.gI = keras.Sequential(gI_layers, name='gI')

        # gA网络：从A表征预测Outcome (A→Y)
        gA_layers = [keras.layers.Dense(config.gA_dim, activation='relu', name=f'gA_h{i}')
                     for i in range(config.gA_layers)]
        gA_layers.append(keras.layers.Dense(1, name='gA_logit'))  # 输出outcome logit
        self.gA = keras.Sequential(gA_layers, name='gA')

        # 样本权重（关键：实现DeR-CFR的相互反馈机制）
        # 完全按照DeR-CFR原版：每个样本独立可学习的ωᵢ
        # 注意：这里先创建占位符，实际训练时会根据数据集大小重新初始化
        self.sample_weights = None  # 将在build_sample_weights中初始化
        self.n_samples = None  # 数据集大小

    def build_sample_weights(self, n_samples):
        """
        初始化样本权重（完全按照DeR-CFR原版）

        Args:
            n_samples: 训练集样本数量
        """
        self.n_samples = n_samples
        # 完全按照DeR-CFR原版：shape=[N, 1]
        self.sample_weights = self.add_weight(
            name='sample_weights',
            shape=(n_samples, 1),
            initializer='ones',
            trainable=True
        )

    def build(self, input_shape):
        """构建模型"""
        super(Stage1Model, self).build(input_shape)
    
    def call(self, inputs, training=None):
        """前向传播"""
        x, t, y = inputs
        
        # 学习三种表征
        rep_I = self.rep_I(x, training=training)
        rep_C = self.rep_C(x, training=training)
        rep_A = self.rep_A(x, training=training)
        
        # 生成潜在结果：使用C和A表征（完全按照VGANITE的Generator设计）
        generator_input = tf.concat([rep_C, rep_A], axis=1)

        # Stage1 Generator forward pass (与VGANITE完全一致)
        G_h1 = self.G_h1(generator_input)
        G_h1 = self.dropout7(G_h1, training=training)
        G_h1 = self.G_bn1(G_h1, training=training)
        G_h1 = self.G_act1(G_h1)

        G_h2 = self.G_h2(G_h1)
        G_h2 = self.G_bn2(G_h2, training=training)
        G_h2 = self.G_act2(G_h2)

        G_h31 = self.G_h31(G_h2)
        G_h31 = self.G_bn3(G_h31, training=training)
        G_h31 = self.G_act3(G_h31)

        y_logits = self.G_logit(G_h31)  # [Y(0), Y(1)] logits
        y_probs = tf.nn.sigmoid(y_logits)
        
        # 判别器输入（VGANITE风格：基于"真假混合"预测treatment）
        d_logit = self.discriminator(t, y, y_logits, training=training)

        # 预测网络：防止信息泄漏的关键约束
        t_pred_logit = self.gI(rep_I, training=training)  # I→T预测
        y_pred_logit = self.gA(rep_A, training=training)  # A→Y预测

        return {
            'rep_I': rep_I,
            'rep_C': rep_C,
            'rep_A': rep_A,
            'y_logits': y_logits,
            'y_probs': y_probs,
            'd_logit': d_logit,
            't_pred_logit': t_pred_logit,  # 新增：I→T预测
            'y_pred_logit': y_pred_logit   # 新增：A→Y预测
        }
    
    def compute_ipm_loss(self, rep, t, weights=None, p_t=0.5):
        """计算积分概率度量(IPM)损失"""
        # 分离处理组和控制组
        i0 = tf.where(t < 0.5)[:, 0]
        i1 = tf.where(t >= 0.5)[:, 0]
        
        rep_0 = tf.gather(rep, i0)
        rep_1 = tf.gather(rep, i1)
        
        # 计算加权均值（完全按照DeR-CFR原版）
        if weights is not None:
            weights_0 = tf.gather(weights, i0)  # [n0, 1]
            weights_1 = tf.gather(weights, i1)  # [n1, 1]

            # 修正：使用正确的加权均值公式 sum(ω*x)/sum(ω)
            mean_0 = tf.reduce_sum(weights_0 * rep_0, axis=0) / (tf.reduce_sum(weights_0) + 1e-8)
            mean_1 = tf.reduce_sum(weights_1 * rep_1, axis=0) / (tf.reduce_sum(weights_1) + 1e-8)
        else:
            mean_0 = tf.reduce_mean(rep_0, axis=0)
            mean_1 = tf.reduce_mean(rep_1, axis=0)
        
        # 计算IPM损失
        ipm_loss = tf.reduce_sum(tf.square(
            2.0 * p_t * mean_1 - 2.0 * (1.0 - p_t) * mean_0
        ))
        
        return ipm_loss
    
    def compute_instrumental_loss(self, rep_I, t, y, weights=None, p_t=0.5):
        """
        计算工具变量损失：L_I = β(IPM_I + ℓ(t, gI(I)))
        完全按照DeR-CFR原版：IPM损失 + I→T预测误差
        """
        # 1. IPM损失部分：I ⊥ Y | T
        # 分别在T=0和T=1条件下计算I和Y的独立性
        i_0_0 = tf.where((t < 0.5) & (y < 0.5))[:, 0]  # T=0, Y=0
        i_0_1 = tf.where((t < 0.5) & (y >= 0.5))[:, 0]  # T=0, Y=1
        i_1_0 = tf.where((t >= 0.5) & (y < 0.5))[:, 0]  # T=1, Y=0
        i_1_1 = tf.where((t >= 0.5) & (y >= 0.5))[:, 0]  # T=1, Y=1

        # 获取各组的表征
        I_0_0 = tf.gather(rep_I, i_0_0)
        I_0_1 = tf.gather(rep_I, i_0_1)
        I_1_0 = tf.gather(rep_I, i_1_0)
        I_1_1 = tf.gather(rep_I, i_1_1)

        # 计算加权均值（修正：使用正确的加权均值公式）
        if weights is not None:
            w_0_0 = tf.gather(weights, i_0_0)
            w_0_1 = tf.gather(weights, i_0_1)
            w_1_0 = tf.gather(weights, i_1_0)
            w_1_1 = tf.gather(weights, i_1_1)

            # 修正：使用 sum(ω*x)/sum(ω) 而非 reduce_mean(ω*x)
            mean_0_0 = tf.reduce_sum(w_0_0 * I_0_0, axis=0) / (tf.reduce_sum(w_0_0) + 1e-8)
            mean_0_1 = tf.reduce_sum(w_0_1 * I_0_1, axis=0) / (tf.reduce_sum(w_0_1) + 1e-8)
            mean_1_0 = tf.reduce_sum(w_1_0 * I_1_0, axis=0) / (tf.reduce_sum(w_1_0) + 1e-8)
            mean_1_1 = tf.reduce_sum(w_1_1 * I_1_1, axis=0) / (tf.reduce_sum(w_1_1) + 1e-8)
        else:
            mean_0_0 = tf.reduce_mean(I_0_0, axis=0)
            mean_0_1 = tf.reduce_mean(I_0_1, axis=0)
            mean_1_0 = tf.reduce_mean(I_1_0, axis=0)
            mean_1_1 = tf.reduce_mean(I_1_1, axis=0)

        # 计算条件独立性损失
        mmd_0 = tf.reduce_sum(tf.square(
            2.0 * p_t * mean_0_1 - 2.0 * (1.0 - p_t) * mean_0_0
        ))
        mmd_1 = tf.reduce_sum(tf.square(
            2.0 * p_t * mean_1_1 - 2.0 * (1.0 - p_t) * mean_1_0
        ))
        ipm_I = mmd_0 + mmd_1

        # 2. 预测误差部分：ℓ(t, gI(I))
        t_pred_logit = self.gI(rep_I, training=True)  # gI(I)
        ce_loss = tf.nn.sigmoid_cross_entropy_with_logits(
            labels=tf.cast(t, tf.float32), logits=t_pred_logit
        )

        # 加权预测误差
        if weights is not None:
            pred_loss = tf.reduce_sum(weights * ce_loss) / (tf.reduce_sum(weights) + 1e-8)
        else:
            pred_loss = tf.reduce_mean(ce_loss)

        # 完整的L_I损失
        return ipm_I + pred_loss
    
    def compute_orthogonal_loss(self):
        """
        计算正交正则化损失（完全按照DeR-CFR原版：权重矩阵连乘计算特征贡献度）
        """
        # 获取权重矩阵
        w_I = self.rep_I.get_weights_for_orthogonal_loss()
        w_C = self.rep_C.get_weights_for_orthogonal_loss()
        w_A = self.rep_A.get_weights_for_orthogonal_loss()

        # 按照DeR-CFR的ICA_W_setting方法：确定使用的层数
        if self.config.select_layer == 0:
            layer_num = len(w_I)  # 使用所有层
        else:
            layer_num = min(self.config.select_layer, len(w_I))  # 使用指定的层数

        # 初始化权重矩阵连乘（完全按照DeR-CFR原版）
        if layer_num > 0:
            w_I_sum = w_I[0]
            w_C_sum = w_C[0]
            w_A_sum = w_A[0]

            # 通过循环连乘权重矩阵（完全按照DeR-CFR原版）
            for i in range(1, layer_num):
                w_I_sum = tf.matmul(w_I_sum, w_I[i])
                w_C_sum = tf.matmul(w_C_sum, w_C[i])
                w_A_sum = tf.matmul(w_A_sum, w_A[i])
        else:
            # 如果没有权重，使用零矩阵
            w_I_sum = tf.zeros([1, 1])
            w_C_sum = tf.zeros([1, 1])
            w_A_sum = tf.zeros([1, 1])

        # 计算特征贡献度（完全按照DeR-CFR原版）
        w_I_mean = tf.reduce_mean(tf.abs(w_I_sum), axis=1)
        w_C_mean = tf.reduce_mean(tf.abs(w_C_sum), axis=1)
        w_A_mean = tf.reduce_mean(tf.abs(w_A_sum), axis=1)

        # 正交损失：最小化点积（完全按照DeR-CFR原版）
        orthogonal_loss = (
            tf.reduce_sum(w_I_mean * w_C_mean) +
            tf.reduce_sum(w_I_mean * w_A_mean) +
            tf.reduce_sum(w_C_mean * w_A_mean)
        )

        # 支撑损失：防止权重全为零（完全按照DeR-CFR原版）
        support_loss = (
            tf.square(tf.reduce_sum(w_I_mean) - 1.0) +
            tf.square(tf.reduce_sum(w_C_mean) - 1.0) +
            tf.square(tf.reduce_sum(w_A_mean) - 1.0)
        )

        return orthogonal_loss + support_loss

    def compute_adjustment_loss(self, rep_A, t, y, weights=None):
        """
        计算调整变量损失：L_A = α(IPM_A + ℓ(y, gA(A)))
        完全按照DeR-CFR原版：IPM损失 + A→Y预测误差
        """
        # 1. IPM损失部分
        ipm_A = self.compute_ipm_loss(rep_A, t, weights)

        # 2. 预测误差部分：ℓ(y, gA(A))
        y_pred_logit = self.gA(rep_A, training=True)  # gA(A)

        # 使用MSE损失（按照DeR-CFR原版）
        y_pred_prob = tf.nn.sigmoid(y_pred_logit)
        mse_loss = tf.square(y - y_pred_prob)

        # 加权预测误差
        if weights is not None:
            pred_loss = tf.reduce_sum(weights * mse_loss) / (tf.reduce_sum(weights) + 1e-8)
        else:
            pred_loss = tf.reduce_mean(mse_loss)

        # 完整的L_A损失
        return ipm_A + pred_loss
    
    def compute_factual_loss(self, y_logits, y_true, t, weights=None):
        """
        计算事实损失（关键：支持样本权重加权）
        这是实现相互反馈机制的核心：E[ω·∥Y^F−Y_F∥]
        """
        # 获取事实预测
        y_0_logit = y_logits[:, 0:1]
        y_1_logit = y_logits[:, 1:2]

        # 根据处理变量选择对应的预测
        y_factual_logit = t * y_1_logit + (1 - t) * y_0_logit

        # 计算每个样本的交叉熵损失（不立即求均值）
        sample_losses = tf.nn.sigmoid_cross_entropy_with_logits(
            labels=y_true, logits=y_factual_logit
        )

        # 关键：应用样本权重进行加权
        if weights is not None:
            # 确保权重维度匹配
            if len(weights.shape) == 1:
                weights = tf.expand_dims(weights, axis=1)

            # 修正：使用正确的加权损失公式 sum(ω*loss)/sum(ω)
            weighted_losses = weights * sample_losses
            factual_loss = tf.reduce_sum(weighted_losses) / (tf.reduce_sum(weights) + 1e-8)
        else:
            factual_loss = tf.reduce_mean(sample_losses)

        return factual_loss

    def get_sample_weights(self, batch_indices):
        """
        获取当前批次的样本权重（完全按照DeR-CFR原版）

        Args:
            batch_indices: 批次样本索引 [batch_size]

        Returns:
            weights: 样本权重 [batch_size, 1]
        """
        if self.sample_weights is None:
            raise ValueError("Sample weights not initialized. Call build_sample_weights first.")

        # 完全按照DeR-CFR原版：tf.gather(sample_weight, self.I)
        return tf.gather(self.sample_weights, batch_indices)
    
    def generator_forward(self, x, t, y, training=False):
        """
        只运行生成器的前向传播（用于判别器训练步骤）

        Args:
            x: 输入特征 [batch_size, x_dim]
            t: 处理变量 [batch_size, 1]
            y: 结果变量 [batch_size, 1]
            training: 是否训练模式

        Returns:
            y_logits: 生成器输出的潜在结果logits [batch_size, 2]
        """
        # 学习三种表征
        rep_I = self.rep_I(x, training=training)
        rep_C = self.rep_C(x, training=training)
        rep_A = self.rep_A(x, training=training)

        # 生成潜在结果：使用C和A表征（完全按照VGANITE的Generator设计）
        generator_input = tf.concat([rep_C, rep_A], axis=1)

        # Stage1 Generator forward pass (与VGANITE完全一致)
        G_h1 = self.G_h1(generator_input)
        G_h1 = self.dropout7(G_h1, training=training)
        G_h1 = self.G_bn1(G_h1, training=training)
        G_h1 = self.G_act1(G_h1)

        G_h2 = self.G_h2(G_h1)
        G_h2 = self.G_bn2(G_h2, training=training)
        G_h2 = self.G_act2(G_h2)

        G_h31 = self.G_h31(G_h2)
        G_h31 = self.G_bn3(G_h31, training=training)
        G_h31 = self.G_act3(G_h31)

        y_logits = self.G_logit(G_h31)

        return y_logits

    def get_representations(self, x, training=False):
        """获取表征（用于阶段二）"""
        rep_I = self.rep_I(x, training=training)
        rep_C = self.rep_C(x, training=training)
        rep_A = self.rep_A(x, training=training)

        return rep_I, rep_C, rep_A

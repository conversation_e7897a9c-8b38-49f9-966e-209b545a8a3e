"""
检查数据泄露和其他潜在错误
"""
import sys
import os
sys.path.append(os.path.dirname(__file__))

from data_loader import load_custom_dataset, preprocess_data
import numpy as np
import pandas as pd

print("=== 数据泄露检查 ===")

# 1. 检查原始数据
print("\n1. 原始数据检查")
data = pd.read_csv('./data/ihdp.csv')
print(f"总样本数: {len(data)}")
print(f"列数: {len(data.columns)}")
print("列名:", list(data.columns))

# 检查是否有重复样本
duplicates = data.duplicated().sum()
print(f"重复样本数: {duplicates}")

# 2. 检查数据加载过程
print("\n2. 数据加载过程检查")
x_cols = [5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29]
result = load_custom_dataset(
    data_path='./data/ihdp.csv',
    train_rate=0.8,
    file_format='csv',
    x_cols=x_cols,
    t_col=0,
    y0_col=3,
    y1_col=4,
    factual_y_col=1
)

train_x, train_t, train_y, train_potential_y, test_x, test_t, test_y, test_potential_y = result

print(f"训练集大小: {len(train_x)}")
print(f"测试集大小: {len(test_x)}")
print(f"总计: {len(train_x) + len(test_x)}")

# 3. 检查训练测试集是否有重叠
print("\n3. 训练测试集重叠检查")
# 将特征转换为字符串进行比较
train_x_str = [','.join(map(str, row)) for row in train_x]
test_x_str = [','.join(map(str, row)) for row in test_x]

overlap = set(train_x_str) & set(test_x_str)
print(f"训练测试集重叠样本数: {len(overlap)}")

if len(overlap) > 0:
    print("⚠️ 发现数据泄露：训练集和测试集有重叠样本！")
else:
    print("✅ 训练测试集无重叠")

# 4. 检查标准化过程
print("\n4. 标准化过程检查")
processed_data = preprocess_data(
    train_x, train_t, train_y, train_potential_y,
    test_x, test_t, test_y, test_potential_y,
    normalize_x=True,
    normalize_y=True,
    normalization_method='minmax'
)

proc_train_x, proc_train_t, proc_train_y, proc_train_potential_y, proc_test_x, proc_test_t, proc_test_y, proc_test_potential_y = processed_data

print("标准化后的数据范围:")
print(f"  训练集X: [{proc_train_x.min():.6f}, {proc_train_x.max():.6f}]")
print(f"  测试集X: [{proc_test_x.min():.6f}, {proc_test_x.max():.6f}]")
print(f"  训练集Y: [{proc_train_y.min():.6f}, {proc_train_y.max():.6f}]")
print(f"  测试集Y: [{proc_test_y.min():.6f}, {proc_test_y.max():.6f}]")

# 5. 检查是否使用了测试集信息进行标准化
print("\n5. 标准化参数检查")
# 重新计算标准化参数，确保只使用训练集
y_min_train = np.min(train_y)
y_max_train = np.max(train_y)
y_range_train = y_max_train - y_min_train

print(f"仅使用训练集的Y标准化参数:")
print(f"  Y_min: {y_min_train:.6f}")
print(f"  Y_max: {y_max_train:.6f}")
print(f"  Y_range: {y_range_train:.6f}")

# 检查测试集是否超出训练集范围
test_y_min = np.min(test_y)
test_y_max = np.max(test_y)
print(f"测试集Y范围: [{test_y_min:.6f}, {test_y_max:.6f}]")

if test_y_min < y_min_train or test_y_max > y_max_train:
    print("⚠️ 测试集Y值超出训练集范围，可能导致标准化问题")
else:
    print("✅ 测试集Y值在训练集范围内")

# 6. 检查潜在结果的一致性
print("\n6. 潜在结果一致性检查")
for i in range(min(5, len(train_t))):
    t = train_t[i]
    y = train_y[i]
    y0 = train_potential_y[i, 0]
    y1 = train_potential_y[i, 1]
    
    expected_y = t * y1 + (1-t) * y0
    diff = abs(y - expected_y)
    
    print(f"样本{i}: t={t:.0f}, y={y:.3f}, y0={y0:.3f}, y1={y1:.3f}, expected_y={expected_y:.3f}, diff={diff:.6f}")

# 7. 检查ATE计算
print("\n7. ATE计算检查")
true_ate_train = np.mean(train_potential_y[:, 1] - train_potential_y[:, 0])
true_ate_test = np.mean(test_potential_y[:, 1] - test_potential_y[:, 0])

print(f"训练集真实ATE: {true_ate_train:.6f}")
print(f"测试集真实ATE: {true_ate_test:.6f}")

# 标准化后的ATE
proc_true_ate_train = np.mean(proc_train_potential_y[:, 1] - proc_train_potential_y[:, 0])
proc_true_ate_test = np.mean(proc_test_potential_y[:, 1] - proc_test_potential_y[:, 0])

print(f"标准化后训练集ATE: {proc_true_ate_train:.6f}")
print(f"标准化后测试集ATE: {proc_true_ate_test:.6f}")

# 8. 检查特征分布
print("\n8. 特征分布检查")
print("训练集特征统计:")
print(f"  均值范围: [{np.mean(train_x, axis=0).min():.3f}, {np.mean(train_x, axis=0).max():.3f}]")
print(f"  标准差范围: [{np.std(train_x, axis=0).min():.3f}, {np.std(train_x, axis=0).max():.3f}]")

print("测试集特征统计:")
print(f"  均值范围: [{np.mean(test_x, axis=0).min():.3f}, {np.mean(test_x, axis=0).max():.3f}]")
print(f"  标准差范围: [{np.std(test_x, axis=0).min():.3f}, {np.std(test_x, axis=0).max():.3f}]")

# 9. 检查是否有异常简单的模式
print("\n9. 简单模式检查")
# 检查Y是否与某个特征高度相关
correlations = []
for i in range(train_x.shape[1]):
    corr = np.corrcoef(train_x[:, i], train_y.flatten())[0, 1]
    correlations.append(abs(corr))

max_corr = max(correlations)
max_corr_idx = correlations.index(max_corr)
print(f"Y与特征的最大相关性: {max_corr:.6f} (特征{max_corr_idx})")

if max_corr > 0.9:
    print("⚠️ 发现异常高的相关性，可能存在数据泄露")
else:
    print("✅ 特征与Y的相关性正常")

print("\n=== 检查总结 ===")
print("如果结果异常好，可能的原因:")
print("1. 数据泄露（训练测试集重叠）")
print("2. 标准化使用了测试集信息")
print("3. 模型过于简单，没有学到真正的因果关系")
print("4. 数据集本身就很容易预测")
print("5. 评估指标计算错误")

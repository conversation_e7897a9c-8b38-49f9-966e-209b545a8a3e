"""
测试Y标准化的影响
"""
import sys
import os
sys.path.append(os.path.dirname(__file__))

from data_loader import load_custom_dataset, preprocess_data
import numpy as np

print("=== 测试Y标准化的影响 ===")

# 加载原始数据
x_cols = [5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29]
result = load_custom_dataset(
    data_path='./data/ihdp.csv',
    train_rate=0.8,
    file_format='csv',
    x_cols=x_cols,
    t_col=0,
    y0_col=3,
    y1_col=4,
    factual_y_col=1
)

train_x, train_t, train_y, train_potential_y, test_x, test_t, test_y, test_potential_y = result

print("原始数据范围:")
print(f"  训练集Y范围: {train_y.min():.3f} 到 {train_y.max():.3f}")
print(f"  训练集Y(0)范围: {train_potential_y[:, 0].min():.3f} 到 {train_potential_y[:, 0].max():.3f}")
print(f"  训练集Y(1)范围: {train_potential_y[:, 1].min():.3f} 到 {train_potential_y[:, 1].max():.3f}")
print(f"  训练集真实ATE: {np.mean(train_potential_y[:, 1] - train_potential_y[:, 0]):.6f}")

# 测试不标准化Y
print("\n=== 不标准化Y (当前设置) ===")
processed_data_no_norm = preprocess_data(
    train_x, train_t, train_y, train_potential_y,
    test_x, test_t, test_y, test_potential_y,
    normalize_x=True,
    normalize_y=False
)

_, _, proc_train_y_no, proc_train_potential_y_no, _, _, proc_test_y_no, proc_test_potential_y_no = processed_data_no_norm

print(f"  处理后Y范围: {proc_train_y_no.min():.3f} 到 {proc_train_y_no.max():.3f}")
print(f"  处理后真实ATE: {np.mean(proc_train_potential_y_no[:, 1] - proc_train_potential_y_no[:, 0]):.6f}")

# 测试标准化Y
print("\n=== 标准化Y ===")
processed_data_norm = preprocess_data(
    train_x, train_t, train_y, train_potential_y,
    test_x, test_t, test_y, test_potential_y,
    normalize_x=True,
    normalize_y=True
)

_, _, proc_train_y_norm, proc_train_potential_y_norm, _, _, proc_test_y_norm, proc_test_potential_y_norm = processed_data_norm

print(f"  处理后Y范围: {proc_train_y_norm.min():.3f} 到 {proc_train_y_norm.max():.3f}")
print(f"  处理后Y均值: {np.mean(proc_train_y_norm):.6f}")
print(f"  处理后Y标准差: {np.std(proc_train_y_norm):.6f}")
print(f"  处理后真实ATE: {np.mean(proc_train_potential_y_norm[:, 1] - proc_train_potential_y_norm[:, 0]):.6f}")

# 计算标准化参数
y_mean = np.mean(train_y)
y_std = np.std(train_y) + 1e-8
print(f"\n标准化参数:")
print(f"  Y均值: {y_mean:.6f}")
print(f"  Y标准差: {y_std:.6f}")

print(f"\n=== 结论 ===")
print("如果使用Y标准化:")
print("  - 模型预测范围应该在[-3, 3]左右（标准化后的合理范围）")
print("  - 需要在评估时反标准化才能得到真实尺度的结果")
print("  - ATE也会被标准化，需要乘以y_std来恢复真实尺度")

print("\n如果不使用Y标准化:")
print("  - 模型需要预测[0, 13]范围的值")
print("  - 但sigmoid输出只能在[0, 1]范围")
print("  - 这可能是ATE预测偏差的主要原因")

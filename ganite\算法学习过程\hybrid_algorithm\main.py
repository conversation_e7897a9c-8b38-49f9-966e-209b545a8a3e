"""
主训练脚本：融合算法的完整训练流程
"""

import os
import sys
import numpy as np
import tensorflow as tf
from datetime import datetime
import json

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

from config import get_config
from data_loader import load_data, preprocess_data
from stage1_trainer import Stage1Trainer
from stage2_trainer import Stage2Trainer
from evaluation import evaluate_model, print_evaluation_results, save_evaluation_results

def setup_environment(config):
    """设置环境"""
    # 设置随机种子
    tf.random.set_seed(config.seed)
    np.random.seed(config.seed)
    
    # 设置GPU
    if config.device == 'cuda':
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if gpus:
            try:
                for gpu in gpus:
                    tf.config.experimental.set_memory_growth(gpu, True)
                print(f"Using GPU: {len(gpus)} device(s) found")
            except RuntimeError as e:
                print(f"GPU setup error: {e}")
        else:
            print("No GPU found, using CPU")
            config.device = 'cpu'
    
    # 创建输出目录
    config.create_output_dir()
    
    print(f"Environment setup completed. Device: {config.device}")

def load_and_preprocess_data(config):
    """加载和预处理数据"""
    print("Loading data...")
    
    # 加载数据
    if config.data_name == 'custom':
        # 处理自定义数据集的列索引
        x_cols = None
        if config.x_cols is not None:
            x_cols = [int(x.strip()) for x in config.x_cols.split(',')]

        data = load_data(
            data_name=config.data_name,
            train_rate=0.8,
            data_path=config.data_path,
            file_format=config.file_format,
            x_cols=x_cols,
            t_col=config.t_col,
            y0_col=config.y0_col,
            y1_col=config.y1_col,
            factual_y_col=config.factual_y_col
        )
    else:
        data = load_data(
            data_name=config.data_name,
            train_rate=0.8
        )
    
    train_x, train_t, train_y, train_potential_y, test_x, test_t, test_y, test_potential_y = data
    
    # 预处理数据
    processed_data = preprocess_data(
        train_x, train_t, train_y, train_potential_y,
        test_x, test_t, test_y, test_potential_y,
        normalize_x=True,
        normalize_y=True,  # 启用Y标准化以解决尺度不匹配问题
        normalization_method='minmax'  # 使用Min-Max标准化
    )
    
    train_x, train_t, train_y, train_potential_y, test_x, test_t, test_y, test_potential_y = processed_data
    
    # 更新配置中的维度信息
    config.x_dim = train_x.shape[1]
    
    print(f"Data loaded successfully!")
    print(f"  Training samples: {len(train_x)}")
    print(f"  Test samples: {len(test_x)}")
    print(f"  Feature dimension: {config.x_dim}")
    print(f"  Treatment ratio: {np.mean(train_t):.3f}")
    print(f"  Outcome ratio: {np.mean(train_y):.3f}")
    
    return (train_x, train_t, train_y, train_potential_y), (test_x, test_t, test_y, test_potential_y)

def train_stage1(config, train_data, test_data):
    """训练阶段一模型"""
    print("\n" + "="*50)
    print("STAGE 1: DeR-CFR Enhanced Representation Learning + GAN")
    print("="*50)
    
    # 创建阶段一训练器
    stage1_trainer = Stage1Trainer(config)
    
    # 训练
    stage1_history = stage1_trainer.train(train_data, test_data)
    
    # 最终评估
    train_x, train_t, train_y, train_potential_y = train_data
    test_x, test_t, test_y, test_potential_y = test_data
    
    final_results = evaluate_model(
        stage1_trainer.model, test_x, test_t, test_y, test_potential_y, stage='stage1'
    )
    
    print_evaluation_results(final_results, stage='stage1')
    
    # 保存结果
    save_path = os.path.join(config.output_dir, 'stage1_final_results.json')
    save_evaluation_results(final_results, save_path, stage='stage1')
    
    return stage1_trainer, stage1_history, final_results

def generate_stage1_outputs(stage1_trainer, train_data, test_data):
    """
    生成阶段一的输出供阶段二使用
    完全按照VGANITE的wgangp.py第461-470行
    """
    print("\nGenerating Stage 1 outputs for Stage 2...")

    train_x, train_t, train_y, train_potential_y = train_data
    test_x, test_t, test_y, test_potential_y = test_data

    # 完全按照VGANITE的做法（wgangp.py第461-466行）
    x_train_tf = tf.cast(train_x, tf.float32)
    t_train_tf = tf.cast(train_t, tf.float32)
    y_train_tf = tf.cast(train_y, tf.float32)

    # 获取阶段一的输出logits
    train_outputs = stage1_trainer.model([x_train_tf, t_train_tf, y_train_tf], training=False)
    y_bar_logits_from_stage1 = train_outputs['y_logits']

    # 使用概率作为目标分布（与VGANITE完全一致）
    y_bar_prob_target = tf.nn.sigmoid(y_bar_logits_from_stage1)

    # 同样处理测试集
    x_test_tf = tf.cast(test_x, tf.float32)
    t_test_tf = tf.cast(test_t, tf.float32)
    y_test_tf = tf.cast(test_y, tf.float32)

    test_outputs = stage1_trainer.model([x_test_tf, t_test_tf, y_test_tf], training=False)
    test_y_bar_logits = test_outputs['y_logits']
    test_y_bar_prob = tf.nn.sigmoid(test_y_bar_logits)

    print(f"Stage 1 outputs generated:")
    print(f"  Train y_bar_prob shape: {y_bar_prob_target.shape}")
    print(f"  Test y_bar_prob shape: {test_y_bar_prob.shape}")

    # 返回处理好的数据，供阶段二直接使用
    return {
        'train_x': x_train_tf,
        'train_y_bar_prob': y_bar_prob_target,
        'test_x': x_test_tf,
        'test_y_bar_prob': test_y_bar_prob
    }

def train_stage2(config, stage1_trainer, stage1_outputs):
    """训练阶段二模型"""
    print("\n" + "="*50)
    print("STAGE 2: WGAN-GP Refinement")
    print("="*50)

    # 创建阶段二训练器
    stage2_trainer = Stage2Trainer(config, stage1_trainer.model)

    # 使用阶段一的输出进行真正的阶段二训练
    stage2_history = stage2_trainer.train_with_stage1_outputs(stage1_outputs)

    # 保存模型
    save_path = os.path.join(config.output_dir, 'models', 'stage2_final_model')
    stage2_trainer.save_model(save_path)

    return stage2_trainer, stage2_history

def final_evaluation(config, stage1_trainer, stage2_trainer, train_data, test_data, train_y_stage1, test_y_stage1):
    """最终评估 - 同时评估训练集和测试集"""
    print("\n" + "="*50)
    print("FINAL EVALUATION ON BOTH TRAIN AND TEST SETS")
    print("="*50)

    train_x, train_t, train_y, train_potential_y = train_data
    test_x, test_t, test_y, test_potential_y = test_data

    # 阶段一结果 - 训练集
    stage1_train_results = evaluate_model(
        stage1_trainer.model, train_x, train_t, train_y, train_potential_y, stage='stage1'
    )

    # 阶段一结果 - 测试集
    stage1_test_results = evaluate_model(
        stage1_trainer.model, test_x, test_t, test_y, test_potential_y, stage='stage1'
    )
    
    # 导入评估函数
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'vganite'))
    try:
        from metrics import PEHE, ATE
    except ImportError:
        def PEHE(y_true, y_pred):
            return np.sqrt(np.mean((y_true - y_pred) ** 2))
        def ATE(y_true, y_pred):
            return np.abs(np.mean(y_true) - np.mean(y_pred))

    # 阶段二结果 - 训练集
    train_y_stage2_probs = stage2_trainer.model.generate_refined_outcomes(
        train_x, train_y_stage1, training=False
    ).numpy()

    # 计算阶段二的评估指标 - 训练集
    train_true_ite = train_potential_y[:, 1] - train_potential_y[:, 0]
    train_pred_ite_stage2 = train_y_stage2_probs[:, 1] - train_y_stage2_probs[:, 0]

    # 计算事实MSE - 训练集
    train_t_flat = train_t.flatten()
    train_y_flat = train_y.flatten()
    train_factual_pred_stage2 = train_t_flat * train_y_stage2_probs[:, 1] + (1 - train_t_flat) * train_y_stage2_probs[:, 0]
    train_factual_mse_stage2 = float(np.mean((train_y_flat - train_factual_pred_stage2) ** 2))

    stage2_train_results = {
        'pehe': float(PEHE(train_potential_y, train_y_stage2_probs)),
        'ate_error': float(ATE(train_potential_y, train_y_stage2_probs)),
        'mse_y0': float(np.mean((train_potential_y[:, 0] - train_y_stage2_probs[:, 0]) ** 2)),
        'mse_y1': float(np.mean((train_potential_y[:, 1] - train_y_stage2_probs[:, 1]) ** 2)),
        'factual_mse': train_factual_mse_stage2,
        'true_ate': float(np.mean(train_true_ite)),
        'pred_ate': float(np.mean(train_pred_ite_stage2))
    }

    # 阶段二结果 - 测试集
    test_y_stage2_probs = stage2_trainer.model.generate_refined_outcomes(
        test_x, test_y_stage1, training=False
    ).numpy()

    # 计算阶段二的评估指标 - 测试集
    test_true_ite = test_potential_y[:, 1] - test_potential_y[:, 0]
    test_pred_ite_stage2 = test_y_stage2_probs[:, 1] - test_y_stage2_probs[:, 0]

    # 计算事实MSE - 测试集
    test_t_flat = test_t.flatten()
    test_y_flat = test_y.flatten()
    test_factual_pred_stage2 = test_t_flat * test_y_stage2_probs[:, 1] + (1 - test_t_flat) * test_y_stage2_probs[:, 0]
    test_factual_mse_stage2 = float(np.mean((test_y_flat - test_factual_pred_stage2) ** 2))

    stage2_test_results = {
        'pehe': float(PEHE(test_potential_y, test_y_stage2_probs)),
        'ate_error': float(ATE(test_potential_y, test_y_stage2_probs)),
        'mse_y0': float(np.mean((test_potential_y[:, 0] - test_y_stage2_probs[:, 0]) ** 2)),
        'mse_y1': float(np.mean((test_potential_y[:, 1] - test_y_stage2_probs[:, 1]) ** 2)),
        'factual_mse': test_factual_mse_stage2,
        'true_ate': float(np.mean(test_true_ite)),
        'pred_ate': float(np.mean(test_pred_ite_stage2))
    }
    
    # 打印结果
    print("\n" + "="*60)
    print("STAGE 1 RESULTS")
    print("="*60)
    print("\n--- TRAINING SET ---")
    print_evaluation_results(stage1_train_results, stage='stage1')
    print("\n--- TEST SET ---")
    print_evaluation_results(stage1_test_results, stage='stage1')

    print("\n" + "="*60)
    print("STAGE 2 RESULTS")
    print("="*60)
    print("\n--- TRAINING SET ---")
    print_evaluation_results(stage2_train_results, stage='stage2')
    print("\n--- TEST SET ---")
    print_evaluation_results(stage2_test_results, stage='stage2')

    # 比较结果 - 测试集
    print("\n" + "="*60)
    print("IMPROVEMENT FROM STAGE 1 TO STAGE 2 (TEST SET):")
    print("="*60)
    print(f"PEHE: {stage1_test_results['pehe']:.6f} -> {stage2_test_results['pehe']:.6f} "
          f"({stage2_test_results['pehe'] - stage1_test_results['pehe']:+.6f})")
    print(f"ATE Error: {stage1_test_results['ate_error']:.6f} -> {stage2_test_results['ate_error']:.6f} "
          f"({stage2_test_results['ate_error'] - stage1_test_results['ate_error']:+.6f})")

    # 比较结果 - 训练集
    print("\nIMPROVEMENT FROM STAGE 1 TO STAGE 2 (TRAIN SET):")
    print(f"PEHE: {stage1_train_results['pehe']:.6f} -> {stage2_train_results['pehe']:.6f} "
          f"({stage2_train_results['pehe'] - stage1_train_results['pehe']:+.6f})")
    print(f"ATE Error: {stage1_train_results['ate_error']:.6f} -> {stage2_train_results['ate_error']:.6f} "
          f"({stage2_train_results['ate_error'] - stage1_train_results['ate_error']:+.6f})")

    # 保存结果
    save_evaluation_results(stage1_train_results,
                          os.path.join(config.output_dir, 'final_stage1_train_results.json'),
                          stage='stage1')
    save_evaluation_results(stage1_test_results,
                          os.path.join(config.output_dir, 'final_stage1_test_results.json'),
                          stage='stage1')
    save_evaluation_results(stage2_train_results,
                          os.path.join(config.output_dir, 'final_stage2_train_results.json'),
                          stage='stage2')
    save_evaluation_results(stage2_test_results,
                          os.path.join(config.output_dir, 'final_stage2_test_results.json'),
                          stage='stage2')

    return {
        'stage1_train': stage1_train_results,
        'stage1_test': stage1_test_results,
        'stage2_train': stage2_train_results,
        'stage2_test': stage2_test_results
    }

def save_training_summary(config, stage1_history, stage2_history, final_results):
    """保存训练总结"""
    summary = {
        'config': vars(config),
        'stage1_history': stage1_history,
        'stage2_history': stage2_history,
        'final_results': final_results,
        'timestamp': datetime.now().isoformat()
    }
    
    summary_path = os.path.join(config.output_dir, 'training_summary.json')
    with open(summary_path, 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"\nTraining summary saved to {summary_path}")

def main():
    """主函数"""
    print("="*60)
    print("HYBRID ALGORITHM: DeR-CFR + VGANITE")
    print("="*60)
    
    # 获取配置
    config = get_config()
    print(config)
    
    # 设置环境
    setup_environment(config)
    
    # 加载数据
    train_data, test_data = load_and_preprocess_data(config)
    
    # 阶段一训练
    stage1_trainer, stage1_history, stage1_results = train_stage1(config, train_data, test_data)
    
    # 生成阶段一输出
    stage1_outputs = generate_stage1_outputs(stage1_trainer, train_data, test_data)

    # 阶段二训练
    stage2_trainer, stage2_history = train_stage2(config, stage1_trainer, stage1_outputs)
    
    # 最终评估
    final_results = final_evaluation(
        config, stage1_trainer, stage2_trainer, train_data, test_data,
        stage1_outputs['train_y_bar_prob'], stage1_outputs['test_y_bar_prob']
    )

    # 保存训练总结
    save_training_summary(config, stage1_history, stage2_history, final_results)
    
    print("\n" + "="*60)
    print("TRAINING COMPLETED SUCCESSFULLY!")
    print("="*60)

if __name__ == "__main__":
    main()

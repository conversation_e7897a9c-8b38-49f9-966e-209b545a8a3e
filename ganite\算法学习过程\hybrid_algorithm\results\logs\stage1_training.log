2025-06-12 16:38:40,650 - INFO - Starting Stage 1 training...
2025-06-12 16:38:40,650 - INFO - Training samples: 9120
2025-06-12 16:38:40,650 - INFO - Test samples: 2280
2025-06-12 16:43:21,388 - INFO - Starting Stage 1 training...
2025-06-12 16:43:21,388 - INFO - Training samples: 9120
2025-06-12 16:43:21,388 - INFO - Test samples: 2280
2025-06-12 16:54:38,925 - INFO - Starting Stage 1 training...
2025-06-12 16:54:38,935 - INFO - Training samples: 9120
2025-06-12 16:54:38,951 - INFO - Test samples: 2280
2025-06-12 17:03:04,043 - INFO - Starting Stage 1 training...
2025-06-12 17:03:04,052 - INFO - Training samples: 9120
2025-06-12 17:03:04,052 - INFO - Test samples: 2280
2025-06-12 18:37:21,414 - INFO - Starting Stage 1 training...
2025-06-12 18:37:21,414 - INFO - Training samples: 9120
2025-06-12 18:37:21,414 - INFO - Test samples: 2280
2025-06-12 23:11:49,516 - INFO - Starting Stage 1 training...
2025-06-12 23:11:49,516 - INFO - Training samples: 9120
2025-06-12 23:11:49,516 - INFO - Test samples: 2280
2025-06-12 23:14:15,699 - INFO - Starting Stage 1 training...
2025-06-12 23:14:15,699 - INFO - Training samples: 9120
2025-06-12 23:14:15,699 - INFO - Test samples: 2280
2025-06-12 23:14:26,156 - INFO - Epoch 0:
2025-06-12 23:14:26,157 - INFO -   Total Loss: 12.606366
2025-06-12 23:14:26,157 - INFO -   PEHE: 0.322759
2025-06-12 23:14:26,157 - INFO -   ATE Error: 0.001136
2025-06-12 23:14:26,233 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-12 23:14:26,296 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-12 23:14:55,845 - INFO - Epoch 20:
2025-06-12 23:14:55,845 - INFO -   Total Loss: 1.225509
2025-06-12 23:14:55,846 - INFO -   PEHE: 0.321376
2025-06-12 23:14:55,846 - INFO -   ATE Error: 0.013372
2025-06-12 23:14:55,925 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-12 23:15:23,997 - INFO - Epoch 40:
2025-06-12 23:15:23,998 - INFO -   Total Loss: 1.338560
2025-06-12 23:15:23,998 - INFO -   PEHE: 0.320238
2025-06-12 23:15:23,999 - INFO -   ATE Error: 0.009144
2025-06-12 23:15:24,084 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-12 23:15:40,222 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-12 23:15:53,574 - INFO - Epoch 60:
2025-06-12 23:15:53,575 - INFO -   Total Loss: 1.379468
2025-06-12 23:15:53,575 - INFO -   PEHE: 0.319814
2025-06-12 23:15:53,576 - INFO -   ATE Error: 0.008134
2025-06-12 23:15:53,640 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-12 23:16:23,188 - INFO - Epoch 80:
2025-06-12 23:16:23,189 - INFO -   Total Loss: 1.421550
2025-06-12 23:16:23,191 - INFO -   PEHE: 0.319778
2025-06-12 23:16:23,191 - INFO -   ATE Error: 0.010499
2025-06-12 23:16:23,242 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-12 23:16:50,815 - INFO - Stage 1 training completed!
2025-06-12 23:30:15,282 - INFO - Starting Stage 1 training...
2025-06-12 23:30:15,283 - INFO - Training samples: 9120
2025-06-12 23:30:15,283 - INFO - Test samples: 2280
2025-06-12 23:30:26,067 - INFO - Epoch 0:
2025-06-12 23:30:26,067 - INFO -   Total Loss: 12.606366
2025-06-12 23:30:26,067 - INFO -   PEHE: 0.322760
2025-06-12 23:30:26,068 - INFO -   ATE Error: 0.001102
2025-06-12 23:30:26,145 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-12 23:30:26,204 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-12 23:30:57,497 - INFO - Epoch 20:
2025-06-12 23:30:57,498 - INFO -   Total Loss: 1.230637
2025-06-12 23:30:57,498 - INFO -   PEHE: 0.322409
2025-06-12 23:30:57,499 - INFO -   ATE Error: 0.015852
2025-06-12 23:30:57,555 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-12 23:31:30,068 - INFO - Epoch 40:
2025-06-12 23:31:30,069 - INFO -   Total Loss: 1.338786
2025-06-12 23:31:30,070 - INFO -   PEHE: 0.320027
2025-06-12 23:31:30,070 - INFO -   ATE Error: 0.007490
2025-06-12 23:31:30,134 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-12 23:31:46,080 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-12 23:32:00,187 - INFO - Epoch 60:
2025-06-12 23:32:00,188 - INFO -   Total Loss: 1.379118
2025-06-12 23:32:00,188 - INFO -   PEHE: 0.319207
2025-06-12 23:32:00,188 - INFO -   ATE Error: 0.008597
2025-06-12 23:32:00,259 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-12 23:32:28,724 - INFO - Epoch 80:
2025-06-12 23:32:28,724 - INFO -   Total Loss: 1.414601
2025-06-12 23:32:28,725 - INFO -   PEHE: 0.319839
2025-06-12 23:32:28,725 - INFO -   ATE Error: 0.011013
2025-06-12 23:32:54,454 - INFO - Stage 1 training completed!
2025-06-12 23:32:54,657 - INFO - Stage 2 training skipped for quick test
2025-06-12 23:36:53,082 - INFO - Starting Stage 1 training...
2025-06-12 23:36:53,082 - INFO - Training samples: 9120
2025-06-12 23:36:53,082 - INFO - Test samples: 2280
2025-06-12 23:37:04,510 - INFO - Epoch 0:
2025-06-12 23:37:04,511 - INFO -   Total Loss: 12.606367
2025-06-12 23:37:04,511 - INFO -   PEHE: 0.322759
2025-06-12 23:37:04,512 - INFO -   ATE Error: 0.001136
2025-06-12 23:37:04,581 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-12 23:37:04,632 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-12 23:37:36,304 - INFO - Epoch 20:
2025-06-12 23:37:36,304 - INFO -   Total Loss: 1.228007
2025-06-12 23:37:36,304 - INFO -   PEHE: 0.320797
2025-06-12 23:37:36,304 - INFO -   ATE Error: 0.010869
2025-06-12 23:37:36,361 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-12 23:38:09,339 - INFO - Epoch 40:
2025-06-12 23:38:09,340 - INFO -   Total Loss: 1.345219
2025-06-12 23:38:09,340 - INFO -   PEHE: 0.319845
2025-06-12 23:38:09,340 - INFO -   ATE Error: 0.008389
2025-06-12 23:38:09,410 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-12 23:38:26,346 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-12 23:38:41,822 - INFO - Epoch 60:
2025-06-12 23:38:41,822 - INFO -   Total Loss: 1.387337
2025-06-12 23:38:41,823 - INFO -   PEHE: 0.320042
2025-06-12 23:38:41,823 - INFO -   ATE Error: 0.008964
2025-06-12 23:39:13,198 - INFO - Epoch 80:
2025-06-12 23:39:13,199 - INFO -   Total Loss: 1.415184
2025-06-12 23:39:13,199 - INFO -   PEHE: 0.319874
2025-06-12 23:39:13,199 - INFO -   ATE Error: 0.011477
2025-06-12 23:39:45,371 - INFO - Stage 1 training completed!
2025-06-12 23:39:45,637 - INFO - Starting Stage 2 training...
2025-06-12 23:39:45,638 - INFO - Training samples: 9120
2025-06-12 23:39:45,639 - INFO - Test samples: 2280
2025-06-12 23:48:12,361 - INFO - Starting Stage 1 training...
2025-06-12 23:48:12,362 - INFO - Training samples: 9120
2025-06-12 23:48:12,363 - INFO - Test samples: 2280
2025-06-12 23:48:23,666 - INFO - Epoch 0:
2025-06-12 23:48:23,667 - INFO -   Total Loss: 12.606367
2025-06-12 23:48:23,667 - INFO -   PEHE: 0.322760
2025-06-12 23:48:23,667 - INFO -   ATE Error: 0.001135
2025-06-12 23:48:23,740 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-12 23:48:23,792 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-12 23:48:54,298 - INFO - Epoch 20:
2025-06-12 23:48:54,298 - INFO -   Total Loss: 1.229041
2025-06-12 23:48:54,298 - INFO -   PEHE: 0.321673
2025-06-12 23:48:54,299 - INFO -   ATE Error: 0.013694
2025-06-12 23:48:54,370 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-12 23:49:27,483 - INFO - Epoch 40:
2025-06-12 23:49:27,483 - INFO -   Total Loss: 1.344225
2025-06-12 23:49:27,484 - INFO -   PEHE: 0.320650
2025-06-12 23:49:27,484 - INFO -   ATE Error: 0.005856
2025-06-12 23:49:27,554 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-12 23:49:45,001 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-12 23:50:01,954 - INFO - Epoch 60:
2025-06-12 23:50:01,954 - INFO -   Total Loss: 1.395115
2025-06-12 23:50:01,955 - INFO -   PEHE: 0.319672
2025-06-12 23:50:01,955 - INFO -   ATE Error: 0.009522
2025-06-12 23:50:02,038 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-12 23:50:35,288 - INFO - Epoch 80:
2025-06-12 23:50:35,289 - INFO -   Total Loss: 1.418002
2025-06-12 23:50:35,289 - INFO -   PEHE: 0.319758
2025-06-12 23:50:35,290 - INFO -   ATE Error: 0.011937
2025-06-12 23:51:08,163 - INFO - Stage 1 training completed!
2025-06-12 23:51:08,386 - INFO - Starting Stage 2 training...
2025-06-12 23:51:08,386 - INFO - Training samples: 9120
2025-06-12 23:51:08,387 - INFO - Test samples: 2280
2025-06-12 23:51:08,387 - INFO - Stage 2 training skipped for quick test
2025-06-12 23:51:08,388 - INFO - Stage2 model save skipped for quick test
2025-06-12 23:52:46,902 - INFO - Starting Stage 1 training...
2025-06-12 23:52:46,902 - INFO - Training samples: 9120
2025-06-12 23:52:46,902 - INFO - Test samples: 2280
2025-06-12 23:52:56,692 - INFO - Epoch 0:
2025-06-12 23:52:56,692 - INFO -   Total Loss: 12.606365
2025-06-12 23:52:56,692 - INFO -   PEHE: 0.322759
2025-06-12 23:52:56,692 - INFO -   ATE Error: 0.001108
2025-06-12 23:52:56,753 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-12 23:52:56,832 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-12 23:53:29,216 - INFO - Epoch 20:
2025-06-12 23:53:29,217 - INFO -   Total Loss: 1.229108
2025-06-12 23:53:29,217 - INFO -   PEHE: 0.320524
2025-06-12 23:53:29,218 - INFO -   ATE Error: 0.008670
2025-06-12 23:53:29,278 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-12 23:53:56,421 - INFO - Epoch 40:
2025-06-12 23:53:56,421 - INFO -   Total Loss: 1.336211
2025-06-12 23:53:56,421 - INFO -   PEHE: 0.320979
2025-06-12 23:53:56,421 - INFO -   ATE Error: 0.008098
2025-06-12 23:54:12,034 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-12 23:54:28,703 - INFO - Epoch 60:
2025-06-12 23:54:28,703 - INFO -   Total Loss: 1.387335
2025-06-12 23:54:28,703 - INFO -   PEHE: 0.320834
2025-06-12 23:54:28,703 - INFO -   ATE Error: 0.007993
2025-06-12 23:55:00,834 - INFO - Epoch 80:
2025-06-12 23:55:00,834 - INFO -   Total Loss: 1.412147
2025-06-12 23:55:00,834 - INFO -   PEHE: 0.319886
2025-06-12 23:55:00,834 - INFO -   ATE Error: 0.009619
2025-06-12 23:55:00,898 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-12 23:55:31,572 - INFO - Stage 1 training completed!
2025-06-12 23:55:31,823 - INFO - Starting Stage 2 training...
2025-06-12 23:55:31,823 - INFO - Training samples: 9120
2025-06-12 23:55:31,823 - INFO - Test samples: 2280
2025-06-12 23:55:31,823 - INFO - Stage 2 training skipped for quick test
2025-06-12 23:55:31,823 - INFO - Stage2 model save skipped for quick test
2025-06-16 13:22:43,056 - INFO - Starting Stage 1 training...
2025-06-16 13:22:43,056 - INFO - Training samples: 9120
2025-06-16 13:22:43,056 - INFO - Test samples: 2280
2025-06-16 13:22:54,057 - INFO - Epoch 0:
2025-06-16 13:22:54,057 - INFO -   Total Loss: 12.606367
2025-06-16 13:22:54,072 - INFO -   PEHE: 0.322759
2025-06-16 13:22:54,073 - INFO -   ATE Error: 0.001106
2025-06-16 13:22:54,142 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 13:22:54,200 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-16 13:23:27,378 - INFO - Epoch 20:
2025-06-16 13:23:27,378 - INFO -   Total Loss: 1.230713
2025-06-16 13:23:27,378 - INFO -   PEHE: 0.320442
2025-06-16 13:23:27,378 - INFO -   ATE Error: 0.011817
2025-06-16 13:23:27,444 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 13:24:00,088 - INFO - Epoch 40:
2025-06-16 13:24:00,088 - INFO -   Total Loss: 1.345961
2025-06-16 13:24:00,088 - INFO -   PEHE: 0.319806
2025-06-16 13:24:00,088 - INFO -   ATE Error: 0.004864
2025-06-16 13:24:00,151 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 13:24:16,740 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-16 13:24:33,491 - INFO - Epoch 60:
2025-06-16 13:24:33,491 - INFO -   Total Loss: 1.384724
2025-06-16 13:24:33,491 - INFO -   PEHE: 0.319481
2025-06-16 13:24:33,491 - INFO -   ATE Error: 0.008232
2025-06-16 13:24:33,554 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 13:25:06,904 - INFO - Epoch 80:
2025-06-16 13:25:06,904 - INFO -   Total Loss: 1.412968
2025-06-16 13:25:06,904 - INFO -   PEHE: 0.319309
2025-06-16 13:25:06,904 - INFO -   ATE Error: 0.010343
2025-06-16 13:25:06,955 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 13:25:37,503 - INFO - Stage 1 training completed!
2025-06-16 13:25:37,740 - INFO - Starting Stage 2 training...
2025-06-16 13:25:37,740 - INFO - Training samples: 9120
2025-06-16 13:25:37,741 - INFO - Test samples: 2280
2025-06-16 13:25:37,742 - INFO - Stage 2 training skipped for quick test
2025-06-16 13:25:37,742 - INFO - Stage2 model save skipped for quick test
2025-06-16 13:38:46,517 - INFO - Starting Stage 1 training...
2025-06-16 13:38:46,517 - INFO - Training samples: 9120
2025-06-16 13:38:46,517 - INFO - Test samples: 2280
2025-06-16 13:38:57,598 - INFO - Epoch 0:
2025-06-16 13:38:57,598 - INFO -   Total Loss: 12.606366
2025-06-16 13:38:57,598 - INFO -   PEHE: 0.322759
2025-06-16 13:38:57,598 - INFO -   ATE Error: 0.001140
2025-06-16 13:38:57,650 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 13:38:57,717 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-16 13:39:31,068 - INFO - Epoch 20:
2025-06-16 13:39:31,068 - INFO -   Total Loss: 1.225495
2025-06-16 13:39:31,068 - INFO -   PEHE: 0.320956
2025-06-16 13:39:31,068 - INFO -   ATE Error: 0.012529
2025-06-16 13:39:31,130 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 13:40:05,169 - INFO - Epoch 40:
2025-06-16 13:40:05,169 - INFO -   Total Loss: 1.336811
2025-06-16 13:40:05,169 - INFO -   PEHE: 0.322195
2025-06-16 13:40:05,169 - INFO -   ATE Error: 0.008983
2025-06-16 13:40:22,718 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-16 13:40:38,674 - INFO - Epoch 60:
2025-06-16 13:40:38,674 - INFO -   Total Loss: 1.388179
2025-06-16 13:40:38,674 - INFO -   PEHE: 0.319775
2025-06-16 13:40:38,674 - INFO -   ATE Error: 0.008020
2025-06-16 13:40:38,754 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 13:41:11,364 - INFO - Epoch 80:
2025-06-16 13:41:11,364 - INFO -   Total Loss: 1.408091
2025-06-16 13:41:11,364 - INFO -   PEHE: 0.319926
2025-06-16 13:41:11,364 - INFO -   ATE Error: 0.010738
2025-06-16 13:41:43,960 - INFO - Stage 1 training completed!
2025-06-16 13:41:44,201 - INFO - Starting Stage 2 training...
2025-06-16 13:41:44,201 - INFO - Training samples: 9120
2025-06-16 13:41:44,201 - INFO - Test samples: 2280
2025-06-16 13:41:44,201 - INFO - Stage 2 training skipped for quick test
2025-06-16 13:41:44,204 - INFO - Stage2 model save skipped for quick test
2025-06-16 13:54:26,316 - INFO - Starting Stage 1 training...
2025-06-16 13:54:26,316 - INFO - Training samples: 9120
2025-06-16 13:54:26,316 - INFO - Test samples: 2280
2025-06-16 13:54:37,233 - INFO - Epoch 0:
2025-06-16 13:54:37,233 - INFO -   Total Loss: 12.606367
2025-06-16 13:54:37,233 - INFO -   PEHE: 0.322760
2025-06-16 13:54:37,233 - INFO -   ATE Error: 0.001117
2025-06-16 13:54:37,312 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 13:54:37,387 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-16 13:55:10,223 - INFO - Epoch 20:
2025-06-16 13:55:10,223 - INFO -   Total Loss: 1.229433
2025-06-16 13:55:10,223 - INFO -   PEHE: 0.320618
2025-06-16 13:55:10,223 - INFO -   ATE Error: 0.010346
2025-06-16 13:55:10,287 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 13:55:42,907 - INFO - Epoch 40:
2025-06-16 13:55:42,907 - INFO -   Total Loss: 1.350551
2025-06-16 13:55:42,907 - INFO -   PEHE: 0.318985
2025-06-16 13:55:42,907 - INFO -   ATE Error: 0.006017
2025-06-16 13:55:42,980 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 13:55:59,856 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-16 13:56:15,262 - INFO - Epoch 60:
2025-06-16 13:56:15,262 - INFO -   Total Loss: 1.379032
2025-06-16 13:56:15,262 - INFO -   PEHE: 0.319708
2025-06-16 13:56:15,262 - INFO -   ATE Error: 0.009401
2025-06-16 13:56:49,709 - INFO - Epoch 80:
2025-06-16 13:56:49,709 - INFO -   Total Loss: 1.426958
2025-06-16 13:56:49,709 - INFO -   PEHE: 0.319667
2025-06-16 13:56:49,709 - INFO -   ATE Error: 0.011528
2025-06-16 13:57:19,103 - INFO - Stage 1 training completed!
2025-06-16 13:57:19,356 - INFO - Starting Stage 2 training...
2025-06-16 13:57:19,356 - INFO - Training samples: 9120
2025-06-16 13:57:19,356 - INFO - Test samples: 2280
2025-06-16 13:57:19,356 - INFO - Stage 2 training skipped for quick test
2025-06-16 13:57:19,356 - INFO - Stage2 model save skipped for quick test
2025-06-16 14:00:45,386 - INFO - Starting Stage 1 training...
2025-06-16 14:00:45,386 - INFO - Training samples: 9120
2025-06-16 14:00:45,386 - INFO - Test samples: 2280
2025-06-16 14:00:56,715 - INFO - Epoch 0:
2025-06-16 14:00:56,715 - INFO -   Total Loss: 12.606367
2025-06-16 14:00:56,715 - INFO -   PEHE: 0.322759
2025-06-16 14:00:56,715 - INFO -   ATE Error: 0.001150
2025-06-16 14:00:56,778 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 14:00:56,840 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-16 14:01:29,586 - INFO - Epoch 20:
2025-06-16 14:01:29,586 - INFO -   Total Loss: 1.228575
2025-06-16 14:01:29,586 - INFO -   PEHE: 0.321513
2025-06-16 14:01:29,586 - INFO -   ATE Error: 0.013784
2025-06-16 14:01:29,669 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 14:02:01,205 - INFO - Epoch 40:
2025-06-16 14:02:01,211 - INFO -   Total Loss: 1.337837
2025-06-16 14:02:01,211 - INFO -   PEHE: 0.319818
2025-06-16 14:02:01,211 - INFO -   ATE Error: 0.009803
2025-06-16 14:02:01,274 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 14:02:17,155 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-16 14:02:34,024 - INFO - Epoch 60:
2025-06-16 14:02:34,024 - INFO -   Total Loss: 1.371295
2025-06-16 14:02:34,024 - INFO -   PEHE: 0.320770
2025-06-16 14:02:34,024 - INFO -   ATE Error: 0.007401
2025-06-16 14:03:04,133 - INFO - Epoch 80:
2025-06-16 14:03:04,133 - INFO -   Total Loss: 1.453632
2025-06-16 14:03:04,133 - INFO -   PEHE: 0.320091
2025-06-16 14:03:04,133 - INFO -   ATE Error: 0.010635
2025-06-16 14:03:37,609 - INFO - Stage 1 training completed!
2025-06-16 14:03:37,861 - INFO - Starting Stage 2 training...
2025-06-16 14:03:37,861 - INFO - Training samples: 9120
2025-06-16 14:03:37,861 - INFO - Test samples: 2280
2025-06-16 14:03:37,861 - INFO - Generating Stage 1 outputs for Stage 2 training...
2025-06-16 14:03:37,955 - INFO - Starting Stage 2 training for 50 iterations...
2025-06-16 14:19:13,684 - INFO - Starting Stage 1 training...
2025-06-16 14:19:13,684 - INFO - Training samples: 9120
2025-06-16 14:19:13,684 - INFO - Test samples: 2280
2025-06-16 14:19:24,384 - INFO - Epoch 0:
2025-06-16 14:19:24,384 - INFO -   Total Loss: 12.606365
2025-06-16 14:19:24,384 - INFO -   PEHE: 0.322759
2025-06-16 14:19:24,384 - INFO -   ATE Error: 0.001121
2025-06-16 14:19:24,452 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 14:19:24,505 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-16 14:19:54,628 - INFO - Epoch 20:
2025-06-16 14:19:54,628 - INFO -   Total Loss: 1.223600
2025-06-16 14:19:54,628 - INFO -   PEHE: 0.319743
2025-06-16 14:19:54,628 - INFO -   ATE Error: 0.009235
2025-06-16 14:19:54,675 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 14:20:19,473 - INFO - Epoch 40:
2025-06-16 14:20:19,473 - INFO -   Total Loss: 1.333871
2025-06-16 14:20:19,473 - INFO -   PEHE: 0.320213
2025-06-16 14:20:19,473 - INFO -   ATE Error: 0.009574
2025-06-16 14:20:33,680 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-16 14:20:47,730 - INFO - Epoch 60:
2025-06-16 14:20:47,731 - INFO -   Total Loss: 1.386333
2025-06-16 14:20:47,731 - INFO -   PEHE: 0.319734
2025-06-16 14:20:47,731 - INFO -   ATE Error: 0.009584
2025-06-16 14:20:47,812 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 14:21:17,288 - INFO - Epoch 80:
2025-06-16 14:21:17,288 - INFO -   Total Loss: 1.412418
2025-06-16 14:21:17,288 - INFO -   PEHE: 0.320378
2025-06-16 14:21:17,288 - INFO -   ATE Error: 0.012201
2025-06-16 14:21:44,612 - INFO - Stage 1 training completed!
2025-06-16 14:21:44,864 - INFO - Starting Stage 2 training...
2025-06-16 14:21:44,864 - INFO - Training samples: 9120
2025-06-16 14:21:44,864 - INFO - Test samples: 2280
2025-06-16 14:21:44,864 - INFO - Generating Stage 1 outputs for Stage 2 training...
2025-06-16 14:21:44,953 - INFO - Starting Stage 2 training for 50 iterations...
2025-06-16 15:48:15,633 - INFO - Starting Stage 1 training...
2025-06-16 15:48:15,633 - INFO - Training samples: 9120
2025-06-16 15:48:15,633 - INFO - Test samples: 2280
2025-06-16 15:48:25,275 - INFO - Epoch 0:
2025-06-16 15:48:25,275 - INFO -   Total Loss: 12.606368
2025-06-16 15:48:25,275 - INFO -   PEHE: 0.322759
2025-06-16 15:48:25,275 - INFO -   ATE Error: 0.001137
2025-06-16 15:48:25,386 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 15:48:25,496 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-16 15:48:51,571 - INFO - Epoch 20:
2025-06-16 15:48:51,571 - INFO -   Total Loss: 1.224820
2025-06-16 15:48:51,572 - INFO -   PEHE: 0.320513
2025-06-16 15:48:51,573 - INFO -   ATE Error: 0.011289
2025-06-16 15:48:51,648 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 15:49:18,222 - INFO - Epoch 40:
2025-06-16 15:49:18,222 - INFO -   Total Loss: 1.344860
2025-06-16 15:49:18,222 - INFO -   PEHE: 0.319201
2025-06-16 15:49:18,222 - INFO -   ATE Error: 0.008962
2025-06-16 15:49:18,284 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 15:49:30,556 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-16 15:49:42,304 - INFO - Epoch 60:
2025-06-16 15:49:42,304 - INFO -   Total Loss: 1.386461
2025-06-16 15:49:42,304 - INFO -   PEHE: 0.319981
2025-06-16 15:49:42,304 - INFO -   ATE Error: 0.009014
2025-06-16 15:50:08,996 - INFO - Epoch 80:
2025-06-16 15:50:08,996 - INFO -   Total Loss: 1.436998
2025-06-16 15:50:08,996 - INFO -   PEHE: 0.319390
2025-06-16 15:50:08,996 - INFO -   ATE Error: 0.011476
2025-06-16 15:50:34,268 - INFO - Stage 1 training completed!
2025-06-16 15:50:34,533 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-06-16 15:50:34,534 - INFO - Stage 2 training data shapes:
2025-06-16 15:50:34,535 - INFO -   x_train: (9120, 30)
2025-06-16 15:50:34,536 - INFO -   y_bar_target: (9120, 2)
2025-06-16 15:52:13,412 - INFO - Starting Stage 1 training...
2025-06-16 15:52:13,412 - INFO - Training samples: 9120
2025-06-16 15:52:13,412 - INFO - Test samples: 2280
2025-06-16 15:52:24,630 - INFO - Epoch 0:
2025-06-16 15:52:24,630 - INFO -   Total Loss: 12.606365
2025-06-16 15:52:24,630 - INFO -   PEHE: 0.322760
2025-06-16 15:52:24,630 - INFO -   ATE Error: 0.001156
2025-06-16 15:52:24,674 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 15:52:24,722 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-16 15:52:49,674 - INFO - Epoch 20:
2025-06-16 15:52:49,674 - INFO -   Total Loss: 1.226870
2025-06-16 15:52:49,674 - INFO -   PEHE: 0.320445
2025-06-16 15:52:49,674 - INFO -   ATE Error: 0.008981
2025-06-16 15:52:49,722 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 15:53:20,634 - INFO - Epoch 40:
2025-06-16 15:53:20,634 - INFO -   Total Loss: 1.342249
2025-06-16 15:53:20,634 - INFO -   PEHE: 0.320849
2025-06-16 15:53:20,634 - INFO -   ATE Error: 0.009731
2025-06-16 15:53:37,145 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-16 15:53:52,855 - INFO - Epoch 60:
2025-06-16 15:53:52,855 - INFO -   Total Loss: 1.389879
2025-06-16 15:53:52,855 - INFO -   PEHE: 0.321200
2025-06-16 15:53:52,855 - INFO -   ATE Error: 0.008172
2025-06-16 15:54:27,208 - INFO - Epoch 80:
2025-06-16 15:54:27,210 - INFO -   Total Loss: 1.451213
2025-06-16 15:54:27,210 - INFO -   PEHE: 0.319799
2025-06-16 15:54:27,210 - INFO -   ATE Error: 0.009665
2025-06-16 15:54:27,272 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 15:54:53,725 - INFO - Stage 1 training completed!
2025-06-16 15:54:53,992 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-06-16 15:54:53,992 - INFO - Stage 2 training data shapes:
2025-06-16 15:54:53,992 - INFO -   x_train: (9120, 30)
2025-06-16 15:54:53,992 - INFO -   y_bar_target: (9120, 2)
2025-06-16 15:55:47,125 - INFO - Stage 2 training completed!
2025-06-16 15:55:47,427 - INFO - Stage2 model save skipped for quick test
2025-06-16 16:15:35,051 - INFO - Starting Stage 1 training...
2025-06-16 16:15:35,051 - INFO - Training samples: 9120
2025-06-16 16:15:35,051 - INFO - Test samples: 2280
2025-06-16 16:15:45,367 - INFO - Epoch 0:
2025-06-16 16:15:45,367 - INFO -   Total Loss: 12.606368
2025-06-16 16:15:45,367 - INFO -   PEHE: 0.322759
2025-06-16 16:15:45,367 - INFO -   ATE Error: 0.001123
2025-06-16 16:15:45,433 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 16:15:45,517 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-16 16:16:12,012 - INFO - Epoch 20:
2025-06-16 16:16:12,013 - INFO -   Total Loss: 1.229654
2025-06-16 16:16:12,013 - INFO -   PEHE: 0.320717
2025-06-16 16:16:12,014 - INFO -   ATE Error: 0.006807
2025-06-16 16:16:12,073 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 16:16:41,267 - INFO - Epoch 40:
2025-06-16 16:16:41,268 - INFO -   Total Loss: 1.338425
2025-06-16 16:16:41,268 - INFO -   PEHE: 0.319997
2025-06-16 16:16:41,268 - INFO -   ATE Error: 0.009795
2025-06-16 16:16:41,317 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 16:16:56,403 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-16 16:17:11,426 - INFO - Epoch 60:
2025-06-16 16:17:11,427 - INFO -   Total Loss: 1.380263
2025-06-16 16:17:11,427 - INFO -   PEHE: 0.320492
2025-06-16 16:17:11,428 - INFO -   ATE Error: 0.008414
2025-06-16 16:17:42,114 - INFO - Epoch 80:
2025-06-16 16:17:42,114 - INFO -   Total Loss: 1.415527
2025-06-16 16:17:42,114 - INFO -   PEHE: 0.319394
2025-06-16 16:17:42,114 - INFO -   ATE Error: 0.010969
2025-06-16 16:17:42,161 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 16:18:06,408 - INFO - Epoch 100:
2025-06-16 16:18:06,409 - INFO -   Total Loss: 1.427265
2025-06-16 16:18:06,409 - INFO -   PEHE: 0.319785
2025-06-16 16:18:06,410 - INFO -   ATE Error: 0.010903
2025-06-16 16:18:06,482 - INFO - Model saved to ./results\models\stage1_model_epoch_100
2025-06-16 16:18:32,959 - INFO - Epoch 120:
2025-06-16 16:18:32,961 - INFO -   Total Loss: 1.481868
2025-06-16 16:18:32,961 - INFO -   PEHE: 0.319607
2025-06-16 16:18:32,961 - INFO -   ATE Error: 0.011953
2025-06-16 16:19:01,234 - INFO - Epoch 140:
2025-06-16 16:19:01,238 - INFO -   Total Loss: 1.517805
2025-06-16 16:19:01,239 - INFO -   PEHE: 0.319479
2025-06-16 16:19:01,239 - INFO -   ATE Error: 0.012245
2025-06-16 16:19:15,709 - INFO - Model saved to ./results\models\stage1_model_epoch_150
2025-06-16 16:19:29,021 - INFO - Epoch 160:
2025-06-16 16:19:29,022 - INFO -   Total Loss: 1.527104
2025-06-16 16:19:29,022 - INFO -   PEHE: 0.319825
2025-06-16 16:19:29,022 - INFO -   ATE Error: 0.011448
2025-06-16 16:19:55,630 - INFO - Epoch 180:
2025-06-16 16:19:55,630 - INFO -   Total Loss: 1.544323
2025-06-16 16:19:55,630 - INFO -   PEHE: 0.319472
2025-06-16 16:19:55,630 - INFO -   ATE Error: 0.010995
2025-06-16 16:20:21,517 - INFO - Epoch 200:
2025-06-16 16:20:21,518 - INFO -   Total Loss: 1.601093
2025-06-16 16:20:21,518 - INFO -   PEHE: 0.319615
2025-06-16 16:20:21,518 - INFO -   ATE Error: 0.010857
2025-06-16 16:20:21,584 - INFO - Model saved to ./results\models\stage1_model_epoch_200
2025-06-16 16:20:46,586 - INFO - Epoch 220:
2025-06-16 16:20:46,587 - INFO -   Total Loss: 1.576085
2025-06-16 16:20:46,587 - INFO -   PEHE: 0.319773
2025-06-16 16:20:46,587 - INFO -   ATE Error: 0.010508
2025-06-16 16:21:13,457 - INFO - Epoch 240:
2025-06-16 16:21:13,458 - INFO -   Total Loss: 1.615809
2025-06-16 16:21:13,458 - INFO -   PEHE: 0.319812
2025-06-16 16:21:13,459 - INFO -   ATE Error: 0.011343
2025-06-16 16:21:28,961 - INFO - Model saved to ./results\models\stage1_model_epoch_250
2025-06-16 16:21:41,874 - INFO - Epoch 260:
2025-06-16 16:21:41,874 - INFO -   Total Loss: 1.656094
2025-06-16 16:21:41,875 - INFO -   PEHE: 0.319683
2025-06-16 16:21:41,875 - INFO -   ATE Error: 0.011381
2025-06-16 16:22:12,078 - INFO - Epoch 280:
2025-06-16 16:22:12,079 - INFO -   Total Loss: 1.618774
2025-06-16 16:22:12,079 - INFO -   PEHE: 0.319736
2025-06-16 16:22:12,080 - INFO -   ATE Error: 0.011665
2025-06-16 16:22:39,507 - INFO - Epoch 300:
2025-06-16 16:22:39,507 - INFO -   Total Loss: 1.697319
2025-06-16 16:22:39,507 - INFO -   PEHE: 0.319852
2025-06-16 16:22:39,507 - INFO -   ATE Error: 0.011640
2025-06-16 16:22:39,552 - INFO - Model saved to ./results\models\stage1_model_epoch_300
2025-06-16 16:23:04,867 - INFO - Epoch 320:
2025-06-16 16:23:04,868 - INFO -   Total Loss: 1.698114
2025-06-16 16:23:04,868 - INFO -   PEHE: 0.319652
2025-06-16 16:23:04,868 - INFO -   ATE Error: 0.011423
2025-06-16 16:23:30,635 - INFO - Epoch 340:
2025-06-16 16:23:30,636 - INFO -   Total Loss: 1.603897
2025-06-16 16:23:30,636 - INFO -   PEHE: 0.319754
2025-06-16 16:23:30,636 - INFO -   ATE Error: 0.011252
2025-06-16 16:23:44,109 - INFO - Model saved to ./results\models\stage1_model_epoch_350
2025-06-16 16:23:56,655 - INFO - Epoch 360:
2025-06-16 16:23:56,655 - INFO -   Total Loss: 1.674428
2025-06-16 16:23:56,656 - INFO -   PEHE: 0.319493
2025-06-16 16:23:56,657 - INFO -   ATE Error: 0.011620
2025-06-16 16:24:24,836 - INFO - Epoch 380:
2025-06-16 16:24:24,837 - INFO -   Total Loss: 1.625070
2025-06-16 16:24:24,838 - INFO -   PEHE: 0.320066
2025-06-16 16:24:24,840 - INFO -   ATE Error: 0.011536
2025-06-16 16:24:49,692 - INFO - Stage 1 training completed!
2025-06-16 16:24:49,907 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-06-16 16:24:49,907 - INFO - Stage 2 training data shapes:
2025-06-16 16:24:49,908 - INFO -   x_train: (9120, 30)
2025-06-16 16:24:49,908 - INFO -   y_bar_target: (9120, 2)
2025-06-16 16:25:44,521 - INFO - Stage 2 training completed!
2025-06-16 16:25:44,781 - INFO - Stage2 model save skipped for quick test
2025-06-16 17:39:31,817 - INFO - Starting Stage 1 training...
2025-06-16 17:39:31,817 - INFO - Training samples: 9120
2025-06-16 17:39:31,817 - INFO - Test samples: 2280
2025-06-16 17:39:31,817 - INFO - Sample weights initialized: shape=(9120, 1)
2025-06-16 17:41:38,011 - INFO - Starting Stage 1 training...
2025-06-16 17:41:38,012 - INFO - Training samples: 9120
2025-06-16 17:41:38,012 - INFO - Test samples: 2280
2025-06-16 17:41:38,013 - INFO - Sample weights initialized: shape=(9120, 1)
2025-06-16 17:41:48,641 - INFO - Epoch 0:
2025-06-16 17:41:48,643 - INFO -   Total Loss: 15.722594
2025-06-16 17:41:48,644 - INFO -   PEHE: 0.324274
2025-06-16 17:41:48,645 - INFO -   ATE Error: 0.043706
2025-06-16 17:41:48,715 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 17:41:48,785 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-16 17:42:20,888 - INFO - Epoch 20:
2025-06-16 17:42:20,888 - INFO -   Total Loss: 2.033880
2025-06-16 17:42:20,888 - INFO -   PEHE: 0.320381
2025-06-16 17:42:20,889 - INFO -   ATE Error: 0.001785
2025-06-16 17:42:20,959 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 17:42:53,979 - INFO - Epoch 40:
2025-06-16 17:42:53,980 - INFO -   Total Loss: 4.379614
2025-06-16 17:42:53,980 - INFO -   PEHE: 0.320309
2025-06-16 17:42:53,981 - INFO -   ATE Error: 0.001439
2025-06-16 17:42:54,060 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 17:43:08,852 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-16 17:43:23,400 - INFO - Epoch 60:
2025-06-16 17:43:23,401 - INFO -   Total Loss: 6.899621
2025-06-16 17:43:23,401 - INFO -   PEHE: 0.322594
2025-06-16 17:43:23,401 - INFO -   ATE Error: 0.004723
2025-06-16 17:43:55,167 - INFO - Epoch 80:
2025-06-16 17:43:55,168 - INFO -   Total Loss: 9.543710
2025-06-16 17:43:55,168 - INFO -   PEHE: 0.328138
2025-06-16 17:43:55,169 - INFO -   ATE Error: 0.013581
2025-06-16 17:44:27,490 - INFO - Epoch 100:
2025-06-16 17:44:27,491 - INFO -   Total Loss: 11.642153
2025-06-16 17:44:27,492 - INFO -   PEHE: 0.327654
2025-06-16 17:44:27,492 - INFO -   ATE Error: 0.016658
2025-06-16 17:44:27,549 - INFO - Model saved to ./results\models\stage1_model_epoch_100
2025-06-16 17:44:59,695 - INFO - Epoch 120:
2025-06-16 17:44:59,695 - INFO -   Total Loss: 13.217210
2025-06-16 17:44:59,695 - INFO -   PEHE: 0.328052
2025-06-16 17:44:59,695 - INFO -   ATE Error: 0.005423
2025-06-16 17:45:26,754 - INFO - Epoch 140:
2025-06-16 17:45:26,755 - INFO -   Total Loss: 14.914353
2025-06-16 17:45:26,755 - INFO -   PEHE: 0.332306
2025-06-16 17:45:26,756 - INFO -   ATE Error: 0.018655
2025-06-16 17:45:42,223 - INFO - Model saved to ./results\models\stage1_model_epoch_150
2025-06-16 17:45:57,216 - INFO - Epoch 160:
2025-06-16 17:45:57,218 - INFO -   Total Loss: 15.753654
2025-06-16 17:45:57,218 - INFO -   PEHE: 0.331994
2025-06-16 17:45:57,219 - INFO -   ATE Error: 0.009417
2025-06-16 17:46:25,984 - INFO - Epoch 180:
2025-06-16 17:46:25,984 - INFO -   Total Loss: 17.414717
2025-06-16 17:46:25,984 - INFO -   PEHE: 0.334015
2025-06-16 17:46:25,984 - INFO -   ATE Error: 0.000327
2025-06-16 17:46:53,976 - INFO - Epoch 200:
2025-06-16 17:46:53,976 - INFO -   Total Loss: 18.195932
2025-06-16 17:46:53,977 - INFO -   PEHE: 0.337947
2025-06-16 17:46:53,977 - INFO -   ATE Error: 0.006391
2025-06-16 17:46:54,041 - INFO - Model saved to ./results\models\stage1_model_epoch_200
2025-06-16 17:47:21,373 - INFO - Epoch 220:
2025-06-16 17:47:21,388 - INFO -   Total Loss: 18.782324
2025-06-16 17:47:21,388 - INFO -   PEHE: 0.346397
2025-06-16 17:47:21,388 - INFO -   ATE Error: 0.011729
2025-06-16 17:47:49,642 - INFO - Epoch 240:
2025-06-16 17:47:49,642 - INFO -   Total Loss: 19.389553
2025-06-16 17:47:49,642 - INFO -   PEHE: 0.338308
2025-06-16 17:47:49,642 - INFO -   ATE Error: 0.008654
2025-06-16 17:48:00,922 - INFO - Model saved to ./results\models\stage1_model_epoch_250
2025-06-16 17:48:15,374 - INFO - Epoch 260:
2025-06-16 17:48:15,375 - INFO -   Total Loss: 20.548763
2025-06-16 17:48:15,375 - INFO -   PEHE: 0.341787
2025-06-16 17:48:15,376 - INFO -   ATE Error: 0.004437
2025-06-16 17:48:48,659 - INFO - Epoch 280:
2025-06-16 17:48:48,659 - INFO -   Total Loss: 20.654535
2025-06-16 17:48:48,659 - INFO -   PEHE: 0.348391
2025-06-16 17:48:48,659 - INFO -   ATE Error: 0.013551
2025-06-16 17:49:16,319 - INFO - Epoch 300:
2025-06-16 17:49:16,320 - INFO -   Total Loss: nan
2025-06-16 17:49:16,320 - INFO -   PEHE: nan
2025-06-16 17:49:16,320 - INFO -   ATE Error: nan
2025-06-16 17:49:16,388 - INFO - Model saved to ./results\models\stage1_model_epoch_300
2025-06-16 17:49:44,237 - INFO - Epoch 320:
2025-06-16 17:49:44,237 - INFO -   Total Loss: nan
2025-06-16 17:49:44,237 - INFO -   PEHE: nan
2025-06-16 17:49:44,238 - INFO -   ATE Error: nan
2025-06-16 17:50:14,450 - INFO - Epoch 340:
2025-06-16 17:50:14,451 - INFO -   Total Loss: nan
2025-06-16 17:50:14,452 - INFO -   PEHE: nan
2025-06-16 17:50:14,452 - INFO -   ATE Error: nan
2025-06-16 17:50:29,809 - INFO - Model saved to ./results\models\stage1_model_epoch_350
2025-06-16 17:50:44,475 - INFO - Epoch 360:
2025-06-16 17:50:44,476 - INFO -   Total Loss: nan
2025-06-16 17:50:44,476 - INFO -   PEHE: nan
2025-06-16 17:50:44,476 - INFO -   ATE Error: nan
2025-06-16 17:51:14,722 - INFO - Epoch 380:
2025-06-16 17:51:14,722 - INFO -   Total Loss: nan
2025-06-16 17:51:14,722 - INFO -   PEHE: nan
2025-06-16 17:51:14,722 - INFO -   ATE Error: nan
2025-06-16 17:51:44,644 - INFO - Stage 1 training completed!
2025-06-16 17:51:44,880 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-06-16 17:51:44,881 - INFO - Stage 2 training data shapes:
2025-06-16 17:51:44,881 - INFO -   x_train: (9120, 30)
2025-06-16 17:51:44,882 - INFO -   y_bar_target: (9120, 2)
2025-06-16 17:52:36,497 - INFO - Stage 2 training completed!
2025-06-16 17:52:36,713 - INFO - Stage2 model save skipped for quick test
2025-06-16 18:51:21,765 - INFO - Starting Stage 1 training...
2025-06-16 18:51:21,765 - INFO - Training samples: 9120
2025-06-16 18:51:21,765 - INFO - Test samples: 2280
2025-06-16 18:51:21,765 - INFO - Sample weights initialized: shape=(9120, 1)
2025-06-16 18:51:33,078 - INFO - Epoch 0:
2025-06-16 18:51:33,078 - INFO -   Total Loss: 15.722973
2025-06-16 18:51:33,079 - INFO -   PEHE: 0.322076
2025-06-16 18:51:33,079 - INFO -   ATE Error: 0.020157
2025-06-16 18:51:33,137 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 18:51:33,197 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-16 18:52:04,432 - INFO - Epoch 20:
2025-06-16 18:52:04,433 - INFO -   Total Loss: 1.181815
2025-06-16 18:52:04,433 - INFO -   PEHE: 0.324507
2025-06-16 18:52:04,433 - INFO -   ATE Error: 0.003843
2025-06-16 18:52:38,341 - INFO - Epoch 40:
2025-06-16 18:52:38,341 - INFO -   Total Loss: 1.240124
2025-06-16 18:52:38,342 - INFO -   PEHE: 0.323078
2025-06-16 18:52:38,342 - INFO -   ATE Error: 0.000753
2025-06-16 18:52:55,017 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-16 18:53:11,810 - INFO - Epoch 60:
2025-06-16 18:53:11,811 - INFO -   Total Loss: 1.259208
2025-06-16 18:53:11,811 - INFO -   PEHE: 0.320034
2025-06-16 18:53:11,811 - INFO -   ATE Error: 0.007043
2025-06-16 18:53:11,881 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 18:53:43,423 - INFO - Epoch 80:
2025-06-16 18:53:43,425 - INFO -   Total Loss: 1.240884
2025-06-16 18:53:43,426 - INFO -   PEHE: 0.319789
2025-06-16 18:53:43,426 - INFO -   ATE Error: 0.007508
2025-06-16 18:53:43,495 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 18:54:11,318 - INFO - Stage 1 training completed!
2025-06-16 18:54:11,551 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-06-16 18:54:11,552 - INFO - Stage 2 training data shapes:
2025-06-16 18:54:11,553 - INFO -   x_train: (9120, 30)
2025-06-16 18:54:11,554 - INFO -   y_bar_target: (9120, 2)
2025-06-16 18:55:06,248 - INFO - Stage 2 training completed!
2025-06-16 18:55:06,489 - INFO - Stage2 model save skipped for quick test
2025-06-16 22:29:04,893 - INFO - Starting Stage 1 training...
2025-06-16 22:29:04,894 - INFO - Training samples: 9120
2025-06-16 22:29:04,894 - INFO - Test samples: 2280
2025-06-16 22:29:04,894 - INFO - Sample weights initialized: shape=(9120, 1)
2025-06-16 22:29:16,342 - INFO - Epoch 0:
2025-06-16 22:29:16,342 - INFO -   Total Loss: 15.722800
2025-06-16 22:29:16,342 - INFO -   PEHE: 0.322098
2025-06-16 22:29:16,342 - INFO -   ATE Error: 0.022381
2025-06-16 22:29:16,417 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 22:29:16,479 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-16 22:29:45,198 - INFO - Epoch 20:
2025-06-16 22:29:45,198 - INFO -   Total Loss: 1.181591
2025-06-16 22:29:45,198 - INFO -   PEHE: 0.324715
2025-06-16 22:29:45,198 - INFO -   ATE Error: 0.003182
2025-06-16 22:30:17,057 - INFO - Epoch 40:
2025-06-16 22:30:17,057 - INFO -   Total Loss: 1.239525
2025-06-16 22:30:17,057 - INFO -   PEHE: 0.323038
2025-06-16 22:30:17,057 - INFO -   ATE Error: 0.003551
2025-06-16 22:30:33,902 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-16 22:30:50,176 - INFO - Epoch 60:
2025-06-16 22:30:50,176 - INFO -   Total Loss: 1.257608
2025-06-16 22:30:50,176 - INFO -   PEHE: 0.320727
2025-06-16 22:30:50,176 - INFO -   ATE Error: 0.006827
2025-06-16 22:30:50,223 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 22:31:22,923 - INFO - Epoch 80:
2025-06-16 22:31:22,923 - INFO -   Total Loss: 1.268627
2025-06-16 22:31:22,923 - INFO -   PEHE: 0.320493
2025-06-16 22:31:22,923 - INFO -   ATE Error: 0.005930
2025-06-16 22:31:22,985 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 22:31:56,362 - INFO - Epoch 100:
2025-06-16 22:31:56,362 - INFO -   Total Loss: 1.235942
2025-06-16 22:31:56,362 - INFO -   PEHE: 0.319744
2025-06-16 22:31:56,377 - INFO -   ATE Error: 0.009114
2025-06-16 22:31:56,437 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 22:31:56,499 - INFO - Model saved to ./results\models\stage1_model_epoch_100
2025-06-16 22:32:28,042 - INFO - Epoch 120:
2025-06-16 22:32:28,042 - INFO -   Total Loss: 1.253165
2025-06-16 22:32:28,042 - INFO -   PEHE: 0.320947
2025-06-16 22:32:28,042 - INFO -   ATE Error: 0.007080
2025-06-16 22:32:58,474 - INFO - Epoch 140:
2025-06-16 22:32:58,490 - INFO -   Total Loss: 1.196004
2025-06-16 22:32:58,490 - INFO -   PEHE: 0.319985
2025-06-16 22:32:58,490 - INFO -   ATE Error: 0.008496
2025-06-16 22:33:15,145 - INFO - Model saved to ./results\models\stage1_model_epoch_150
2025-06-16 22:33:29,422 - INFO - Epoch 160:
2025-06-16 22:33:29,422 - INFO -   Total Loss: 1.189468
2025-06-16 22:33:29,422 - INFO -   PEHE: 0.320133
2025-06-16 22:33:29,422 - INFO -   ATE Error: 0.010026
2025-06-16 22:34:01,235 - INFO - Epoch 180:
2025-06-16 22:34:01,235 - INFO -   Total Loss: 1.159405
2025-06-16 22:34:01,235 - INFO -   PEHE: 0.319576
2025-06-16 22:34:01,235 - INFO -   ATE Error: 0.010451
2025-06-16 22:34:01,298 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 22:34:32,385 - INFO - Epoch 200:
2025-06-16 22:34:32,385 - INFO -   Total Loss: 1.103487
2025-06-16 22:34:32,385 - INFO -   PEHE: 0.319574
2025-06-16 22:34:32,385 - INFO -   ATE Error: 0.008690
2025-06-16 22:34:32,432 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 22:34:32,464 - INFO - Model saved to ./results\models\stage1_model_epoch_200
2025-06-16 22:34:56,725 - INFO - Epoch 220:
2025-06-16 22:34:56,725 - INFO -   Total Loss: 1.078946
2025-06-16 22:34:56,725 - INFO -   PEHE: 0.319284
2025-06-16 22:34:56,725 - INFO -   ATE Error: 0.010361
2025-06-16 22:34:56,788 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 22:35:28,970 - INFO - Epoch 240:
2025-06-16 22:35:28,970 - INFO -   Total Loss: 1.048530
2025-06-16 22:35:28,970 - INFO -   PEHE: 0.319210
2025-06-16 22:35:28,970 - INFO -   ATE Error: 0.010241
2025-06-16 22:35:29,001 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 22:35:45,385 - INFO - Model saved to ./results\models\stage1_model_epoch_250
2025-06-16 22:36:01,722 - INFO - Epoch 260:
2025-06-16 22:36:01,722 - INFO -   Total Loss: 1.017092
2025-06-16 22:36:01,722 - INFO -   PEHE: 0.319554
2025-06-16 22:36:01,722 - INFO -   ATE Error: 0.011186
2025-06-16 22:36:35,029 - INFO - Epoch 280:
2025-06-16 22:36:35,029 - INFO -   Total Loss: 0.975856
2025-06-16 22:36:35,029 - INFO -   PEHE: 0.319584
2025-06-16 22:36:35,029 - INFO -   ATE Error: 0.011203
2025-06-16 22:49:12,891 - INFO - Starting Stage 1 training...
2025-06-16 22:49:12,891 - INFO - Training samples: 9120
2025-06-16 22:49:12,891 - INFO - Test samples: 2280
2025-06-16 22:49:12,891 - INFO - Sample weights initialized: shape=(9120, 1)
2025-06-16 22:49:24,418 - INFO - Epoch 0:
2025-06-16 22:49:24,418 - INFO -   Total Loss: 30.531694
2025-06-16 22:49:24,418 - INFO -   PEHE: 0.324863
2025-06-16 22:49:24,418 - INFO -   ATE Error: 0.046320
2025-06-16 22:49:24,480 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 22:49:24,539 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-16 22:49:54,618 - INFO - Epoch 20:
2025-06-16 22:49:54,618 - INFO -   Total Loss: 2.006078
2025-06-16 22:49:54,618 - INFO -   PEHE: 0.320505
2025-06-16 22:49:54,618 - INFO -   ATE Error: 0.003641
2025-06-16 22:49:54,665 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 22:50:25,640 - INFO - Epoch 40:
2025-06-16 22:50:25,640 - INFO -   Total Loss: 4.405267
2025-06-16 22:50:25,640 - INFO -   PEHE: 0.323271
2025-06-16 22:50:25,640 - INFO -   ATE Error: 0.011953
2025-06-16 22:50:42,390 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-16 22:50:57,216 - INFO - Epoch 60:
2025-06-16 22:50:57,216 - INFO -   Total Loss: 7.008757
2025-06-16 22:50:57,216 - INFO -   PEHE: 0.324812
2025-06-16 22:50:57,216 - INFO -   ATE Error: 0.005788
2025-06-16 22:51:29,506 - INFO - Epoch 80:
2025-06-16 22:51:29,506 - INFO -   Total Loss: 9.985668
2025-06-16 22:51:29,506 - INFO -   PEHE: 0.328509
2025-06-16 22:51:29,506 - INFO -   ATE Error: 0.025895
2025-06-16 22:52:03,130 - INFO - Epoch 100:
2025-06-16 22:52:03,130 - INFO -   Total Loss: 12.190350
2025-06-16 22:52:03,130 - INFO -   PEHE: 0.333681
2025-06-16 22:52:03,130 - INFO -   ATE Error: 0.013850
2025-06-16 22:52:03,193 - INFO - Model saved to ./results\models\stage1_model_epoch_100
2025-06-16 22:52:34,968 - INFO - Epoch 120:
2025-06-16 22:52:34,968 - INFO -   Total Loss: 13.500852
2025-06-16 22:52:34,968 - INFO -   PEHE: 0.335524
2025-06-16 22:52:34,968 - INFO -   ATE Error: 0.008290
2025-06-16 22:53:08,309 - INFO - Epoch 140:
2025-06-16 22:53:08,309 - INFO -   Total Loss: 14.927798
2025-06-16 22:53:08,309 - INFO -   PEHE: 0.338124
2025-06-16 22:53:08,309 - INFO -   ATE Error: 0.003371
2025-06-16 22:53:22,879 - INFO - Model saved to ./results\models\stage1_model_epoch_150
2025-06-16 22:53:35,225 - INFO - Epoch 160:
2025-06-16 22:53:35,227 - INFO -   Total Loss: 16.104078
2025-06-16 22:53:35,228 - INFO -   PEHE: 0.337297
2025-06-16 22:53:35,228 - INFO -   ATE Error: 0.003684
2025-06-16 22:54:01,817 - INFO - Epoch 180:
2025-06-16 22:54:01,817 - INFO -   Total Loss: 17.370668
2025-06-16 22:54:01,818 - INFO -   PEHE: 0.334591
2025-06-16 22:54:01,818 - INFO -   ATE Error: 0.006933
2025-06-16 22:54:27,543 - INFO - Epoch 200:
2025-06-16 22:54:27,543 - INFO -   Total Loss: 18.225054
2025-06-16 22:54:27,545 - INFO -   PEHE: 0.342394
2025-06-16 22:54:27,545 - INFO -   ATE Error: 0.023813
2025-06-16 22:54:27,623 - INFO - Model saved to ./results\models\stage1_model_epoch_200
2025-06-16 22:54:52,994 - INFO - Epoch 220:
2025-06-16 22:54:52,994 - INFO -   Total Loss: 18.758053
2025-06-16 22:54:52,994 - INFO -   PEHE: 0.344086
2025-06-16 22:54:52,994 - INFO -   ATE Error: 0.004168
2025-06-16 22:55:15,180 - INFO - Epoch 240:
2025-06-16 22:55:15,181 - INFO -   Total Loss: 19.452669
2025-06-16 22:55:15,182 - INFO -   PEHE: 0.341547
2025-06-16 22:55:15,183 - INFO -   ATE Error: 0.006701
2025-06-16 22:55:27,417 - INFO - Model saved to ./results\models\stage1_model_epoch_250
2025-06-16 22:55:39,752 - INFO - Epoch 260:
2025-06-16 22:55:39,752 - INFO -   Total Loss: 20.224627
2025-06-16 22:55:39,752 - INFO -   PEHE: 0.341875
2025-06-16 22:55:39,752 - INFO -   ATE Error: 0.004261
2025-06-16 22:56:04,325 - INFO - Epoch 280:
2025-06-16 22:56:04,325 - INFO -   Total Loss: 20.501530
2025-06-16 22:56:04,325 - INFO -   PEHE: 0.353146
2025-06-16 22:56:04,325 - INFO -   ATE Error: 0.006741
2025-06-16 22:56:30,754 - INFO - Epoch 300:
2025-06-16 22:56:30,754 - INFO -   Total Loss: 21.392889
2025-06-16 22:56:30,754 - INFO -   PEHE: 0.354277
2025-06-16 22:56:30,754 - INFO -   ATE Error: 0.010255
2025-06-16 22:56:30,824 - INFO - Model saved to ./results\models\stage1_model_epoch_300
2025-06-16 22:56:54,632 - INFO - Epoch 320:
2025-06-16 22:56:54,632 - INFO -   Total Loss: 21.727592
2025-06-16 22:56:54,632 - INFO -   PEHE: 0.359997
2025-06-16 22:56:54,632 - INFO -   ATE Error: 0.009893
2025-06-16 22:57:20,277 - INFO - Epoch 340:
2025-06-16 22:57:20,277 - INFO -   Total Loss: 21.973680
2025-06-16 22:57:20,277 - INFO -   PEHE: 0.351757
2025-06-16 22:57:20,277 - INFO -   ATE Error: 0.000358
2025-06-16 22:57:33,006 - INFO - Model saved to ./results\models\stage1_model_epoch_350
2025-06-16 22:57:46,170 - INFO - Epoch 360:
2025-06-16 22:57:46,170 - INFO -   Total Loss: 23.023211
2025-06-16 22:57:46,170 - INFO -   PEHE: 0.357478
2025-06-16 22:57:46,170 - INFO -   ATE Error: 0.011784
2025-06-16 22:58:11,297 - INFO - Epoch 380:
2025-06-16 22:58:11,298 - INFO -   Total Loss: 23.300829
2025-06-16 22:58:11,298 - INFO -   PEHE: 0.354468
2025-06-16 22:58:11,298 - INFO -   ATE Error: 0.002286
2025-06-16 22:58:32,861 - INFO - Stage 1 training completed!
2025-06-16 22:58:33,040 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-06-16 22:58:33,040 - INFO - Stage 2 training data shapes:
2025-06-16 22:58:33,040 - INFO -   x_train: (9120, 30)
2025-06-16 22:58:33,040 - INFO -   y_bar_target: (9120, 2)
2025-06-16 22:59:10,509 - INFO - Starting Stage 1 training...
2025-06-16 22:59:10,510 - INFO - Training samples: 9120
2025-06-16 22:59:10,510 - INFO - Test samples: 2280
2025-06-16 22:59:10,510 - INFO - Sample weights initialized: shape=(9120, 1)
2025-06-16 22:59:22,251 - INFO - Epoch 0:
2025-06-16 22:59:22,251 - INFO -   Total Loss: 30.532513
2025-06-16 22:59:22,251 - INFO -   PEHE: 0.322652
2025-06-16 22:59:22,251 - INFO -   ATE Error: 0.025987
2025-06-16 22:59:22,330 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 22:59:22,393 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-16 22:59:51,939 - INFO - Epoch 20:
2025-06-16 22:59:51,939 - INFO -   Total Loss: 1.207820
2025-06-16 22:59:51,939 - INFO -   PEHE: 0.322585
2025-06-16 22:59:51,939 - INFO -   ATE Error: 0.001145
2025-06-16 22:59:52,004 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 23:00:19,836 - INFO - Epoch 40:
2025-06-16 23:00:19,836 - INFO -   Total Loss: 1.316592
2025-06-16 23:00:19,836 - INFO -   PEHE: 0.320764
2025-06-16 23:00:19,836 - INFO -   ATE Error: 0.000456
2025-06-16 23:00:19,919 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 23:00:34,228 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-16 23:00:48,244 - INFO - Epoch 60:
2025-06-16 23:00:48,245 - INFO -   Total Loss: 1.370403
2025-06-16 23:00:48,246 - INFO -   PEHE: 0.320204
2025-06-16 23:00:48,246 - INFO -   ATE Error: 0.007071
2025-06-16 23:00:48,314 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 23:01:15,340 - INFO - Epoch 80:
2025-06-16 23:01:15,340 - INFO -   Total Loss: 1.428297
2025-06-16 23:01:15,340 - INFO -   PEHE: 0.319960
2025-06-16 23:01:15,340 - INFO -   ATE Error: 0.006763
2025-06-16 23:01:15,439 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 23:01:41,860 - INFO - Epoch 100:
2025-06-16 23:01:41,860 - INFO -   Total Loss: 1.462052
2025-06-16 23:01:41,861 - INFO -   PEHE: 0.320008
2025-06-16 23:01:41,861 - INFO -   ATE Error: 0.008322
2025-06-16 23:01:41,928 - INFO - Model saved to ./results\models\stage1_model_epoch_100
2025-06-16 23:02:09,156 - INFO - Epoch 120:
2025-06-16 23:02:09,157 - INFO -   Total Loss: 1.498444
2025-06-16 23:02:09,158 - INFO -   PEHE: 0.319373
2025-06-16 23:02:09,158 - INFO -   ATE Error: 0.007841
2025-06-16 23:02:09,229 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 23:02:38,842 - INFO - Epoch 140:
2025-06-16 23:02:38,842 - INFO -   Total Loss: 1.513804
2025-06-16 23:02:38,842 - INFO -   PEHE: 0.319332
2025-06-16 23:02:38,842 - INFO -   ATE Error: 0.009319
2025-06-16 23:02:38,921 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 23:02:53,162 - INFO - Model saved to ./results\models\stage1_model_epoch_150
2025-06-16 23:03:06,648 - INFO - Epoch 160:
2025-06-16 23:03:06,649 - INFO -   Total Loss: 1.558538
2025-06-16 23:03:06,650 - INFO -   PEHE: 0.319540
2025-06-16 23:03:06,651 - INFO -   ATE Error: 0.010404
2025-06-16 23:03:37,721 - INFO - Epoch 180:
2025-06-16 23:03:37,721 - INFO -   Total Loss: 1.619478
2025-06-16 23:03:37,721 - INFO -   PEHE: 0.318927
2025-06-16 23:03:37,721 - INFO -   ATE Error: 0.009020
2025-06-16 23:03:37,770 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-16 23:04:10,186 - INFO - Epoch 200:
2025-06-16 23:04:10,186 - INFO -   Total Loss: 1.655056
2025-06-16 23:04:10,186 - INFO -   PEHE: 0.319742
2025-06-16 23:04:10,186 - INFO -   ATE Error: 0.011217
2025-06-16 23:04:10,297 - INFO - Model saved to ./results\models\stage1_model_epoch_200
2025-06-16 23:04:39,593 - INFO - Epoch 220:
2025-06-16 23:04:39,593 - INFO -   Total Loss: 1.683243
2025-06-16 23:04:39,593 - INFO -   PEHE: 0.319865
2025-06-16 23:04:39,593 - INFO -   ATE Error: 0.011588
2025-06-16 23:05:09,932 - INFO - Epoch 240:
2025-06-16 23:05:09,932 - INFO -   Total Loss: 1.679780
2025-06-16 23:05:09,932 - INFO -   PEHE: 0.319487
2025-06-16 23:05:09,932 - INFO -   ATE Error: 0.011531
2025-06-16 23:05:23,495 - INFO - Model saved to ./results\models\stage1_model_epoch_250
2025-06-16 23:05:37,580 - INFO - Epoch 260:
2025-06-16 23:05:37,580 - INFO -   Total Loss: 1.765861
2025-06-16 23:05:37,580 - INFO -   PEHE: 0.319811
2025-06-16 23:05:37,580 - INFO -   ATE Error: 0.011679
2025-06-16 23:06:06,544 - INFO - Epoch 280:
2025-06-16 23:06:06,544 - INFO -   Total Loss: 1.758967
2025-06-16 23:06:06,544 - INFO -   PEHE: 0.319359
2025-06-16 23:06:06,544 - INFO -   ATE Error: 0.010300
2025-06-16 23:06:34,892 - INFO - Epoch 300:
2025-06-16 23:06:34,892 - INFO -   Total Loss: 1.722785
2025-06-16 23:06:34,892 - INFO -   PEHE: 0.319679
2025-06-16 23:06:34,892 - INFO -   ATE Error: 0.010656
2025-06-16 23:06:34,977 - INFO - Model saved to ./results\models\stage1_model_epoch_300
2025-06-16 23:07:04,068 - INFO - Epoch 320:
2025-06-16 23:07:04,069 - INFO -   Total Loss: 1.751951
2025-06-16 23:07:04,070 - INFO -   PEHE: 0.319665
2025-06-16 23:07:04,071 - INFO -   ATE Error: 0.010427
2025-06-16 23:07:34,175 - INFO - Epoch 340:
2025-06-16 23:07:34,176 - INFO -   Total Loss: 1.835894
2025-06-16 23:07:34,177 - INFO -   PEHE: 0.319671
2025-06-16 23:07:34,177 - INFO -   ATE Error: 0.010912
2025-06-16 23:07:49,798 - INFO - Model saved to ./results\models\stage1_model_epoch_350
2025-06-16 23:08:04,093 - INFO - Epoch 360:
2025-06-16 23:08:04,093 - INFO -   Total Loss: 1.770321
2025-06-16 23:08:04,093 - INFO -   PEHE: 0.319561
2025-06-16 23:08:04,093 - INFO -   ATE Error: 0.010759
2025-06-16 23:08:32,512 - INFO - Epoch 380:
2025-06-16 23:08:32,513 - INFO -   Total Loss: 1.901877
2025-06-16 23:08:32,513 - INFO -   PEHE: 0.319685
2025-06-16 23:08:32,514 - INFO -   ATE Error: 0.011484
2025-06-16 23:08:56,211 - INFO - Stage 1 training completed!
2025-06-16 23:08:56,433 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-06-16 23:08:56,433 - INFO - Stage 2 training data shapes:
2025-06-16 23:08:56,433 - INFO -   x_train: (9120, 30)
2025-06-16 23:08:56,433 - INFO -   y_bar_target: (9120, 2)
2025-06-16 23:09:44,910 - INFO - Stage 2 training completed!
2025-06-16 23:09:45,160 - INFO - Stage2 model save skipped for quick test
2025-06-17 11:27:01,673 - INFO - Starting Stage 1 training...
2025-06-17 11:27:01,673 - INFO - Training samples: 9120
2025-06-17 11:27:01,673 - INFO - Test samples: 2280
2025-06-17 11:27:01,673 - INFO - Sample weights initialized: shape=(9120, 1)
2025-06-17 11:27:14,843 - INFO - Epoch 0:
2025-06-17 11:27:14,843 - INFO -   Total Loss: 31.412903
2025-06-17 11:27:14,843 - INFO -   PEHE: 0.323328
2025-06-17 11:27:14,843 - INFO -   ATE Error: 0.032772
2025-06-17 11:27:14,924 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 11:27:14,993 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-17 11:27:46,784 - INFO - Epoch 20:
2025-06-17 11:27:46,784 - INFO -   Total Loss: 1.983517
2025-06-17 11:27:46,784 - INFO -   PEHE: 0.321829
2025-06-17 11:27:46,784 - INFO -   ATE Error: 0.000310
2025-06-17 11:27:46,829 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 11:28:15,038 - INFO - Epoch 40:
2025-06-17 11:28:15,038 - INFO -   Total Loss: 2.088040
2025-06-17 11:28:15,038 - INFO -   PEHE: 0.319832
2025-06-17 11:28:15,038 - INFO -   ATE Error: 0.005621
2025-06-17 11:28:15,117 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 11:28:31,078 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-17 11:28:46,890 - INFO - Epoch 60:
2025-06-17 11:28:46,890 - INFO -   Total Loss: 2.154132
2025-06-17 11:28:46,890 - INFO -   PEHE: 0.320053
2025-06-17 11:28:46,890 - INFO -   ATE Error: 0.009003
2025-06-17 11:29:14,025 - INFO - Epoch 80:
2025-06-17 11:29:14,025 - INFO -   Total Loss: 2.224770
2025-06-17 11:29:14,025 - INFO -   PEHE: 0.321292
2025-06-17 11:29:14,025 - INFO -   ATE Error: 0.005977
2025-06-17 11:29:41,019 - INFO - Epoch 100:
2025-06-17 11:29:41,019 - INFO -   Total Loss: 2.285927
2025-06-17 11:29:41,019 - INFO -   PEHE: 0.319796
2025-06-17 11:29:41,019 - INFO -   ATE Error: 0.005841
2025-06-17 11:29:41,081 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 11:29:41,144 - INFO - Model saved to ./results\models\stage1_model_epoch_100
2025-06-17 11:30:07,310 - INFO - Epoch 120:
2025-06-17 11:30:07,310 - INFO -   Total Loss: 2.268371
2025-06-17 11:30:07,311 - INFO -   PEHE: 0.319442
2025-06-17 11:30:07,311 - INFO -   ATE Error: 0.007973
2025-06-17 11:30:07,378 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 11:30:33,455 - INFO - Epoch 140:
2025-06-17 11:30:33,455 - INFO -   Total Loss: 2.337440
2025-06-17 11:30:33,455 - INFO -   PEHE: 0.319387
2025-06-17 11:30:33,455 - INFO -   ATE Error: 0.009986
2025-06-17 11:30:33,516 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 11:30:47,027 - INFO - Model saved to ./results\models\stage1_model_epoch_150
2025-06-17 11:31:00,520 - INFO - Epoch 160:
2025-06-17 11:31:00,520 - INFO -   Total Loss: 2.406381
2025-06-17 11:31:00,521 - INFO -   PEHE: 0.319849
2025-06-17 11:31:00,521 - INFO -   ATE Error: 0.009872
2025-06-17 11:31:27,428 - INFO - Epoch 180:
2025-06-17 11:31:27,428 - INFO -   Total Loss: 2.408206
2025-06-17 11:31:27,428 - INFO -   PEHE: 0.319458
2025-06-17 11:31:27,428 - INFO -   ATE Error: 0.009342
2025-06-17 11:31:55,775 - INFO - Epoch 200:
2025-06-17 11:31:55,775 - INFO -   Total Loss: 2.437389
2025-06-17 11:31:55,775 - INFO -   PEHE: 0.319728
2025-06-17 11:31:55,775 - INFO -   ATE Error: 0.009958
2025-06-17 11:31:55,857 - INFO - Model saved to ./results\models\stage1_model_epoch_200
2025-06-17 11:32:21,640 - INFO - Epoch 220:
2025-06-17 11:32:21,656 - INFO -   Total Loss: 2.453591
2025-06-17 11:32:21,656 - INFO -   PEHE: 0.319995
2025-06-17 11:32:21,656 - INFO -   ATE Error: 0.011378
2025-06-17 11:32:46,028 - INFO - Epoch 240:
2025-06-17 11:32:46,028 - INFO -   Total Loss: 2.485369
2025-06-17 11:32:46,028 - INFO -   PEHE: 0.319888
2025-06-17 11:32:46,028 - INFO -   ATE Error: 0.010278
2025-06-17 11:32:59,478 - INFO - Model saved to ./results\models\stage1_model_epoch_250
2025-06-17 11:33:12,485 - INFO - Epoch 260:
2025-06-17 11:33:12,485 - INFO -   Total Loss: 2.496700
2025-06-17 11:33:12,486 - INFO -   PEHE: 0.319569
2025-06-17 11:33:12,486 - INFO -   ATE Error: 0.010719
2025-06-17 11:33:44,417 - INFO - Epoch 280:
2025-06-17 11:33:44,417 - INFO -   Total Loss: 2.506421
2025-06-17 11:33:44,418 - INFO -   PEHE: 0.319749
2025-06-17 11:33:44,418 - INFO -   ATE Error: 0.011515
2025-06-17 11:34:12,204 - INFO - Epoch 300:
2025-06-17 11:34:12,204 - INFO -   Total Loss: 2.576329
2025-06-17 11:34:12,205 - INFO -   PEHE: 0.319695
2025-06-17 11:34:12,205 - INFO -   ATE Error: 0.011135
2025-06-17 11:34:12,284 - INFO - Model saved to ./results\models\stage1_model_epoch_300
2025-06-17 11:34:39,284 - INFO - Epoch 320:
2025-06-17 11:34:39,284 - INFO -   Total Loss: 2.687239
2025-06-17 11:34:39,285 - INFO -   PEHE: 0.319758
2025-06-17 11:34:39,286 - INFO -   ATE Error: 0.012199
2025-06-17 11:37:55,193 - INFO - Starting Stage 1 training...
2025-06-17 11:37:55,193 - INFO - Training samples: 9120
2025-06-17 11:37:55,194 - INFO - Test samples: 2280
2025-06-17 11:37:55,194 - INFO - Sample weights initialized: shape=(9120, 1)
2025-06-17 11:38:07,253 - INFO - Epoch 0:
2025-06-17 11:38:07,253 - INFO -   Total Loss: 31.413021
2025-06-17 11:38:07,253 - INFO -   PEHE: 0.323272
2025-06-17 11:38:07,253 - INFO -   ATE Error: 0.032068
2025-06-17 11:38:07,301 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 11:38:07,364 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-17 11:38:37,370 - INFO - Epoch 20:
2025-06-17 11:38:37,370 - INFO -   Total Loss: 1.982838
2025-06-17 11:38:37,385 - INFO -   PEHE: 0.322227
2025-06-17 11:38:37,385 - INFO -   ATE Error: 0.001687
2025-06-17 11:38:37,463 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 11:39:08,092 - INFO - Epoch 40:
2025-06-17 11:39:08,092 - INFO -   Total Loss: 2.078411
2025-06-17 11:39:08,092 - INFO -   PEHE: 0.321224
2025-06-17 11:39:08,092 - INFO -   ATE Error: 0.001273
2025-06-17 11:39:08,163 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 11:39:24,836 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-17 11:39:41,178 - INFO - Epoch 60:
2025-06-17 11:39:41,179 - INFO -   Total Loss: 2.152146
2025-06-17 11:39:41,179 - INFO -   PEHE: 0.319621
2025-06-17 11:39:41,180 - INFO -   ATE Error: 0.012491
2025-06-17 11:39:41,241 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 11:40:11,559 - INFO - Epoch 80:
2025-06-17 11:40:11,560 - INFO -   Total Loss: 2.203454
2025-06-17 11:40:11,560 - INFO -   PEHE: 0.319866
2025-06-17 11:40:11,561 - INFO -   ATE Error: 0.009500
2025-06-17 11:40:42,645 - INFO - Epoch 100:
2025-06-17 11:40:42,645 - INFO -   Total Loss: 2.231352
2025-06-17 11:40:42,645 - INFO -   PEHE: 0.319698
2025-06-17 11:40:42,645 - INFO -   ATE Error: 0.008074
2025-06-17 11:40:42,688 - INFO - Model saved to ./results\models\stage1_model_epoch_100
2025-06-17 11:41:15,116 - INFO - Epoch 120:
2025-06-17 11:41:15,117 - INFO -   Total Loss: 2.263664
2025-06-17 11:41:15,118 - INFO -   PEHE: 0.319724
2025-06-17 11:41:15,119 - INFO -   ATE Error: 0.010688
2025-06-17 11:41:47,326 - INFO - Epoch 140:
2025-06-17 11:41:47,326 - INFO -   Total Loss: 2.332505
2025-06-17 11:41:47,326 - INFO -   PEHE: 0.319521
2025-06-17 11:41:47,326 - INFO -   ATE Error: 0.008100
2025-06-17 11:41:47,406 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 11:42:02,375 - INFO - Model saved to ./results\models\stage1_model_epoch_150
2025-06-17 11:42:19,187 - INFO - Epoch 160:
2025-06-17 11:42:19,187 - INFO -   Total Loss: 2.352046
2025-06-17 11:42:19,187 - INFO -   PEHE: 0.319571
2025-06-17 11:42:19,187 - INFO -   ATE Error: 0.009073
2025-06-17 11:42:51,685 - INFO - Epoch 180:
2025-06-17 11:42:51,685 - INFO -   Total Loss: 2.370291
2025-06-17 11:42:51,685 - INFO -   PEHE: 0.319281
2025-06-17 11:42:51,685 - INFO -   ATE Error: 0.010773
2025-06-17 11:42:51,765 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 11:43:22,569 - INFO - Epoch 200:
2025-06-17 11:43:22,569 - INFO -   Total Loss: 2.405302
2025-06-17 11:43:22,569 - INFO -   PEHE: 0.319648
2025-06-17 11:43:22,569 - INFO -   ATE Error: 0.010542
2025-06-17 11:43:22,625 - INFO - Model saved to ./results\models\stage1_model_epoch_200
2025-06-17 11:43:55,638 - INFO - Epoch 220:
2025-06-17 11:43:55,638 - INFO -   Total Loss: 2.484587
2025-06-17 11:43:55,638 - INFO -   PEHE: 0.320277
2025-06-17 11:43:55,638 - INFO -   ATE Error: 0.011183
2025-06-17 11:44:29,025 - INFO - Epoch 240:
2025-06-17 11:44:29,025 - INFO -   Total Loss: 2.505045
2025-06-17 11:44:29,025 - INFO -   PEHE: 0.320833
2025-06-17 11:44:29,025 - INFO -   ATE Error: 0.010009
2025-06-17 11:44:44,783 - INFO - Model saved to ./results\models\stage1_model_epoch_250
2025-06-17 11:44:59,247 - INFO - Epoch 260:
2025-06-17 11:44:59,247 - INFO -   Total Loss: 2.515184
2025-06-17 11:44:59,247 - INFO -   PEHE: 0.319621
2025-06-17 11:44:59,247 - INFO -   ATE Error: 0.010649
2025-06-17 11:45:31,285 - INFO - Epoch 280:
2025-06-17 11:45:31,285 - INFO -   Total Loss: 2.493672
2025-06-17 11:45:31,285 - INFO -   PEHE: 0.319772
2025-06-17 11:45:31,285 - INFO -   ATE Error: 0.010427
2025-06-17 11:46:02,122 - INFO - Epoch 300:
2025-06-17 11:46:02,122 - INFO -   Total Loss: 2.580266
2025-06-17 11:46:02,127 - INFO -   PEHE: 0.319934
2025-06-17 11:46:02,127 - INFO -   ATE Error: 0.010302
2025-06-17 11:46:02,201 - INFO - Model saved to ./results\models\stage1_model_epoch_300
2025-06-17 11:46:34,312 - INFO - Epoch 320:
2025-06-17 11:46:34,312 - INFO -   Total Loss: 2.510243
2025-06-17 11:46:34,312 - INFO -   PEHE: 0.319494
2025-06-17 11:46:34,312 - INFO -   ATE Error: 0.011166
2025-06-17 11:47:07,207 - INFO - Epoch 340:
2025-06-17 11:47:07,207 - INFO -   Total Loss: 2.521396
2025-06-17 11:47:07,207 - INFO -   PEHE: 0.319642
2025-06-17 11:47:07,207 - INFO -   ATE Error: 0.010123
2025-06-17 11:47:23,807 - INFO - Model saved to ./results\models\stage1_model_epoch_350
2025-06-17 11:47:37,078 - INFO - Epoch 360:
2025-06-17 11:47:37,079 - INFO -   Total Loss: 2.675105
2025-06-17 11:47:37,079 - INFO -   PEHE: 0.319726
2025-06-17 11:47:37,079 - INFO -   ATE Error: 0.012419
2025-06-17 11:48:06,066 - INFO - Epoch 380:
2025-06-17 11:48:06,066 - INFO -   Total Loss: 2.708374
2025-06-17 11:48:06,066 - INFO -   PEHE: 0.319553
2025-06-17 11:48:06,066 - INFO -   ATE Error: 0.011795
2025-06-17 11:48:35,682 - INFO - Stage 1 training completed!
2025-06-17 11:48:35,981 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-06-17 11:48:35,981 - INFO - Stage 2 training data shapes:
2025-06-17 11:48:35,981 - INFO -   x_train: (9120, 30)
2025-06-17 11:48:35,981 - INFO -   y_bar_target: (9120, 2)
2025-06-17 11:49:26,358 - INFO - Stage 2 training completed!
2025-06-17 11:49:26,549 - INFO - Stage2 model save skipped for quick test
2025-06-17 14:25:37,920 - INFO - Starting Stage 1 training...
2025-06-17 14:25:37,922 - INFO - Training samples: 9120
2025-06-17 14:25:37,922 - INFO - Test samples: 2280
2025-06-17 14:25:37,922 - INFO - Sample weights initialized: shape=(9120, 1)
2025-06-17 14:25:51,092 - INFO - Epoch 0:
2025-06-17 14:25:51,093 - INFO -   Total Loss: 2136.059082
2025-06-17 14:25:51,093 - INFO -   PEHE: 0.324737
2025-06-17 14:25:51,093 - INFO -   ATE Error: 0.057695
2025-06-17 14:25:51,175 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 14:25:51,242 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-17 14:26:19,523 - INFO - Starting Stage 1 training...
2025-06-17 14:26:19,523 - INFO - Training samples: 9120
2025-06-17 14:26:19,523 - INFO - Test samples: 2280
2025-06-17 14:26:19,523 - INFO - Sample weights initialized: shape=(9120, 1)
2025-06-17 14:26:33,122 - INFO - Epoch 0:
2025-06-17 14:26:33,122 - INFO -   Total Loss: 2136.059082
2025-06-17 14:26:33,122 - INFO -   PEHE: 0.324737
2025-06-17 14:26:33,122 - INFO -   ATE Error: 0.057696
2025-06-17 14:26:33,215 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 14:26:33,303 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-17 14:26:58,687 - INFO - Epoch 20:
2025-06-17 14:26:58,687 - INFO -   Total Loss: 6.162165
2025-06-17 14:26:58,689 - INFO -   PEHE: 0.319647
2025-06-17 14:26:58,689 - INFO -   ATE Error: 0.010084
2025-06-17 14:26:58,771 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 14:27:24,132 - INFO - Epoch 40:
2025-06-17 14:27:24,132 - INFO -   Total Loss: 6.162203
2025-06-17 14:27:24,132 - INFO -   PEHE: 0.319671
2025-06-17 14:27:24,133 - INFO -   ATE Error: 0.010811
2025-06-17 14:27:38,730 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-17 14:27:53,807 - INFO - Epoch 60:
2025-06-17 14:27:53,807 - INFO -   Total Loss: 6.162192
2025-06-17 14:27:53,807 - INFO -   PEHE: 0.319702
2025-06-17 14:27:53,807 - INFO -   ATE Error: 0.011698
2025-06-17 14:28:21,075 - INFO - Epoch 80:
2025-06-17 14:28:21,075 - INFO -   Total Loss: 6.162202
2025-06-17 14:28:21,075 - INFO -   PEHE: 0.319655
2025-06-17 14:28:21,075 - INFO -   ATE Error: 0.010318
2025-06-17 14:28:47,112 - INFO - Epoch 100:
2025-06-17 14:28:47,113 - INFO -   Total Loss: 6.162219
2025-06-17 14:28:47,113 - INFO -   PEHE: 0.319660
2025-06-17 14:28:47,113 - INFO -   ATE Error: 0.010477
2025-06-17 14:28:47,180 - INFO - Model saved to ./results\models\stage1_model_epoch_100
2025-06-17 14:29:15,370 - INFO - Epoch 120:
2025-06-17 14:29:15,370 - INFO -   Total Loss: 6.162208
2025-06-17 14:29:15,370 - INFO -   PEHE: 0.319633
2025-06-17 14:29:15,386 - INFO -   ATE Error: 0.009602
2025-06-17 14:29:15,449 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 14:29:42,791 - INFO - Epoch 140:
2025-06-17 14:29:42,791 - INFO -   Total Loss: 6.162204
2025-06-17 14:29:42,791 - INFO -   PEHE: 0.319682
2025-06-17 14:29:42,791 - INFO -   ATE Error: 0.011121
2025-06-17 14:29:57,911 - INFO - Model saved to ./results\models\stage1_model_epoch_150
2025-06-17 14:30:11,147 - INFO - Epoch 160:
2025-06-17 14:30:11,147 - INFO -   Total Loss: 6.162199
2025-06-17 14:30:11,148 - INFO -   PEHE: 0.319688
2025-06-17 14:30:11,148 - INFO -   ATE Error: 0.011286
2025-06-17 14:30:36,405 - INFO - Epoch 180:
2025-06-17 14:30:36,405 - INFO -   Total Loss: 6.162252
2025-06-17 14:30:36,405 - INFO -   PEHE: 0.319651
2025-06-17 14:30:36,406 - INFO -   ATE Error: 0.010199
2025-06-17 14:31:02,641 - INFO - Epoch 200:
2025-06-17 14:31:02,642 - INFO -   Total Loss: 6.162186
2025-06-17 14:31:02,642 - INFO -   PEHE: 0.319678
2025-06-17 14:31:02,643 - INFO -   ATE Error: 0.011008
2025-06-17 14:31:02,722 - INFO - Model saved to ./results\models\stage1_model_epoch_200
2025-06-17 14:31:29,271 - INFO - Epoch 220:
2025-06-17 14:31:29,271 - INFO -   Total Loss: 6.162236
2025-06-17 14:31:29,271 - INFO -   PEHE: 0.319663
2025-06-17 14:31:29,271 - INFO -   ATE Error: 0.010562
2025-06-17 14:31:58,148 - INFO - Epoch 240:
2025-06-17 14:31:58,148 - INFO -   Total Loss: 6.162199
2025-06-17 14:31:58,148 - INFO -   PEHE: 0.319649
2025-06-17 14:31:58,148 - INFO -   ATE Error: 0.010146
2025-06-17 14:32:12,556 - INFO - Model saved to ./results\models\stage1_model_epoch_250
2025-06-17 14:32:27,222 - INFO - Epoch 260:
2025-06-17 14:32:27,222 - INFO -   Total Loss: 6.162237
2025-06-17 14:32:27,222 - INFO -   PEHE: 0.319658
2025-06-17 14:32:27,222 - INFO -   ATE Error: 0.010406
2025-06-17 14:32:55,614 - INFO - Epoch 280:
2025-06-17 14:32:55,614 - INFO -   Total Loss: 6.162199
2025-06-17 14:32:55,614 - INFO -   PEHE: 0.319655
2025-06-17 14:32:55,614 - INFO -   ATE Error: 0.010324
2025-06-17 14:33:25,544 - INFO - Epoch 300:
2025-06-17 14:33:25,544 - INFO -   Total Loss: 6.162181
2025-06-17 14:33:25,544 - INFO -   PEHE: 0.319658
2025-06-17 14:33:25,544 - INFO -   ATE Error: 0.010405
2025-06-17 14:33:25,607 - INFO - Model saved to ./results\models\stage1_model_epoch_300
2025-06-17 14:33:52,621 - INFO - Epoch 320:
2025-06-17 14:33:52,637 - INFO -   Total Loss: 6.162164
2025-06-17 14:33:52,637 - INFO -   PEHE: 0.319674
2025-06-17 14:33:52,638 - INFO -   ATE Error: 0.010900
2025-06-17 14:34:21,683 - INFO - Epoch 340:
2025-06-17 14:34:21,683 - INFO -   Total Loss: 6.162185
2025-06-17 14:34:21,683 - INFO -   PEHE: 0.319668
2025-06-17 14:34:21,698 - INFO -   ATE Error: 0.010725
2025-06-17 14:34:36,621 - INFO - Model saved to ./results\models\stage1_model_epoch_350
2025-06-17 14:34:50,722 - INFO - Epoch 360:
2025-06-17 14:34:50,722 - INFO -   Total Loss: 6.162164
2025-06-17 14:34:50,722 - INFO -   PEHE: 0.319667
2025-06-17 14:34:50,722 - INFO -   ATE Error: 0.010682
2025-06-17 14:35:17,911 - INFO - Epoch 380:
2025-06-17 14:35:17,913 - INFO -   Total Loss: 6.162157
2025-06-17 14:35:17,914 - INFO -   PEHE: 0.319679
2025-06-17 14:35:17,914 - INFO -   ATE Error: 0.011027
2025-06-17 14:35:45,439 - INFO - Stage 1 training completed!
2025-06-17 14:35:45,761 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-06-17 14:35:45,761 - INFO - Stage 2 training data shapes:
2025-06-17 14:35:45,761 - INFO -   x_train: (9120, 30)
2025-06-17 14:35:45,761 - INFO -   y_bar_target: (9120, 2)
2025-06-17 14:36:38,885 - INFO - Stage 2 training completed!
2025-06-17 14:36:39,156 - INFO - Stage2 model save skipped for quick test
2025-06-17 15:03:59,651 - INFO - Starting Stage 1 training...
2025-06-17 15:03:59,651 - INFO - Training samples: 9120
2025-06-17 15:03:59,651 - INFO - Test samples: 2280
2025-06-17 15:03:59,651 - INFO - Sample weights initialized: shape=(9120, 1)
2025-06-17 15:05:05,075 - INFO - Epoch 0:
2025-06-17 15:05:05,075 - INFO -   Total Loss: 5931.523438
2025-06-17 15:05:05,075 - INFO -   PEHE: 0.323102
2025-06-17 15:05:05,075 - INFO -   ATE Error: 0.048190
2025-06-17 15:05:05,557 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 15:05:06,022 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-17 15:06:04,509 - INFO - Epoch 20:
2025-06-17 15:06:04,509 - INFO -   Total Loss: 3.345981
2025-06-17 15:06:04,509 - INFO -   PEHE: 0.319662
2025-06-17 15:06:04,509 - INFO -   ATE Error: 0.010549
2025-06-17 15:06:04,966 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 15:07:04,924 - INFO - Epoch 40:
2025-06-17 15:07:04,924 - INFO -   Total Loss: 3.345985
2025-06-17 15:07:04,924 - INFO -   PEHE: 0.319684
2025-06-17 15:07:04,924 - INFO -   ATE Error: 0.011172
2025-06-17 15:07:35,472 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-17 15:08:05,068 - INFO - Epoch 60:
2025-06-17 15:08:05,068 - INFO -   Total Loss: 3.345998
2025-06-17 15:08:05,068 - INFO -   PEHE: 0.319667
2025-06-17 15:08:05,068 - INFO -   ATE Error: 0.010680
2025-06-17 15:09:05,733 - INFO - Epoch 80:
2025-06-17 15:09:05,776 - INFO -   Total Loss: 3.345986
2025-06-17 15:09:05,777 - INFO -   PEHE: 0.319656
2025-06-17 15:09:05,777 - INFO -   ATE Error: 0.010360
2025-06-17 15:09:06,300 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 15:10:08,725 - INFO - Epoch 100:
2025-06-17 15:10:08,726 - INFO -   Total Loss: 3.345996
2025-06-17 15:10:08,726 - INFO -   PEHE: 0.319678
2025-06-17 15:10:08,727 - INFO -   ATE Error: 0.011000
2025-06-17 15:10:09,237 - INFO - Model saved to ./results\models\stage1_model_epoch_100
2025-06-17 15:11:11,483 - INFO - Epoch 120:
2025-06-17 15:11:11,483 - INFO -   Total Loss: 3.345969
2025-06-17 15:11:11,483 - INFO -   PEHE: 0.319654
2025-06-17 15:11:11,483 - INFO -   ATE Error: 0.010277
2025-06-17 15:11:12,287 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 15:12:15,930 - INFO - Epoch 140:
2025-06-17 15:12:15,930 - INFO -   Total Loss: 3.345951
2025-06-17 15:12:15,930 - INFO -   PEHE: 0.319684
2025-06-17 15:12:15,930 - INFO -   ATE Error: 0.011182
2025-06-17 15:12:46,146 - INFO - Model saved to ./results\models\stage1_model_epoch_150
2025-06-17 15:13:15,234 - INFO - Epoch 160:
2025-06-17 15:13:15,234 - INFO -   Total Loss: 3.345986
2025-06-17 15:13:15,234 - INFO -   PEHE: 0.319682
2025-06-17 15:13:15,234 - INFO -   ATE Error: 0.011137
2025-06-17 15:14:14,294 - INFO - Epoch 180:
2025-06-17 15:14:14,294 - INFO -   Total Loss: 3.346062
2025-06-17 15:14:14,303 - INFO -   PEHE: 0.319666
2025-06-17 15:14:14,304 - INFO -   ATE Error: 0.010658
2025-06-17 15:15:14,081 - INFO - Epoch 200:
2025-06-17 15:15:14,081 - INFO -   Total Loss: 3.345955
2025-06-17 15:15:14,081 - INFO -   PEHE: 0.319628
2025-06-17 15:15:14,081 - INFO -   ATE Error: 0.009445
2025-06-17 15:15:14,562 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 15:15:15,064 - INFO - Model saved to ./results\models\stage1_model_epoch_200
2025-06-17 15:16:14,761 - INFO - Epoch 220:
2025-06-17 15:16:14,763 - INFO -   Total Loss: 3.345966
2025-06-17 15:16:14,764 - INFO -   PEHE: 0.319646
2025-06-17 15:16:14,764 - INFO -   ATE Error: 0.010045
2025-06-17 15:17:12,804 - INFO - Epoch 240:
2025-06-17 15:17:12,804 - INFO -   Total Loss: 3.345997
2025-06-17 15:17:12,815 - INFO -   PEHE: 0.319674
2025-06-17 15:17:12,815 - INFO -   ATE Error: 0.010890
2025-06-17 15:17:41,864 - INFO - Model saved to ./results\models\stage1_model_epoch_250
2025-06-17 15:18:12,029 - INFO - Epoch 260:
2025-06-17 15:18:12,029 - INFO -   Total Loss: 3.345969
2025-06-17 15:18:12,029 - INFO -   PEHE: 0.319689
2025-06-17 15:18:12,029 - INFO -   ATE Error: 0.011337
2025-06-17 15:19:12,822 - INFO - Epoch 280:
2025-06-17 15:19:12,874 - INFO -   Total Loss: 3.345959
2025-06-17 15:19:12,879 - INFO -   PEHE: 0.319667
2025-06-17 15:19:12,880 - INFO -   ATE Error: 0.010682
2025-06-17 15:20:11,872 - INFO - Epoch 300:
2025-06-17 15:20:11,872 - INFO -   Total Loss: 3.346013
2025-06-17 15:20:11,872 - INFO -   PEHE: 0.319669
2025-06-17 15:20:11,872 - INFO -   ATE Error: 0.010743
2025-06-17 15:20:12,375 - INFO - Model saved to ./results\models\stage1_model_epoch_300
2025-06-17 15:21:10,091 - INFO - Epoch 320:
2025-06-17 15:21:10,091 - INFO -   Total Loss: 3.345951
2025-06-17 15:21:10,102 - INFO -   PEHE: 0.319641
2025-06-17 15:21:10,102 - INFO -   ATE Error: 0.009895
2025-06-17 15:22:09,851 - INFO - Epoch 340:
2025-06-17 15:22:09,852 - INFO -   Total Loss: 3.345967
2025-06-17 15:22:09,852 - INFO -   PEHE: 0.319650
2025-06-17 15:22:09,852 - INFO -   ATE Error: 0.010168
2025-06-17 15:22:40,604 - INFO - Model saved to ./results\models\stage1_model_epoch_350
2025-06-17 15:23:10,657 - INFO - Epoch 360:
2025-06-17 15:23:10,657 - INFO -   Total Loss: 3.345976
2025-06-17 15:23:10,657 - INFO -   PEHE: 0.319684
2025-06-17 15:23:10,657 - INFO -   ATE Error: 0.011197
2025-06-17 15:24:10,954 - INFO - Epoch 380:
2025-06-17 15:24:10,954 - INFO -   Total Loss: 3.346037
2025-06-17 15:24:10,954 - INFO -   PEHE: 0.319664
2025-06-17 15:24:10,955 - INFO -   ATE Error: 0.010584
2025-06-17 15:25:05,240 - INFO - Stage 1 training completed!
2025-06-17 15:25:06,360 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-06-17 15:25:06,360 - INFO - Stage 2 training data shapes:
2025-06-17 15:25:06,360 - INFO -   x_train: (9120, 30)
2025-06-17 15:25:06,360 - INFO -   y_bar_target: (9120, 2)
2025-06-17 15:25:57,539 - INFO - Stage 2 training completed!
2025-06-17 15:25:57,761 - INFO - Stage2 model save skipped for quick test
2025-06-17 16:38:18,601 - INFO - Starting Stage 1 training...
2025-06-17 16:38:18,601 - INFO - Training samples: 9120
2025-06-17 16:38:18,615 - INFO - Test samples: 2280
2025-06-17 16:38:18,615 - INFO - Sample weights initialized: shape=(9120, 1)
2025-06-17 16:38:32,807 - INFO - Epoch 0:
2025-06-17 16:38:32,807 - INFO -   Total Loss: 4618.860840
2025-06-17 16:38:32,807 - INFO -   PEHE: 0.350722
2025-06-17 16:38:32,807 - INFO -   ATE Error: 0.128970
2025-06-17 16:38:32,897 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 16:38:32,976 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-17 16:39:02,173 - INFO - Epoch 20:
2025-06-17 16:39:02,173 - INFO -   Total Loss: 886.094849
2025-06-17 16:39:02,173 - INFO -   PEHE: 0.319541
2025-06-17 16:39:02,173 - INFO -   ATE Error: 0.005795
2025-06-17 16:39:02,251 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 16:39:31,301 - INFO - Epoch 40:
2025-06-17 16:39:31,301 - INFO -   Total Loss: 585.743713
2025-06-17 16:39:31,301 - INFO -   PEHE: 0.319670
2025-06-17 16:39:31,301 - INFO -   ATE Error: 0.010788
2025-06-17 16:39:46,058 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-17 16:39:59,233 - INFO - Epoch 60:
2025-06-17 16:39:59,233 - INFO -   Total Loss: 377.881836
2025-06-17 16:39:59,234 - INFO -   PEHE: 0.319674
2025-06-17 16:39:59,234 - INFO -   ATE Error: 0.010894
2025-06-17 16:40:28,715 - INFO - Epoch 80:
2025-06-17 16:40:28,715 - INFO -   Total Loss: 217.748962
2025-06-17 16:40:28,715 - INFO -   PEHE: 0.319669
2025-06-17 16:40:28,715 - INFO -   ATE Error: 0.010742
2025-06-17 16:40:58,556 - INFO - Epoch 100:
2025-06-17 16:40:58,556 - INFO -   Total Loss: 103.612038
2025-06-17 16:40:58,556 - INFO -   PEHE: 0.319673
2025-06-17 16:40:58,556 - INFO -   ATE Error: 0.010860
2025-06-17 16:40:58,672 - INFO - Model saved to ./results\models\stage1_model_epoch_100
2025-06-17 16:41:24,631 - INFO - Epoch 120:
2025-06-17 16:41:24,631 - INFO -   Total Loss: 34.764252
2025-06-17 16:41:24,631 - INFO -   PEHE: 0.319662
2025-06-17 16:41:24,631 - INFO -   ATE Error: 0.010541
2025-06-17 16:41:52,831 - INFO - Epoch 140:
2025-06-17 16:41:52,831 - INFO -   Total Loss: 10.880172
2025-06-17 16:41:52,831 - INFO -   PEHE: 0.319669
2025-06-17 16:41:52,831 - INFO -   ATE Error: 0.010744
2025-06-17 16:42:08,488 - INFO - Model saved to ./results\models\stage1_model_epoch_150
2025-06-17 16:42:23,282 - INFO - Epoch 160:
2025-06-17 16:42:23,282 - INFO -   Total Loss: 10.828837
2025-06-17 16:42:23,282 - INFO -   PEHE: 0.319670
2025-06-17 16:42:23,282 - INFO -   ATE Error: 0.010779
2025-06-17 16:42:53,811 - INFO - Epoch 180:
2025-06-17 16:42:53,813 - INFO -   Total Loss: 10.828854
2025-06-17 16:42:53,813 - INFO -   PEHE: 0.319671
2025-06-17 16:42:53,813 - INFO -   ATE Error: 0.010798
2025-06-17 16:43:24,993 - INFO - Epoch 200:
2025-06-17 16:43:24,994 - INFO -   Total Loss: 10.828844
2025-06-17 16:43:24,995 - INFO -   PEHE: 0.319672
2025-06-17 16:43:24,995 - INFO -   ATE Error: 0.010830
2025-06-17 16:43:25,081 - INFO - Model saved to ./results\models\stage1_model_epoch_200
2025-06-17 16:43:55,292 - INFO - Epoch 220:
2025-06-17 16:43:55,292 - INFO -   Total Loss: 10.828856
2025-06-17 16:43:55,292 - INFO -   PEHE: 0.319669
2025-06-17 16:43:55,292 - INFO -   ATE Error: 0.010746
2025-06-17 16:44:23,053 - INFO - Epoch 240:
2025-06-17 16:44:23,057 - INFO -   Total Loss: 10.828853
2025-06-17 16:44:23,061 - INFO -   PEHE: 0.319669
2025-06-17 16:44:23,066 - INFO -   ATE Error: 0.010762
2025-06-17 16:44:36,274 - INFO - Model saved to ./results\models\stage1_model_epoch_250
2025-06-17 16:44:50,057 - INFO - Epoch 260:
2025-06-17 16:44:50,066 - INFO -   Total Loss: 10.828834
2025-06-17 16:44:50,069 - INFO -   PEHE: 0.319671
2025-06-17 16:44:50,076 - INFO -   ATE Error: 0.010793
2025-06-17 16:45:19,551 - INFO - Epoch 280:
2025-06-17 16:45:19,551 - INFO -   Total Loss: 10.828838
2025-06-17 16:45:19,551 - INFO -   PEHE: 0.319668
2025-06-17 16:45:19,551 - INFO -   ATE Error: 0.010705
2025-06-17 16:45:49,458 - INFO - Epoch 300:
2025-06-17 16:45:49,458 - INFO -   Total Loss: 10.828853
2025-06-17 16:45:49,458 - INFO -   PEHE: 0.319673
2025-06-17 16:45:49,458 - INFO -   ATE Error: 0.010856
2025-06-17 16:45:49,537 - INFO - Model saved to ./results\models\stage1_model_epoch_300
2025-06-17 16:46:19,747 - INFO - Epoch 320:
2025-06-17 16:46:19,747 - INFO -   Total Loss: 10.828841
2025-06-17 16:46:19,747 - INFO -   PEHE: 0.319668
2025-06-17 16:46:19,747 - INFO -   ATE Error: 0.010730
2025-06-17 16:46:50,460 - INFO - Epoch 340:
2025-06-17 16:46:50,460 - INFO -   Total Loss: 10.828861
2025-06-17 16:46:50,460 - INFO -   PEHE: 0.319671
2025-06-17 16:46:50,460 - INFO -   ATE Error: 0.010809
2025-06-17 16:47:06,180 - INFO - Model saved to ./results\models\stage1_model_epoch_350
2025-06-17 16:47:21,541 - INFO - Epoch 360:
2025-06-17 16:47:21,541 - INFO -   Total Loss: 10.828868
2025-06-17 16:47:21,541 - INFO -   PEHE: 0.319670
2025-06-17 16:47:21,541 - INFO -   ATE Error: 0.010777
2025-06-17 16:47:50,306 - INFO - Epoch 380:
2025-06-17 16:47:50,306 - INFO -   Total Loss: 10.828857
2025-06-17 16:47:50,306 - INFO -   PEHE: 0.319670
2025-06-17 16:47:50,306 - INFO -   ATE Error: 0.010791
2025-06-17 16:48:18,405 - INFO - Stage 1 training completed!
2025-06-17 16:48:18,760 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-06-17 16:48:18,760 - INFO - Stage 2 training data shapes:
2025-06-17 16:48:18,760 - INFO -   x_train: (9120, 30)
2025-06-17 16:48:18,760 - INFO -   y_bar_target: (9120, 2)
2025-06-17 16:57:35,717 - INFO - Starting Stage 1 training...
2025-06-17 16:57:35,717 - INFO - Training samples: 9120
2025-06-17 16:57:35,717 - INFO - Test samples: 2280
2025-06-17 16:57:35,717 - INFO - Sample weights initialized: shape=(9120, 1)
2025-06-17 16:57:48,889 - INFO - Epoch 0:
2025-06-17 16:57:48,889 - INFO -   Total Loss: 4618.860840
2025-06-17 16:57:48,889 - INFO -   PEHE: 0.350722
2025-06-17 16:57:48,889 - INFO -   ATE Error: 0.128970
2025-06-17 16:57:48,889 - INFO -     - Main Losses (G): Factual=0.7120, Adversarial=0.6829, Decomp=71.3858
2025-06-17 16:57:48,889 - INFO -     - Discriminator Loss (D): 29.2337
2025-06-17 16:57:48,889 - INFO -     - Balance Loss (Weights): 0.2437
2025-06-17 16:57:48,974 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 16:57:49,053 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-17 16:58:18,362 - INFO - Epoch 20:
2025-06-17 16:58:18,362 - INFO -   Total Loss: 886.094849
2025-06-17 16:58:18,363 - INFO -   PEHE: 0.319541
2025-06-17 16:58:18,363 - INFO -   ATE Error: 0.005795
2025-06-17 16:58:18,363 - INFO -     - Main Losses (G): Factual=0.6375, Adversarial=0.6907, Decomp=3.4107
2025-06-17 16:58:18,364 - INFO -     - Discriminator Loss (D): 14.7696
2025-06-17 16:58:18,364 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 16:58:18,420 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 16:58:47,475 - INFO - Epoch 40:
2025-06-17 16:58:47,475 - INFO -   Total Loss: 585.743713
2025-06-17 16:58:47,491 - INFO -   PEHE: 0.319670
2025-06-17 16:58:47,491 - INFO -   ATE Error: 0.010788
2025-06-17 16:58:47,491 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6967, Decomp=6.0183
2025-06-17 16:58:47,491 - INFO -     - Discriminator Loss (D): 6.5596
2025-06-17 16:58:47,491 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 16:59:01,907 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-17 16:59:15,957 - INFO - Epoch 60:
2025-06-17 16:59:15,957 - INFO -   Total Loss: 377.881836
2025-06-17 16:59:15,957 - INFO -   PEHE: 0.319674
2025-06-17 16:59:15,957 - INFO -   ATE Error: 0.010894
2025-06-17 16:59:15,957 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6935, Decomp=6.0183
2025-06-17 16:59:15,957 - INFO -     - Discriminator Loss (D): 2.6482
2025-06-17 16:59:15,957 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 16:59:45,884 - INFO - Epoch 80:
2025-06-17 16:59:45,885 - INFO -   Total Loss: 217.748962
2025-06-17 16:59:45,885 - INFO -   PEHE: 0.319669
2025-06-17 16:59:45,886 - INFO -   ATE Error: 0.010742
2025-06-17 16:59:45,887 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6932, Decomp=6.0184
2025-06-17 16:59:45,887 - INFO -     - Discriminator Loss (D): 1.3095
2025-06-17 16:59:45,888 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:00:14,652 - INFO - Epoch 100:
2025-06-17 17:00:14,652 - INFO -   Total Loss: 103.612038
2025-06-17 17:00:14,653 - INFO -   PEHE: 0.319673
2025-06-17 17:00:14,653 - INFO -   ATE Error: 0.010860
2025-06-17 17:00:14,654 - INFO -     - Main Losses (G): Factual=0.6829, Adversarial=0.6931, Decomp=6.0182
2025-06-17 17:00:14,654 - INFO -     - Discriminator Loss (D): 0.8964
2025-06-17 17:00:14,655 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:00:14,729 - INFO - Model saved to ./results\models\stage1_model_epoch_100
2025-06-17 17:00:43,646 - INFO - Epoch 120:
2025-06-17 17:00:43,647 - INFO -   Total Loss: 34.764252
2025-06-17 17:00:43,647 - INFO -   PEHE: 0.319662
2025-06-17 17:00:43,648 - INFO -   ATE Error: 0.010541
2025-06-17 17:00:43,648 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=6.0183
2025-06-17 17:00:43,649 - INFO -     - Discriminator Loss (D): 0.7364
2025-06-17 17:00:43,649 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:01:12,089 - INFO - Epoch 140:
2025-06-17 17:01:12,089 - INFO -   Total Loss: 10.880172
2025-06-17 17:01:12,089 - INFO -   PEHE: 0.319669
2025-06-17 17:01:12,089 - INFO -   ATE Error: 0.010744
2025-06-17 17:01:12,089 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=6.0183
2025-06-17 17:01:12,089 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 17:01:12,089 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:01:26,488 - INFO - Model saved to ./results\models\stage1_model_epoch_150
2025-06-17 17:01:40,891 - INFO - Epoch 160:
2025-06-17 17:01:40,891 - INFO -   Total Loss: 10.828837
2025-06-17 17:01:40,891 - INFO -   PEHE: 0.319670
2025-06-17 17:01:40,891 - INFO -   ATE Error: 0.010779
2025-06-17 17:01:40,891 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=6.0183
2025-06-17 17:01:40,891 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 17:01:40,891 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:02:10,443 - INFO - Epoch 180:
2025-06-17 17:02:10,443 - INFO -   Total Loss: 10.828854
2025-06-17 17:02:10,443 - INFO -   PEHE: 0.319671
2025-06-17 17:02:10,443 - INFO -   ATE Error: 0.010798
2025-06-17 17:02:10,443 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=6.0182
2025-06-17 17:02:10,443 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 17:02:10,443 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:02:39,771 - INFO - Epoch 200:
2025-06-17 17:02:39,772 - INFO -   Total Loss: 10.828844
2025-06-17 17:02:39,773 - INFO -   PEHE: 0.319672
2025-06-17 17:02:39,773 - INFO -   ATE Error: 0.010830
2025-06-17 17:02:39,773 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=6.0183
2025-06-17 17:02:39,774 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 17:02:39,774 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:02:39,847 - INFO - Model saved to ./results\models\stage1_model_epoch_200
2025-06-17 17:03:10,275 - INFO - Epoch 220:
2025-06-17 17:03:10,276 - INFO -   Total Loss: 10.828856
2025-06-17 17:03:10,276 - INFO -   PEHE: 0.319669
2025-06-17 17:03:10,277 - INFO -   ATE Error: 0.010746
2025-06-17 17:03:10,278 - INFO -     - Main Losses (G): Factual=0.6829, Adversarial=0.6931, Decomp=6.0182
2025-06-17 17:03:10,279 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 17:03:10,279 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:03:40,819 - INFO - Epoch 240:
2025-06-17 17:03:40,820 - INFO -   Total Loss: 10.828853
2025-06-17 17:03:40,820 - INFO -   PEHE: 0.319669
2025-06-17 17:03:40,821 - INFO -   ATE Error: 0.010762
2025-06-17 17:03:40,822 - INFO -     - Main Losses (G): Factual=0.6829, Adversarial=0.6931, Decomp=6.0183
2025-06-17 17:03:40,822 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 17:03:40,823 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:03:55,653 - INFO - Model saved to ./results\models\stage1_model_epoch_250
2025-06-17 17:04:10,870 - INFO - Epoch 260:
2025-06-17 17:04:10,885 - INFO -   Total Loss: 10.828834
2025-06-17 17:04:10,885 - INFO -   PEHE: 0.319671
2025-06-17 17:04:10,885 - INFO -   ATE Error: 0.010793
2025-06-17 17:04:10,885 - INFO -     - Main Losses (G): Factual=0.6827, Adversarial=0.6931, Decomp=6.0183
2025-06-17 17:04:10,885 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 17:04:10,885 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:04:41,475 - INFO - Epoch 280:
2025-06-17 17:04:41,475 - INFO -   Total Loss: 10.828838
2025-06-17 17:04:41,475 - INFO -   PEHE: 0.319668
2025-06-17 17:04:41,491 - INFO -   ATE Error: 0.010705
2025-06-17 17:04:41,491 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=6.0183
2025-06-17 17:04:41,491 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 17:04:41,491 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:05:10,554 - INFO - Epoch 300:
2025-06-17 17:05:10,554 - INFO -   Total Loss: 10.828853
2025-06-17 17:05:10,554 - INFO -   PEHE: 0.319673
2025-06-17 17:05:10,554 - INFO -   ATE Error: 0.010856
2025-06-17 17:05:10,554 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=6.0182
2025-06-17 17:05:10,554 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 17:05:10,554 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:05:10,737 - INFO - Model saved to ./results\models\stage1_model_epoch_300
2025-06-17 17:05:39,246 - INFO - Epoch 320:
2025-06-17 17:05:39,246 - INFO -   Total Loss: 10.828841
2025-06-17 17:05:39,246 - INFO -   PEHE: 0.319668
2025-06-17 17:05:39,246 - INFO -   ATE Error: 0.010730
2025-06-17 17:05:39,246 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=6.0184
2025-06-17 17:05:39,246 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 17:05:39,246 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:06:09,967 - INFO - Epoch 340:
2025-06-17 17:06:09,967 - INFO -   Total Loss: 10.828861
2025-06-17 17:06:09,973 - INFO -   PEHE: 0.319671
2025-06-17 17:06:09,973 - INFO -   ATE Error: 0.010809
2025-06-17 17:06:09,974 - INFO -     - Main Losses (G): Factual=0.6829, Adversarial=0.6931, Decomp=6.0184
2025-06-17 17:06:09,974 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 17:06:09,975 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:06:26,422 - INFO - Model saved to ./results\models\stage1_model_epoch_350
2025-06-17 17:06:42,592 - INFO - Epoch 360:
2025-06-17 17:06:42,592 - INFO -   Total Loss: 10.828868
2025-06-17 17:06:42,592 - INFO -   PEHE: 0.319670
2025-06-17 17:06:42,592 - INFO -   ATE Error: 0.010777
2025-06-17 17:06:42,592 - INFO -     - Main Losses (G): Factual=0.6829, Adversarial=0.6931, Decomp=6.0183
2025-06-17 17:06:42,592 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 17:06:42,592 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:07:11,489 - INFO - Epoch 380:
2025-06-17 17:07:11,489 - INFO -   Total Loss: 10.828857
2025-06-17 17:07:11,489 - INFO -   PEHE: 0.319670
2025-06-17 17:07:11,489 - INFO -   ATE Error: 0.010791
2025-06-17 17:07:11,489 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=6.0183
2025-06-17 17:07:11,489 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 17:07:11,489 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:07:38,934 - INFO - Stage 1 training completed!
2025-06-17 17:07:39,363 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-06-17 17:07:39,363 - INFO - Stage 2 training data shapes:
2025-06-17 17:07:39,363 - INFO -   x_train: (9120, 30)
2025-06-17 17:07:39,363 - INFO -   y_bar_target: (9120, 2)
2025-06-17 17:08:31,977 - INFO - Stage 2 training completed!
2025-06-17 17:08:32,207 - INFO - Stage2 model save skipped for quick test
2025-06-17 17:30:36,558 - INFO - Starting Stage 1 training...
2025-06-17 17:30:36,558 - INFO - Training samples: 9120
2025-06-17 17:30:36,559 - INFO - Test samples: 2280
2025-06-17 17:30:36,559 - INFO - Sample weights initialized: shape=(9120, 1)
2025-06-17 17:30:49,330 - INFO - Epoch 0:
2025-06-17 17:30:49,330 - INFO -   Total Loss: 4694.783203
2025-06-17 17:30:49,330 - INFO -   PEHE: 0.350659
2025-06-17 17:30:49,330 - INFO -   ATE Error: 0.128796
2025-06-17 17:30:49,330 - INFO -     - Main Losses (G): Factual=0.7121, Adversarial=0.6829, Decomp=146.7076
2025-06-17 17:30:49,330 - INFO -     - Discriminator Loss (D): 29.2337
2025-06-17 17:30:49,330 - INFO -     - Balance Loss (Weights): 0.2437
2025-06-17 17:30:49,450 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 17:30:49,519 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-17 17:31:18,326 - INFO - Epoch 20:
2025-06-17 17:31:18,327 - INFO -   Total Loss: 889.894287
2025-06-17 17:31:18,328 - INFO -   PEHE: 0.319538
2025-06-17 17:31:18,328 - INFO -   ATE Error: 0.005640
2025-06-17 17:31:18,329 - INFO -     - Main Losses (G): Factual=0.6374, Adversarial=0.6907, Decomp=5.2872
2025-06-17 17:31:18,329 - INFO -     - Discriminator Loss (D): 14.7696
2025-06-17 17:31:18,330 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:31:18,406 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 17:31:46,815 - INFO - Epoch 40:
2025-06-17 17:31:46,815 - INFO -   Total Loss: 591.165100
2025-06-17 17:31:46,815 - INFO -   PEHE: 0.319670
2025-06-17 17:31:46,815 - INFO -   ATE Error: 0.010789
2025-06-17 17:31:46,815 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6967, Decomp=7.6918
2025-06-17 17:31:46,827 - INFO -     - Discriminator Loss (D): 6.5596
2025-06-17 17:31:46,827 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:32:01,327 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-17 17:32:15,509 - INFO - Epoch 60:
2025-06-17 17:32:15,509 - INFO -   Total Loss: 383.303284
2025-06-17 17:32:15,509 - INFO -   PEHE: 0.319674
2025-06-17 17:32:15,509 - INFO -   ATE Error: 0.010894
2025-06-17 17:32:15,509 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6935, Decomp=7.6918
2025-06-17 17:32:15,509 - INFO -     - Discriminator Loss (D): 2.6482
2025-06-17 17:32:15,509 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:32:43,915 - INFO - Epoch 80:
2025-06-17 17:32:43,915 - INFO -   Total Loss: 223.170471
2025-06-17 17:32:43,915 - INFO -   PEHE: 0.319669
2025-06-17 17:32:43,915 - INFO -   ATE Error: 0.010742
2025-06-17 17:32:43,915 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6932, Decomp=7.6918
2025-06-17 17:32:43,915 - INFO -     - Discriminator Loss (D): 1.3095
2025-06-17 17:32:43,915 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:33:12,951 - INFO - Epoch 100:
2025-06-17 17:33:12,951 - INFO -   Total Loss: 109.033554
2025-06-17 17:33:12,951 - INFO -   PEHE: 0.319673
2025-06-17 17:33:12,951 - INFO -   ATE Error: 0.010860
2025-06-17 17:33:12,951 - INFO -     - Main Losses (G): Factual=0.6829, Adversarial=0.6931, Decomp=7.6918
2025-06-17 17:33:12,955 - INFO -     - Discriminator Loss (D): 0.8964
2025-06-17 17:33:12,956 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:33:13,043 - INFO - Model saved to ./results\models\stage1_model_epoch_100
2025-06-17 17:33:41,869 - INFO - Epoch 120:
2025-06-17 17:33:41,869 - INFO -   Total Loss: 40.185722
2025-06-17 17:33:41,869 - INFO -   PEHE: 0.319662
2025-06-17 17:33:41,869 - INFO -   ATE Error: 0.010541
2025-06-17 17:33:41,869 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=7.6920
2025-06-17 17:33:41,869 - INFO -     - Discriminator Loss (D): 0.7364
2025-06-17 17:33:41,869 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:34:10,970 - INFO - Epoch 140:
2025-06-17 17:34:10,970 - INFO -   Total Loss: 16.301649
2025-06-17 17:34:10,970 - INFO -   PEHE: 0.319669
2025-06-17 17:34:10,970 - INFO -   ATE Error: 0.010744
2025-06-17 17:34:10,970 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=7.6917
2025-06-17 17:34:10,970 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 17:34:10,970 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:34:25,473 - INFO - Model saved to ./results\models\stage1_model_epoch_150
2025-06-17 17:34:39,801 - INFO - Epoch 160:
2025-06-17 17:34:39,801 - INFO -   Total Loss: 16.250309
2025-06-17 17:34:39,801 - INFO -   PEHE: 0.319670
2025-06-17 17:34:39,801 - INFO -   ATE Error: 0.010779
2025-06-17 17:34:39,801 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=7.6918
2025-06-17 17:34:39,801 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 17:34:39,801 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:35:08,802 - INFO - Epoch 180:
2025-06-17 17:35:08,802 - INFO -   Total Loss: 16.250332
2025-06-17 17:35:08,802 - INFO -   PEHE: 0.319671
2025-06-17 17:35:08,802 - INFO -   ATE Error: 0.010798
2025-06-17 17:35:08,802 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=7.6918
2025-06-17 17:35:08,802 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 17:35:08,802 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:35:37,767 - INFO - Epoch 200:
2025-06-17 17:35:37,767 - INFO -   Total Loss: 16.250315
2025-06-17 17:35:37,767 - INFO -   PEHE: 0.319672
2025-06-17 17:35:37,767 - INFO -   ATE Error: 0.010830
2025-06-17 17:35:37,767 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=7.6919
2025-06-17 17:35:37,767 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 17:35:37,767 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:35:37,814 - INFO - Model saved to ./results\models\stage1_model_epoch_200
2025-06-17 17:36:06,549 - INFO - Epoch 220:
2025-06-17 17:36:06,549 - INFO -   Total Loss: 16.250332
2025-06-17 17:36:06,549 - INFO -   PEHE: 0.319669
2025-06-17 17:36:06,549 - INFO -   ATE Error: 0.010746
2025-06-17 17:36:06,549 - INFO -     - Main Losses (G): Factual=0.6829, Adversarial=0.6931, Decomp=7.6918
2025-06-17 17:36:06,549 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 17:36:06,549 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:36:35,503 - INFO - Epoch 240:
2025-06-17 17:36:35,503 - INFO -   Total Loss: 16.250332
2025-06-17 17:36:35,503 - INFO -   PEHE: 0.319669
2025-06-17 17:36:35,503 - INFO -   ATE Error: 0.010762
2025-06-17 17:36:35,503 - INFO -     - Main Losses (G): Factual=0.6829, Adversarial=0.6931, Decomp=7.6919
2025-06-17 17:36:35,503 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 17:36:35,503 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:36:50,008 - INFO - Model saved to ./results\models\stage1_model_epoch_250
2025-06-17 17:37:04,318 - INFO - Epoch 260:
2025-06-17 17:37:04,331 - INFO -   Total Loss: 16.250299
2025-06-17 17:37:04,332 - INFO -   PEHE: 0.319671
2025-06-17 17:37:04,332 - INFO -   ATE Error: 0.010793
2025-06-17 17:37:04,332 - INFO -     - Main Losses (G): Factual=0.6827, Adversarial=0.6931, Decomp=7.6916
2025-06-17 17:37:04,332 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 17:37:04,332 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:37:34,156 - INFO - Epoch 280:
2025-06-17 17:37:34,156 - INFO -   Total Loss: 16.250307
2025-06-17 17:37:34,156 - INFO -   PEHE: 0.319668
2025-06-17 17:37:34,156 - INFO -   ATE Error: 0.010705
2025-06-17 17:37:34,156 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=7.6919
2025-06-17 17:37:34,156 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 17:37:34,156 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:38:04,646 - INFO - Epoch 300:
2025-06-17 17:38:04,646 - INFO -   Total Loss: 16.250324
2025-06-17 17:38:04,646 - INFO -   PEHE: 0.319673
2025-06-17 17:38:04,646 - INFO -   ATE Error: 0.010856
2025-06-17 17:38:04,646 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=7.6918
2025-06-17 17:38:04,646 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 17:38:04,646 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:38:04,725 - INFO - Model saved to ./results\models\stage1_model_epoch_300
2025-06-17 17:45:50,856 - INFO - Starting Stage 1 training...
2025-06-17 17:45:50,857 - INFO - Training samples: 9120
2025-06-17 17:45:50,857 - INFO - Test samples: 2280
2025-06-17 17:45:50,857 - INFO - Sample weights initialized: shape=(9120, 1)
2025-06-17 17:46:04,208 - INFO - Epoch 0:
2025-06-17 17:46:04,208 - INFO -   Total Loss: 4700.932617
2025-06-17 17:46:04,209 - INFO -   PEHE: 0.347346
2025-06-17 17:46:04,209 - INFO -   ATE Error: 0.119263
2025-06-17 17:46:04,209 - INFO -     - Main Losses (G): Factual=0.7145, Adversarial=0.6823, Decomp=146.7077
2025-06-17 17:46:04,210 - INFO -     - Discriminator Loss (D): 29.2345
2025-06-17 17:46:04,210 - INFO -     - Balance Loss (Weights): 0.2437
2025-06-17 17:46:04,321 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 17:46:04,398 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-17 17:46:32,991 - INFO - Epoch 20:
2025-06-17 17:46:32,992 - INFO -   Total Loss: 896.107605
2025-06-17 17:46:32,992 - INFO -   PEHE: 0.319922
2025-06-17 17:46:32,994 - INFO -   ATE Error: 0.016660
2025-06-17 17:46:32,994 - INFO -     - Main Losses (G): Factual=0.6458, Adversarial=0.6903, Decomp=5.2873
2025-06-17 17:46:32,994 - INFO -     - Discriminator Loss (D): 14.7701
2025-06-17 17:46:32,994 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:46:33,071 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 17:47:01,689 - INFO - Epoch 40:
2025-06-17 17:47:01,690 - INFO -   Total Loss: 597.434448
2025-06-17 17:47:01,690 - INFO -   PEHE: 0.319685
2025-06-17 17:47:01,691 - INFO -   ATE Error: 0.011218
2025-06-17 17:47:01,691 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6967, Decomp=7.6918
2025-06-17 17:47:01,691 - INFO -     - Discriminator Loss (D): 6.5596
2025-06-17 17:47:01,692 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:47:01,759 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 17:47:16,015 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-17 17:47:30,233 - INFO - Epoch 60:
2025-06-17 17:47:30,233 - INFO -   Total Loss: 389.544159
2025-06-17 17:47:30,234 - INFO -   PEHE: 0.319672
2025-06-17 17:47:30,234 - INFO -   ATE Error: 0.010845
2025-06-17 17:47:30,235 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6935, Decomp=7.6918
2025-06-17 17:47:30,235 - INFO -     - Discriminator Loss (D): 2.6482
2025-06-17 17:47:30,236 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:47:30,315 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 17:47:58,764 - INFO - Epoch 80:
2025-06-17 17:47:58,765 - INFO -   Total Loss: 229.408325
2025-06-17 17:47:58,765 - INFO -   PEHE: 0.319669
2025-06-17 17:47:58,765 - INFO -   ATE Error: 0.010741
2025-06-17 17:47:58,766 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6932, Decomp=7.6919
2025-06-17 17:47:58,766 - INFO -     - Discriminator Loss (D): 1.3095
2025-06-17 17:47:58,766 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:47:58,846 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 17:48:27,563 - INFO - Epoch 100:
2025-06-17 17:48:27,564 - INFO -   Total Loss: 115.271515
2025-06-17 17:48:27,564 - INFO -   PEHE: 0.319673
2025-06-17 17:48:27,565 - INFO -   ATE Error: 0.010860
2025-06-17 17:48:27,565 - INFO -     - Main Losses (G): Factual=0.6829, Adversarial=0.6931, Decomp=7.6919
2025-06-17 17:48:27,565 - INFO -     - Discriminator Loss (D): 0.8964
2025-06-17 17:48:27,566 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:48:27,629 - INFO - Model saved to ./results\models\stage1_model_epoch_100
2025-06-17 17:48:56,334 - INFO - Epoch 120:
2025-06-17 17:48:56,335 - INFO -   Total Loss: 46.423866
2025-06-17 17:48:56,335 - INFO -   PEHE: 0.319662
2025-06-17 17:48:56,336 - INFO -   ATE Error: 0.010541
2025-06-17 17:48:56,336 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=7.6918
2025-06-17 17:48:56,337 - INFO -     - Discriminator Loss (D): 0.7364
2025-06-17 17:48:56,337 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:48:56,412 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 17:49:24,902 - INFO - Epoch 140:
2025-06-17 17:49:24,903 - INFO -   Total Loss: 22.539967
2025-06-17 17:49:24,904 - INFO -   PEHE: 0.319669
2025-06-17 17:49:24,904 - INFO -   ATE Error: 0.010744
2025-06-17 17:49:24,904 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=7.6917
2025-06-17 17:49:24,905 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 17:49:24,905 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:49:39,543 - INFO - Model saved to ./results\models\stage1_model_epoch_150
2025-06-17 17:49:53,791 - INFO - Epoch 160:
2025-06-17 17:49:53,791 - INFO -   Total Loss: 22.488647
2025-06-17 17:49:53,792 - INFO -   PEHE: 0.319670
2025-06-17 17:49:53,792 - INFO -   ATE Error: 0.010779
2025-06-17 17:49:53,792 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=7.6919
2025-06-17 17:49:53,793 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 17:49:53,793 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:50:22,751 - INFO - Epoch 180:
2025-06-17 17:50:22,752 - INFO -   Total Loss: 22.488674
2025-06-17 17:50:22,752 - INFO -   PEHE: 0.319671
2025-06-17 17:50:22,752 - INFO -   ATE Error: 0.010798
2025-06-17 17:50:22,753 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=7.6917
2025-06-17 17:50:22,753 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 17:50:22,753 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:50:51,351 - INFO - Epoch 200:
2025-06-17 17:50:51,351 - INFO -   Total Loss: 22.488653
2025-06-17 17:50:51,351 - INFO -   PEHE: 0.319672
2025-06-17 17:50:51,351 - INFO -   ATE Error: 0.010830
2025-06-17 17:50:51,351 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=7.6918
2025-06-17 17:50:51,351 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 17:50:51,361 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:50:51,445 - INFO - Model saved to ./results\models\stage1_model_epoch_200
2025-06-17 17:57:39,326 - INFO - Starting Stage 1 training...
2025-06-17 17:57:39,327 - INFO - Training samples: 9120
2025-06-17 17:57:39,327 - INFO - Test samples: 2280
2025-06-17 17:57:39,328 - INFO - Sample weights initialized: shape=(9120, 1)
2025-06-17 17:57:44,552 - INFO - Epoch 0:
2025-06-17 17:57:44,553 - INFO -   Total Loss: 4700.932617
2025-06-17 17:57:44,553 - INFO -   PEHE: 0.347345
2025-06-17 17:57:44,553 - INFO -   ATE Error: 0.119261
2025-06-17 17:57:44,553 - INFO -     - Main Losses (G): Factual=0.7145, Adversarial=0.6823, Decomp=146.7077
2025-06-17 17:57:44,553 - INFO -     - Discriminator Loss (D): 29.2345
2025-06-17 17:57:44,553 - INFO -     - Balance Loss (Weights): 0.2437
2025-06-17 17:57:44,584 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 17:57:44,611 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-17 17:58:43,510 - INFO - Epoch 100:
2025-06-17 17:58:43,510 - INFO -   Total Loss: 115.271523
2025-06-17 17:58:43,510 - INFO -   PEHE: 0.319673
2025-06-17 17:58:43,510 - INFO -   ATE Error: 0.010860
2025-06-17 17:58:43,510 - INFO -     - Main Losses (G): Factual=0.6829, Adversarial=0.6931, Decomp=7.6918
2025-06-17 17:58:43,510 - INFO -     - Discriminator Loss (D): 0.8964
2025-06-17 17:58:43,510 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 17:58:43,545 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 18:00:17,674 - INFO - Epoch 200:
2025-06-17 18:00:17,675 - INFO -   Total Loss: 22.488649
2025-06-17 18:00:17,675 - INFO -   PEHE: 0.319672
2025-06-17 18:00:17,675 - INFO -   ATE Error: 0.010830
2025-06-17 18:00:17,675 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=7.6917
2025-06-17 18:00:17,675 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 18:00:17,676 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 18:00:17,757 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 18:00:17,811 - INFO - Model saved to ./results\models\stage1_model_epoch_200
2025-06-17 18:02:15,754 - INFO - Epoch 300:
2025-06-17 18:02:15,755 - INFO -   Total Loss: 22.488663
2025-06-17 18:02:15,755 - INFO -   PEHE: 0.319673
2025-06-17 18:02:15,755 - INFO -   ATE Error: 0.010856
2025-06-17 18:02:15,755 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=7.6918
2025-06-17 18:02:15,755 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 18:02:15,756 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 18:03:16,981 - INFO - Epoch 400:
2025-06-17 18:03:16,981 - INFO -   Total Loss: 22.488674
2025-06-17 18:03:16,981 - INFO -   PEHE: 0.319669
2025-06-17 18:03:16,981 - INFO -   ATE Error: 0.010759
2025-06-17 18:03:16,981 - INFO -     - Main Losses (G): Factual=0.6829, Adversarial=0.6931, Decomp=7.6919
2025-06-17 18:03:16,981 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 18:03:16,981 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 18:03:17,028 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 18:03:17,062 - INFO - Model saved to ./results\models\stage1_model_epoch_400
2025-06-17 18:12:59,576 - INFO - Starting Stage 1 training...
2025-06-17 18:12:59,577 - INFO - Training samples: 9120
2025-06-17 18:12:59,577 - INFO - Test samples: 2280
2025-06-17 18:12:59,578 - INFO - Sample weights initialized: shape=(9120, 1)
2025-06-17 18:13:12,194 - INFO - Epoch 0:
2025-06-17 18:13:12,195 - INFO -   Total Loss: 4053.490723
2025-06-17 18:13:12,195 - INFO -   PEHE: 0.334966
2025-06-17 18:13:12,196 - INFO -   ATE Error: 0.096483
2025-06-17 18:13:12,196 - INFO -     - Main Losses (G): Factual=0.7052, Adversarial=0.6807, Decomp=111.4249
2025-06-17 18:13:12,197 - INFO -     - Discriminator Loss (D): 29.2363
2025-06-17 18:13:12,197 - INFO -     - Balance Loss (Weights): 0.1543
2025-06-17 18:13:12,269 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 18:13:12,360 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-17 18:13:40,905 - INFO - Epoch 20:
2025-06-17 18:13:40,906 - INFO -   Total Loss: 234.865234
2025-06-17 18:13:40,906 - INFO -   PEHE: 0.319867
2025-06-17 18:13:40,906 - INFO -   ATE Error: 0.015553
2025-06-17 18:13:40,907 - INFO -     - Main Losses (G): Factual=0.6842, Adversarial=0.6899, Decomp=7.6924
2025-06-17 18:13:40,907 - INFO -     - Discriminator Loss (D): 14.7706
2025-06-17 18:13:40,907 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 18:13:40,962 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 18:14:09,267 - INFO - Epoch 40:
2025-06-17 18:14:09,268 - INFO -   Total Loss: 28.392195
2025-06-17 18:14:09,268 - INFO -   PEHE: 0.319679
2025-06-17 18:14:09,268 - INFO -   ATE Error: 0.011038
2025-06-17 18:14:09,269 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6967, Decomp=7.6918
2025-06-17 18:14:09,269 - INFO -     - Discriminator Loss (D): 6.5596
2025-06-17 18:14:09,270 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 18:14:09,355 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 18:14:23,698 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-17 18:14:38,067 - INFO - Epoch 60:
2025-06-17 18:14:38,067 - INFO -   Total Loss: 24.448265
2025-06-17 18:14:38,068 - INFO -   PEHE: 0.319680
2025-06-17 18:14:38,068 - INFO -   ATE Error: 0.011082
2025-06-17 18:14:38,068 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6935, Decomp=7.6917
2025-06-17 18:14:38,069 - INFO -     - Discriminator Loss (D): 2.6482
2025-06-17 18:14:38,069 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 18:15:06,073 - INFO - Epoch 80:
2025-06-17 18:15:06,074 - INFO -   Total Loss: 23.106825
2025-06-17 18:15:06,074 - INFO -   PEHE: 0.319666
2025-06-17 18:15:06,074 - INFO -   ATE Error: 0.010653
2025-06-17 18:15:06,074 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6932, Decomp=7.6917
2025-06-17 18:15:06,075 - INFO -     - Discriminator Loss (D): 1.3095
2025-06-17 18:15:06,075 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 18:15:06,143 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 18:15:34,705 - INFO - Epoch 100:
2025-06-17 18:15:34,706 - INFO -   Total Loss: 22.693899
2025-06-17 18:15:34,707 - INFO -   PEHE: 0.319671
2025-06-17 18:15:34,707 - INFO -   ATE Error: 0.010818
2025-06-17 18:15:34,708 - INFO -     - Main Losses (G): Factual=0.6829, Adversarial=0.6931, Decomp=7.6925
2025-06-17 18:15:34,708 - INFO -     - Discriminator Loss (D): 0.8964
2025-06-17 18:15:34,708 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 18:15:34,782 - INFO - Model saved to ./results\models\stage1_model_epoch_100
2025-06-17 18:16:03,281 - INFO - Epoch 120:
2025-06-17 18:16:03,282 - INFO -   Total Loss: 22.534052
2025-06-17 18:16:03,282 - INFO -   PEHE: 0.319645
2025-06-17 18:16:03,283 - INFO -   ATE Error: 0.010000
2025-06-17 18:16:03,283 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=7.6918
2025-06-17 18:16:03,283 - INFO -     - Discriminator Loss (D): 0.7364
2025-06-17 18:16:03,284 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 18:16:03,361 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 18:16:31,967 - INFO - Epoch 140:
2025-06-17 18:16:31,967 - INFO -   Total Loss: 22.490845
2025-06-17 18:16:31,968 - INFO -   PEHE: 0.319654
2025-06-17 18:16:31,968 - INFO -   ATE Error: 0.010286
2025-06-17 18:16:31,968 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=7.6920
2025-06-17 18:16:31,969 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 18:16:31,969 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 18:16:46,349 - INFO - Model saved to ./results\models\stage1_model_epoch_150
2025-06-17 18:17:00,011 - INFO - Epoch 160:
2025-06-17 18:17:00,012 - INFO -   Total Loss: 22.490824
2025-06-17 18:17:00,013 - INFO -   PEHE: 0.319660
2025-06-17 18:17:00,014 - INFO -   ATE Error: 0.010478
2025-06-17 18:17:00,015 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=7.6919
2025-06-17 18:17:00,016 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 18:17:00,016 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 18:17:29,361 - INFO - Epoch 180:
2025-06-17 18:17:29,362 - INFO -   Total Loss: 22.490862
2025-06-17 18:17:29,362 - INFO -   PEHE: 0.319676
2025-06-17 18:17:29,363 - INFO -   ATE Error: 0.010948
2025-06-17 18:17:29,363 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=7.6926
2025-06-17 18:17:29,364 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 18:17:29,364 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 18:17:59,771 - INFO - Epoch 200:
2025-06-17 18:17:59,772 - INFO -   Total Loss: 22.490824
2025-06-17 18:17:59,772 - INFO -   PEHE: 0.319674
2025-06-17 18:17:59,773 - INFO -   ATE Error: 0.010902
2025-06-17 18:17:59,774 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=7.6915
2025-06-17 18:17:59,774 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 18:17:59,774 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 18:17:59,849 - INFO - Model saved to ./results\models\stage1_model_epoch_200
2025-06-17 18:18:29,445 - INFO - Epoch 220:
2025-06-17 18:18:29,446 - INFO -   Total Loss: 22.490850
2025-06-17 18:18:29,446 - INFO -   PEHE: 0.319652
2025-06-17 18:18:29,447 - INFO -   ATE Error: 0.010234
2025-06-17 18:18:29,447 - INFO -     - Main Losses (G): Factual=0.6829, Adversarial=0.6931, Decomp=7.6915
2025-06-17 18:18:29,447 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 18:18:29,448 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 18:18:59,444 - INFO - Epoch 240:
2025-06-17 18:18:59,444 - INFO -   Total Loss: 22.490868
2025-06-17 18:18:59,445 - INFO -   PEHE: 0.319664
2025-06-17 18:18:59,445 - INFO -   ATE Error: 0.010604
2025-06-17 18:18:59,446 - INFO -     - Main Losses (G): Factual=0.6829, Adversarial=0.6931, Decomp=7.6919
2025-06-17 18:18:59,446 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 18:18:59,446 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 18:19:14,631 - INFO - Model saved to ./results\models\stage1_model_epoch_250
2025-06-17 18:19:29,351 - INFO - Epoch 260:
2025-06-17 18:19:29,352 - INFO -   Total Loss: 22.490805
2025-06-17 18:19:29,353 - INFO -   PEHE: 0.319672
2025-06-17 18:19:29,353 - INFO -   ATE Error: 0.010834
2025-06-17 18:19:29,354 - INFO -     - Main Losses (G): Factual=0.6827, Adversarial=0.6931, Decomp=7.6926
2025-06-17 18:19:29,354 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 18:19:29,355 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 18:19:59,290 - INFO - Epoch 280:
2025-06-17 18:19:59,291 - INFO -   Total Loss: 22.490818
2025-06-17 18:19:59,291 - INFO -   PEHE: 0.319664
2025-06-17 18:19:59,291 - INFO -   ATE Error: 0.010597
2025-06-17 18:19:59,292 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=7.6914
2025-06-17 18:19:59,292 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 18:19:59,293 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 18:20:28,588 - INFO - Epoch 300:
2025-06-17 18:20:28,588 - INFO -   Total Loss: 22.490850
2025-06-17 18:20:28,589 - INFO -   PEHE: 0.319680
2025-06-17 18:20:28,589 - INFO -   ATE Error: 0.011079
2025-06-17 18:20:28,590 - INFO -     - Main Losses (G): Factual=0.6829, Adversarial=0.6931, Decomp=7.6916
2025-06-17 18:20:28,590 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 18:20:28,590 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 18:20:28,674 - INFO - Model saved to ./results\models\stage1_model_epoch_300
2025-06-17 18:20:57,554 - INFO - Epoch 320:
2025-06-17 18:20:57,555 - INFO -   Total Loss: 22.490839
2025-06-17 18:20:57,555 - INFO -   PEHE: 0.319658
2025-06-17 18:20:57,555 - INFO -   ATE Error: 0.010422
2025-06-17 18:20:57,556 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=7.6915
2025-06-17 18:20:57,556 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 18:20:57,556 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 18:21:26,740 - INFO - Epoch 340:
2025-06-17 18:21:26,741 - INFO -   Total Loss: 22.490860
2025-06-17 18:21:26,742 - INFO -   PEHE: 0.319666
2025-06-17 18:21:26,742 - INFO -   ATE Error: 0.010653
2025-06-17 18:21:26,743 - INFO -     - Main Losses (G): Factual=0.6830, Adversarial=0.6931, Decomp=7.6919
2025-06-17 18:21:26,743 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 18:21:26,744 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 18:21:41,343 - INFO - Model saved to ./results\models\stage1_model_epoch_350
2025-06-17 18:21:55,712 - INFO - Epoch 360:
2025-06-17 18:21:55,713 - INFO -   Total Loss: 22.490898
2025-06-17 18:21:55,713 - INFO -   PEHE: 0.319678
2025-06-17 18:21:55,713 - INFO -   ATE Error: 0.011009
2025-06-17 18:21:55,714 - INFO -     - Main Losses (G): Factual=0.6830, Adversarial=0.6931, Decomp=7.6916
2025-06-17 18:21:55,714 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 18:21:55,715 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 18:22:24,845 - INFO - Epoch 380:
2025-06-17 18:22:24,845 - INFO -   Total Loss: 22.490854
2025-06-17 18:22:24,846 - INFO -   PEHE: 0.319679
2025-06-17 18:22:24,846 - INFO -   ATE Error: 0.011037
2025-06-17 18:22:24,847 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=7.6931
2025-06-17 18:22:24,848 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 18:22:24,848 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 18:22:52,686 - INFO - Stage 1 training completed!
2025-06-17 18:22:53,088 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-06-17 18:22:53,088 - INFO - Stage 2 training data shapes:
2025-06-17 18:22:53,089 - INFO -   x_train: (9120, 30)
2025-06-17 18:22:53,090 - INFO -   y_bar_target: (9120, 2)
2025-06-17 18:23:44,600 - INFO - Stage 2 training completed!
2025-06-17 18:23:44,858 - INFO - Stage2 model save skipped for quick test
2025-06-17 20:22:40,955 - INFO - Starting Stage 1 training...
2025-06-17 20:22:40,956 - INFO - Training samples: 9120
2025-06-17 20:22:40,957 - INFO - Test samples: 2280
2025-06-17 20:22:40,957 - INFO - Sample weights initialized: shape=(9120, 1)
2025-06-17 20:22:54,294 - INFO - Epoch 0:
2025-06-17 20:22:54,294 - INFO -   Total Loss: 4053.490723
2025-06-17 20:22:54,294 - INFO -   PEHE: 0.334966
2025-06-17 20:22:54,294 - INFO -   ATE Error: 0.096483
2025-06-17 20:22:54,294 - INFO -     - Main Losses (G): Factual=0.7052, Adversarial=0.6807, Decomp=111.4249
2025-06-17 20:22:54,294 - INFO -     - Discriminator Loss (D): 29.2363
2025-06-17 20:22:54,294 - INFO -     - Balance Loss (Weights): 0.1543
2025-06-17 20:22:54,372 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 20:22:54,435 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-17 20:23:23,536 - INFO - Epoch 20:
2025-06-17 20:23:23,536 - INFO -   Total Loss: 234.865234
2025-06-17 20:23:23,536 - INFO -   PEHE: 0.319867
2025-06-17 20:23:23,536 - INFO -   ATE Error: 0.015553
2025-06-17 20:23:23,536 - INFO -     - Main Losses (G): Factual=0.6842, Adversarial=0.6899, Decomp=7.6924
2025-06-17 20:23:23,536 - INFO -     - Discriminator Loss (D): 14.7706
2025-06-17 20:23:23,536 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 20:23:23,626 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 20:23:53,119 - INFO - Epoch 40:
2025-06-17 20:23:53,119 - INFO -   Total Loss: 28.392195
2025-06-17 20:23:53,119 - INFO -   PEHE: 0.319679
2025-06-17 20:23:53,119 - INFO -   ATE Error: 0.011038
2025-06-17 20:23:53,119 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6967, Decomp=7.6918
2025-06-17 20:23:53,119 - INFO -     - Discriminator Loss (D): 6.5596
2025-06-17 20:23:53,119 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 20:23:53,198 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 20:24:07,796 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-17 20:24:22,687 - INFO - Epoch 60:
2025-06-17 20:24:22,687 - INFO -   Total Loss: 24.448265
2025-06-17 20:24:22,687 - INFO -   PEHE: 0.319680
2025-06-17 20:24:22,687 - INFO -   ATE Error: 0.011082
2025-06-17 20:24:22,687 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6935, Decomp=7.6917
2025-06-17 20:24:22,687 - INFO -     - Discriminator Loss (D): 2.6482
2025-06-17 20:24:22,687 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 20:24:51,724 - INFO - Epoch 80:
2025-06-17 20:24:51,724 - INFO -   Total Loss: 23.106825
2025-06-17 20:24:51,724 - INFO -   PEHE: 0.319666
2025-06-17 20:24:51,724 - INFO -   ATE Error: 0.010653
2025-06-17 20:24:51,724 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6932, Decomp=7.6917
2025-06-17 20:24:51,724 - INFO -     - Discriminator Loss (D): 1.3095
2025-06-17 20:24:51,724 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 20:24:51,818 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 20:25:21,097 - INFO - Epoch 100:
2025-06-17 20:25:21,098 - INFO -   Total Loss: 22.693899
2025-06-17 20:25:21,098 - INFO -   PEHE: 0.319671
2025-06-17 20:25:21,099 - INFO -   ATE Error: 0.010818
2025-06-17 20:25:21,099 - INFO -     - Main Losses (G): Factual=0.6829, Adversarial=0.6931, Decomp=7.6925
2025-06-17 20:25:21,099 - INFO -     - Discriminator Loss (D): 0.8964
2025-06-17 20:25:21,101 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 20:25:21,162 - INFO - Model saved to ./results\models\stage1_model_epoch_100
2025-06-17 20:25:52,393 - INFO - Epoch 120:
2025-06-17 20:25:52,393 - INFO -   Total Loss: 22.534052
2025-06-17 20:25:52,393 - INFO -   PEHE: 0.319645
2025-06-17 20:25:52,393 - INFO -   ATE Error: 0.010000
2025-06-17 20:25:52,395 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=7.6918
2025-06-17 20:25:52,396 - INFO -     - Discriminator Loss (D): 0.7364
2025-06-17 20:25:52,396 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 20:25:52,506 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-17 20:26:22,428 - INFO - Epoch 140:
2025-06-17 20:26:22,428 - INFO -   Total Loss: 22.490845
2025-06-17 20:26:22,428 - INFO -   PEHE: 0.319654
2025-06-17 20:26:22,428 - INFO -   ATE Error: 0.010286
2025-06-17 20:26:22,428 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=7.6920
2025-06-17 20:26:22,428 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 20:26:22,428 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 20:26:37,556 - INFO - Model saved to ./results\models\stage1_model_epoch_150
2025-06-17 20:26:52,075 - INFO - Epoch 160:
2025-06-17 20:26:52,075 - INFO -   Total Loss: 22.490824
2025-06-17 20:26:52,075 - INFO -   PEHE: 0.319660
2025-06-17 20:26:52,075 - INFO -   ATE Error: 0.010478
2025-06-17 20:26:52,075 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=7.6919
2025-06-17 20:26:52,075 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 20:26:52,075 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 20:27:21,054 - INFO - Epoch 180:
2025-06-17 20:27:21,054 - INFO -   Total Loss: 22.490862
2025-06-17 20:27:21,054 - INFO -   PEHE: 0.319676
2025-06-17 20:27:21,054 - INFO -   ATE Error: 0.010948
2025-06-17 20:27:21,054 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=7.6926
2025-06-17 20:27:21,054 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 20:27:21,054 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 20:27:50,940 - INFO - Epoch 200:
2025-06-17 20:27:50,940 - INFO -   Total Loss: 22.490824
2025-06-17 20:27:50,940 - INFO -   PEHE: 0.319674
2025-06-17 20:27:50,940 - INFO -   ATE Error: 0.010902
2025-06-17 20:27:50,945 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=7.6915
2025-06-17 20:27:50,945 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 20:27:50,945 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 20:27:51,004 - INFO - Model saved to ./results\models\stage1_model_epoch_200
2025-06-17 20:28:20,424 - INFO - Epoch 220:
2025-06-17 20:28:20,424 - INFO -   Total Loss: 22.490850
2025-06-17 20:28:20,424 - INFO -   PEHE: 0.319652
2025-06-17 20:28:20,424 - INFO -   ATE Error: 0.010234
2025-06-17 20:28:20,424 - INFO -     - Main Losses (G): Factual=0.6829, Adversarial=0.6931, Decomp=7.6915
2025-06-17 20:28:20,424 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 20:28:20,424 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 20:28:49,874 - INFO - Epoch 240:
2025-06-17 20:28:49,874 - INFO -   Total Loss: 22.490868
2025-06-17 20:28:49,874 - INFO -   PEHE: 0.319664
2025-06-17 20:28:49,874 - INFO -   ATE Error: 0.010604
2025-06-17 20:28:49,874 - INFO -     - Main Losses (G): Factual=0.6829, Adversarial=0.6931, Decomp=7.6919
2025-06-17 20:28:49,874 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 20:28:49,874 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 20:29:05,015 - INFO - Model saved to ./results\models\stage1_model_epoch_250
2025-06-17 20:29:20,683 - INFO - Epoch 260:
2025-06-17 20:29:20,683 - INFO -   Total Loss: 22.490805
2025-06-17 20:29:20,683 - INFO -   PEHE: 0.319672
2025-06-17 20:29:20,683 - INFO -   ATE Error: 0.010834
2025-06-17 20:29:20,693 - INFO -     - Main Losses (G): Factual=0.6827, Adversarial=0.6931, Decomp=7.6926
2025-06-17 20:29:20,693 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 20:29:20,694 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 20:29:51,716 - INFO - Epoch 280:
2025-06-17 20:29:51,716 - INFO -   Total Loss: 22.490818
2025-06-17 20:29:51,717 - INFO -   PEHE: 0.319664
2025-06-17 20:29:51,717 - INFO -   ATE Error: 0.010597
2025-06-17 20:29:51,718 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=7.6914
2025-06-17 20:29:51,718 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-17 20:29:51,718 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-17 20:30:19,644 - INFO - Stage 1 training completed!
2025-06-17 20:30:20,027 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-06-17 20:30:20,027 - INFO - Stage 2 training data shapes:
2025-06-17 20:30:20,027 - INFO -   x_train: (9120, 30)
2025-06-17 20:30:20,027 - INFO -   y_bar_target: (9120, 2)
2025-06-17 20:30:38,490 - INFO - Stage 2 - Iter: 0/300, ITE_C Loss: 5.2381 (Wass: -0.0007, GP: 0.5239), ITE_G Loss: 1.3395 (Adv: -0.2200, Sup: 0.0208)
2025-06-17 20:31:41,430 - INFO - Stage 2 - Iter: 100/300, ITE_C Loss: 4.1759 (Wass: 0.0000, GP: 0.4176), ITE_G Loss: -0.3563 (Adv: -0.3782, Sup: 0.0003)
2025-06-17 20:32:48,574 - INFO - Stage 2 - Iter: 200/300, ITE_C Loss: 4.7337 (Wass: 0.0000, GP: 0.4734), ITE_G Loss: -0.2969 (Adv: -0.3040, Sup: 0.0001)
2025-06-17 20:33:52,618 - INFO - Stage 2 - Iter: 299/300, ITE_C Loss: 5.3192 (Wass: 0.0000, GP: 0.5319), ITE_G Loss: -0.2471 (Adv: -0.2554, Sup: 0.0001)
2025-06-17 20:33:52,618 - INFO - Stage 2 training completed!
2025-06-17 20:33:52,936 - INFO - Stage2 model save skipped for quick test
2025-06-18 11:56:35,741 - INFO - Starting Stage 1 training...
2025-06-18 11:56:35,741 - INFO - Training samples: 9120
2025-06-18 11:56:35,741 - INFO - Test samples: 2280
2025-06-18 11:56:35,741 - INFO - Sample weights initialized: shape=(9120, 1)
2025-06-18 11:56:51,953 - INFO - Epoch 0:
2025-06-18 11:56:51,953 - INFO -   Total Loss: 6331.817871
2025-06-18 11:56:51,953 - INFO -   PEHE: 0.316903
2025-06-18 11:56:51,953 - INFO -   ATE Error: 0.058494
2025-06-18 11:56:51,953 - INFO -     - Main Losses (G): Factual=0.6676, Adversarial=0.6943, Decomp=149.7495
2025-06-18 11:56:51,968 - INFO -     - Discriminator Loss (D): 25.4100
2025-06-18 11:56:51,969 - INFO -     - Balance Loss (Weights): 3.0442
2025-06-18 11:56:52,110 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-18 11:56:52,192 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-18 11:57:26,612 - INFO - Epoch 20:
2025-06-18 11:57:26,612 - INFO -   Total Loss: 632.584534
2025-06-18 11:57:26,612 - INFO -   PEHE: 0.310630
2025-06-18 11:57:26,612 - INFO -   ATE Error: 0.013152
2025-06-18 11:57:26,612 - INFO -     - Main Losses (G): Factual=0.6825, Adversarial=0.6941, Decomp=7.6913
2025-06-18 11:57:26,612 - INFO -     - Discriminator Loss (D): 12.8528
2025-06-18 11:57:26,612 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 11:57:26,738 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-18 11:58:01,246 - INFO - Epoch 40:
2025-06-18 11:58:01,246 - INFO -   Total Loss: 27.595520
2025-06-18 11:58:01,246 - INFO -   PEHE: 0.310624
2025-06-18 11:58:01,246 - INFO -   ATE Error: 0.013000
2025-06-18 11:58:01,246 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6932, Decomp=7.6918
2025-06-18 11:58:01,246 - INFO -     - Discriminator Loss (D): 5.8011
2025-06-18 11:58:01,246 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 11:58:01,378 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-18 11:58:18,101 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-18 11:58:35,758 - INFO - Epoch 60:
2025-06-18 11:58:35,758 - INFO -   Total Loss: 24.063951
2025-06-18 11:58:35,758 - INFO -   PEHE: 0.310638
2025-06-18 11:58:35,758 - INFO -   ATE Error: 0.013345
2025-06-18 11:58:35,760 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6932, Decomp=7.6915
2025-06-18 11:58:35,760 - INFO -     - Discriminator Loss (D): 2.2677
2025-06-18 11:58:35,760 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 11:59:10,914 - INFO - Epoch 80:
2025-06-18 11:59:10,914 - INFO -   Total Loss: 22.780457
2025-06-18 11:59:10,914 - INFO -   PEHE: 0.310605
2025-06-18 11:59:10,914 - INFO -   ATE Error: 0.012556
2025-06-18 11:59:10,914 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6931, Decomp=7.6918
2025-06-18 11:59:10,930 - INFO -     - Discriminator Loss (D): 0.9833
2025-06-18 11:59:10,930 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 11:59:11,024 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-18 11:59:45,991 - INFO - Epoch 100:
2025-06-18 11:59:45,992 - INFO -   Total Loss: 22.510004
2025-06-18 11:59:45,992 - INFO -   PEHE: 0.310631
2025-06-18 11:59:45,993 - INFO -   ATE Error: 0.013178
2025-06-18 11:59:45,994 - INFO -     - Main Losses (G): Factual=0.6827, Adversarial=0.6931, Decomp=7.6927
2025-06-18 11:59:45,995 - INFO -     - Discriminator Loss (D): 0.7125
2025-06-18 11:59:45,995 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 11:59:46,107 - INFO - Model saved to ./results\models\stage1_model_epoch_100
2025-06-18 12:00:21,212 - INFO - Epoch 120:
2025-06-18 12:00:21,213 - INFO -   Total Loss: 22.490698
2025-06-18 12:00:21,213 - INFO -   PEHE: 0.310633
2025-06-18 12:00:21,214 - INFO -   ATE Error: 0.013220
2025-06-18 12:00:21,214 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6931, Decomp=7.6925
2025-06-18 12:00:21,214 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 12:00:21,215 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 12:00:56,134 - INFO - Epoch 140:
2025-06-18 12:00:56,134 - INFO -   Total Loss: 22.490747
2025-06-18 12:00:56,134 - INFO -   PEHE: 0.310635
2025-06-18 12:00:56,134 - INFO -   ATE Error: 0.013258
2025-06-18 12:00:56,134 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6931, Decomp=7.6917
2025-06-18 12:00:56,134 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 12:00:56,134 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 12:01:13,902 - INFO - Model saved to ./results\models\stage1_model_epoch_150
2025-06-18 12:01:31,164 - INFO - Epoch 160:
2025-06-18 12:01:31,164 - INFO -   Total Loss: 22.490709
2025-06-18 12:01:31,164 - INFO -   PEHE: 0.310616
2025-06-18 12:01:31,164 - INFO -   ATE Error: 0.012820
2025-06-18 12:01:31,164 - INFO -     - Main Losses (G): Factual=0.6827, Adversarial=0.6931, Decomp=7.6919
2025-06-18 12:01:31,164 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 12:01:31,164 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 12:02:05,963 - INFO - Epoch 180:
2025-06-18 12:02:05,963 - INFO -   Total Loss: 22.490700
2025-06-18 12:02:05,963 - INFO -   PEHE: 0.310611
2025-06-18 12:02:05,963 - INFO -   ATE Error: 0.012686
2025-06-18 12:02:05,963 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6931, Decomp=7.6915
2025-06-18 12:02:05,963 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 12:02:05,963 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 12:02:41,288 - INFO - Epoch 200:
2025-06-18 12:02:41,288 - INFO -   Total Loss: 22.490711
2025-06-18 12:02:41,288 - INFO -   PEHE: 0.310602
2025-06-18 12:02:41,288 - INFO -   ATE Error: 0.012481
2025-06-18 12:02:41,288 - INFO -     - Main Losses (G): Factual=0.6827, Adversarial=0.6931, Decomp=7.6920
2025-06-18 12:02:41,288 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 12:02:41,288 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 12:02:41,383 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-18 12:02:41,492 - INFO - Model saved to ./results\models\stage1_model_epoch_200
2025-06-18 12:03:17,155 - INFO - Epoch 220:
2025-06-18 12:03:17,155 - INFO -   Total Loss: 22.490694
2025-06-18 12:03:17,155 - INFO -   PEHE: 0.310632
2025-06-18 12:03:17,155 - INFO -   ATE Error: 0.013201
2025-06-18 12:03:17,155 - INFO -     - Main Losses (G): Factual=0.6825, Adversarial=0.6931, Decomp=7.6923
2025-06-18 12:03:17,155 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 12:03:17,155 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 12:03:53,358 - INFO - Epoch 240:
2025-06-18 12:03:53,358 - INFO -   Total Loss: 22.490753
2025-06-18 12:03:53,358 - INFO -   PEHE: 0.310629
2025-06-18 12:03:53,358 - INFO -   ATE Error: 0.013113
2025-06-18 12:03:53,358 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6931, Decomp=7.6916
2025-06-18 12:03:53,358 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 12:03:53,358 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 12:04:11,610 - INFO - Model saved to ./results\models\stage1_model_epoch_250
2025-06-18 12:04:30,117 - INFO - Epoch 260:
2025-06-18 12:04:30,118 - INFO -   Total Loss: 22.490711
2025-06-18 12:04:30,119 - INFO -   PEHE: 0.310608
2025-06-18 12:04:30,119 - INFO -   ATE Error: 0.012627
2025-06-18 12:04:30,119 - INFO -     - Main Losses (G): Factual=0.6827, Adversarial=0.6931, Decomp=7.6926
2025-06-18 12:04:30,119 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 12:04:30,119 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 12:05:04,912 - INFO - Epoch 280:
2025-06-18 12:05:04,914 - INFO -   Total Loss: 22.490725
2025-06-18 12:05:04,914 - INFO -   PEHE: 0.310612
2025-06-18 12:05:04,914 - INFO -   ATE Error: 0.012706
2025-06-18 12:05:04,914 - INFO -     - Main Losses (G): Factual=0.6825, Adversarial=0.6931, Decomp=7.6919
2025-06-18 12:05:04,915 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 12:05:04,915 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 12:05:37,550 - INFO - Stage 1 training completed!
2025-06-18 12:05:37,945 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-06-18 12:05:37,945 - INFO - Stage 2 training data shapes:
2025-06-18 12:05:37,945 - INFO -   x_train: (9120, 30)
2025-06-18 12:05:37,945 - INFO -   y_bar_target: (9120, 2)
2025-06-18 12:05:55,125 - INFO - Stage 2 - Iter: 0/300, ITE_C Loss: 3.3566 (Wass: -0.0004, GP: 0.3357), ITE_G Loss: 2.3745 (Adv: 0.7371, Sup: 0.0218)
2025-06-18 12:07:05,703 - INFO - Stage 2 - Iter: 100/300, ITE_C Loss: 2.8364 (Wass: -0.0001, GP: 0.2836), ITE_G Loss: 1.1196 (Adv: 1.0917, Sup: 0.0004)
2025-06-18 12:08:12,350 - INFO - Stage 2 - Iter: 200/300, ITE_C Loss: 3.1781 (Wass: -0.0000, GP: 0.3178), ITE_G Loss: 1.1126 (Adv: 1.1005, Sup: 0.0002)
2025-06-18 12:09:13,987 - INFO - Stage 2 - Iter: 299/300, ITE_C Loss: 3.5546 (Wass: -0.0000, GP: 0.3555), ITE_G Loss: 0.9682 (Adv: 0.9558, Sup: 0.0002)
2025-06-18 12:09:13,987 - INFO - Stage 2 training completed!
2025-06-18 12:09:14,258 - INFO - Stage2 model save skipped for quick test
2025-06-18 12:28:38,168 - INFO - Starting Stage 1 training...
2025-06-18 12:28:38,169 - INFO - Training samples: 9120
2025-06-18 12:28:38,169 - INFO - Test samples: 2280
2025-06-18 12:28:38,170 - INFO - Sample weights initialized: shape=(9120, 1)
2025-06-18 12:28:56,434 - INFO - Epoch 0:
2025-06-18 12:28:56,434 - INFO -   Total Loss: 6268.404785
2025-06-18 12:28:56,434 - INFO -   PEHE: 0.316927
2025-06-18 12:28:56,435 - INFO -   ATE Error: 0.057791
2025-06-18 12:28:56,435 - INFO -     - Main Losses (G): Factual=0.6685, Adversarial=0.6943, Decomp=72.0388
2025-06-18 12:28:56,436 - INFO -     - Discriminator Loss (D): 25.4100
2025-06-18 12:28:56,436 - INFO -     - Balance Loss (Weights): 3.0285
2025-06-18 12:28:56,534 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-18 12:28:56,630 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-18 12:29:31,721 - INFO - Epoch 20:
2025-06-18 12:29:31,721 - INFO -   Total Loss: 650.852844
2025-06-18 12:29:31,722 - INFO -   PEHE: 0.310630
2025-06-18 12:29:31,722 - INFO -   ATE Error: 0.013153
2025-06-18 12:29:31,722 - INFO -     - Main Losses (G): Factual=0.6825, Adversarial=0.6941, Decomp=24.8761
2025-06-18 12:29:31,723 - INFO -     - Discriminator Loss (D): 12.8528
2025-06-18 12:29:31,723 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 12:29:31,828 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-18 12:30:07,094 - INFO - Epoch 40:
2025-06-18 12:30:07,095 - INFO -   Total Loss: 43.212257
2025-06-18 12:30:07,095 - INFO -   PEHE: 0.310624
2025-06-18 12:30:07,096 - INFO -   ATE Error: 0.013000
2025-06-18 12:30:07,097 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6932, Decomp=23.0360
2025-06-18 12:30:07,098 - INFO -     - Discriminator Loss (D): 5.8011
2025-06-18 12:30:07,099 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 12:30:07,208 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-18 12:30:24,665 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-18 12:30:42,258 - INFO - Epoch 60:
2025-06-18 12:30:42,259 - INFO -   Total Loss: 39.680679
2025-06-18 12:30:42,259 - INFO -   PEHE: 0.310638
2025-06-18 12:30:42,259 - INFO -   ATE Error: 0.013346
2025-06-18 12:30:42,260 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6932, Decomp=23.0361
2025-06-18 12:30:42,260 - INFO -     - Discriminator Loss (D): 2.2677
2025-06-18 12:30:42,260 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 12:31:17,317 - INFO - Epoch 80:
2025-06-18 12:31:17,318 - INFO -   Total Loss: 38.397198
2025-06-18 12:31:17,318 - INFO -   PEHE: 0.310605
2025-06-18 12:31:17,319 - INFO -   ATE Error: 0.012557
2025-06-18 12:31:17,319 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6931, Decomp=23.0363
2025-06-18 12:31:17,319 - INFO -     - Discriminator Loss (D): 0.9833
2025-06-18 12:31:17,320 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 12:31:17,408 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-18 12:31:52,383 - INFO - Epoch 100:
2025-06-18 12:31:52,383 - INFO -   Total Loss: 38.126736
2025-06-18 12:31:52,384 - INFO -   PEHE: 0.310631
2025-06-18 12:31:52,384 - INFO -   ATE Error: 0.013178
2025-06-18 12:31:52,384 - INFO -     - Main Losses (G): Factual=0.6827, Adversarial=0.6931, Decomp=23.0361
2025-06-18 12:31:52,385 - INFO -     - Discriminator Loss (D): 0.7125
2025-06-18 12:31:52,385 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 12:31:52,477 - INFO - Model saved to ./results\models\stage1_model_epoch_100
2025-06-18 12:32:27,996 - INFO - Epoch 120:
2025-06-18 12:32:27,996 - INFO -   Total Loss: 38.107437
2025-06-18 12:32:27,997 - INFO -   PEHE: 0.310633
2025-06-18 12:32:27,997 - INFO -   ATE Error: 0.013220
2025-06-18 12:32:27,998 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6931, Decomp=23.0360
2025-06-18 12:32:27,998 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 12:32:27,999 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 12:33:03,270 - INFO - Epoch 140:
2025-06-18 12:33:03,271 - INFO -   Total Loss: 38.107449
2025-06-18 12:33:03,271 - INFO -   PEHE: 0.310635
2025-06-18 12:33:03,271 - INFO -   ATE Error: 0.013258
2025-06-18 12:33:03,271 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6931, Decomp=23.0359
2025-06-18 12:33:03,272 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 12:33:03,272 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 12:33:20,940 - INFO - Model saved to ./results\models\stage1_model_epoch_150
2025-06-18 12:33:38,605 - INFO - Epoch 160:
2025-06-18 12:33:38,606 - INFO -   Total Loss: 38.107426
2025-06-18 12:33:38,606 - INFO -   PEHE: 0.310616
2025-06-18 12:33:38,606 - INFO -   ATE Error: 0.012820
2025-06-18 12:33:38,606 - INFO -     - Main Losses (G): Factual=0.6827, Adversarial=0.6931, Decomp=23.0359
2025-06-18 12:33:38,608 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 12:33:38,608 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 12:34:13,708 - INFO - Epoch 180:
2025-06-18 12:34:13,709 - INFO -   Total Loss: 38.107441
2025-06-18 12:34:13,709 - INFO -   PEHE: 0.310611
2025-06-18 12:34:13,710 - INFO -   ATE Error: 0.012686
2025-06-18 12:34:13,710 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6931, Decomp=23.0358
2025-06-18 12:34:13,710 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 12:34:13,711 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 12:34:48,766 - INFO - Epoch 200:
2025-06-18 12:34:48,767 - INFO -   Total Loss: 38.107430
2025-06-18 12:34:48,767 - INFO -   PEHE: 0.310602
2025-06-18 12:34:48,767 - INFO -   ATE Error: 0.012481
2025-06-18 12:34:48,767 - INFO -     - Main Losses (G): Factual=0.6827, Adversarial=0.6931, Decomp=23.0360
2025-06-18 12:34:48,769 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 12:34:48,769 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 12:34:48,874 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-18 12:34:48,962 - INFO - Model saved to ./results\models\stage1_model_epoch_200
2025-06-18 12:35:24,131 - INFO - Epoch 220:
2025-06-18 12:35:24,132 - INFO -   Total Loss: 38.107410
2025-06-18 12:35:24,132 - INFO -   PEHE: 0.310632
2025-06-18 12:35:24,132 - INFO -   ATE Error: 0.013201
2025-06-18 12:35:24,133 - INFO -     - Main Losses (G): Factual=0.6825, Adversarial=0.6931, Decomp=23.0359
2025-06-18 12:35:24,133 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 12:35:24,134 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 12:35:59,161 - INFO - Epoch 240:
2025-06-18 12:35:59,161 - INFO -   Total Loss: 38.107441
2025-06-18 12:35:59,162 - INFO -   PEHE: 0.310629
2025-06-18 12:35:59,162 - INFO -   ATE Error: 0.013114
2025-06-18 12:35:59,162 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6931, Decomp=23.0358
2025-06-18 12:35:59,163 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 12:35:59,163 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 12:36:16,868 - INFO - Model saved to ./results\models\stage1_model_epoch_250
2025-06-18 12:36:34,539 - INFO - Epoch 260:
2025-06-18 12:36:34,540 - INFO -   Total Loss: 38.107430
2025-06-18 12:36:34,540 - INFO -   PEHE: 0.310608
2025-06-18 12:36:34,541 - INFO -   ATE Error: 0.012627
2025-06-18 12:36:34,541 - INFO -     - Main Losses (G): Factual=0.6827, Adversarial=0.6931, Decomp=23.0360
2025-06-18 12:36:34,541 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 12:36:34,541 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 12:37:09,838 - INFO - Epoch 280:
2025-06-18 12:37:09,839 - INFO -   Total Loss: 38.107418
2025-06-18 12:37:09,839 - INFO -   PEHE: 0.310612
2025-06-18 12:37:09,839 - INFO -   ATE Error: 0.012706
2025-06-18 12:37:09,840 - INFO -     - Main Losses (G): Factual=0.6825, Adversarial=0.6931, Decomp=23.0359
2025-06-18 12:37:09,840 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 12:37:09,841 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 12:37:43,378 - INFO - Stage 1 training completed!
2025-06-18 12:37:43,843 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-06-18 12:37:43,843 - INFO - Stage 2 training data shapes:
2025-06-18 12:37:43,844 - INFO -   x_train: (9120, 30)
2025-06-18 12:37:43,844 - INFO -   y_bar_target: (9120, 2)
2025-06-18 12:38:00,463 - INFO - Stage 2 - Iter: 0/300, ITE_C Loss: 3.3566 (Wass: -0.0004, GP: 0.3357), ITE_G Loss: 2.3745 (Adv: 0.7371, Sup: 0.0218)
2025-06-18 12:39:07,311 - INFO - Stage 2 - Iter: 100/300, ITE_C Loss: 2.8359 (Wass: 0.0000, GP: 0.2836), ITE_G Loss: 1.1175 (Adv: 1.0916, Sup: 0.0003)
2025-06-18 12:40:12,750 - INFO - Stage 2 - Iter: 200/300, ITE_C Loss: 3.1781 (Wass: 0.0000, GP: 0.3178), ITE_G Loss: 1.1130 (Adv: 1.1005, Sup: 0.0002)
2025-06-18 12:41:15,596 - INFO - Stage 2 - Iter: 299/300, ITE_C Loss: 3.5546 (Wass: -0.0000, GP: 0.3555), ITE_G Loss: 0.9646 (Adv: 0.9558, Sup: 0.0001)
2025-06-18 12:41:15,596 - INFO - Stage 2 training completed!
2025-06-18 12:41:15,862 - INFO - Stage2 model save skipped for quick test
2025-06-18 14:58:32,020 - INFO - Starting Stage 1 training...
2025-06-18 14:58:32,021 - INFO - Training samples: 9120
2025-06-18 14:58:32,022 - INFO - Test samples: 2280
2025-06-18 14:58:32,022 - INFO - Sample weights initialized: shape=(9120, 1)
2025-06-18 14:58:48,326 - INFO - Epoch 0:
2025-06-18 14:58:48,330 - INFO -   Total Loss: 55.531116
2025-06-18 14:58:48,332 - INFO -   PEHE: 0.318957
2025-06-18 14:58:48,332 - INFO -   ATE Error: 0.050888
2025-06-18 14:58:48,334 - INFO -     - Main Losses (G): Factual=0.5259, Adversarial=0.6941, Decomp=50.8416
2025-06-18 14:58:48,334 - INFO -     - Discriminator Loss (D): 0.6973
2025-06-18 14:58:48,334 - INFO -     - Balance Loss (Weights): 3.0905
2025-06-18 14:58:48,438 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-18 14:58:48,528 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-18 14:59:23,712 - INFO - Epoch 20:
2025-06-18 14:59:23,713 - INFO -   Total Loss: 9.523774
2025-06-18 14:59:23,713 - INFO -   PEHE: 0.310345
2025-06-18 14:59:23,714 - INFO -   ATE Error: 0.011528
2025-06-18 14:59:23,714 - INFO -     - Main Losses (G): Factual=0.2819, Adversarial=0.6958, Decomp=5.5328
2025-06-18 14:59:23,714 - INFO -     - Discriminator Loss (D): 0.6945
2025-06-18 14:59:23,715 - INFO -     - Balance Loss (Weights): 1.8676
2025-06-18 14:59:23,938 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-18 14:59:59,293 - INFO - Epoch 40:
2025-06-18 14:59:59,294 - INFO -   Total Loss: 5.774041
2025-06-18 14:59:59,294 - INFO -   PEHE: 0.314008
2025-06-18 14:59:59,295 - INFO -   ATE Error: 0.014731
2025-06-18 14:59:59,295 - INFO -     - Main Losses (G): Factual=0.2667, Adversarial=0.6981, Decomp=2.0295
2025-06-18 14:59:59,296 - INFO -     - Discriminator Loss (D): 0.6919
2025-06-18 14:59:59,296 - INFO -     - Balance Loss (Weights): 1.0736
2025-06-18 15:00:17,321 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-18 15:00:35,072 - INFO - Epoch 60:
2025-06-18 15:00:35,072 - INFO -   Total Loss: 4.433204
2025-06-18 15:00:35,073 - INFO -   PEHE: 0.317808
2025-06-18 15:00:35,073 - INFO -   ATE Error: 0.004648
2025-06-18 15:00:35,074 - INFO -     - Main Losses (G): Factual=0.2616, Adversarial=0.7011, Decomp=0.8642
2025-06-18 15:00:35,074 - INFO -     - Discriminator Loss (D): 0.6892
2025-06-18 15:00:35,075 - INFO -     - Balance Loss (Weights): 0.7323
2025-06-18 15:01:10,563 - INFO - Epoch 80:
2025-06-18 15:01:10,564 - INFO -   Total Loss: 4.178302
2025-06-18 15:01:10,564 - INFO -   PEHE: 0.317704
2025-06-18 15:01:10,565 - INFO -   ATE Error: 0.003560
2025-06-18 15:01:10,565 - INFO -     - Main Losses (G): Factual=0.2537, Adversarial=0.7029, Decomp=0.8005
2025-06-18 15:01:10,566 - INFO -     - Discriminator Loss (D): 0.6877
2025-06-18 15:01:10,566 - INFO -     - Balance Loss (Weights): 0.5292
2025-06-18 15:01:46,254 - INFO - Epoch 100:
2025-06-18 15:01:46,255 - INFO -   Total Loss: 4.069450
2025-06-18 15:01:46,255 - INFO -   PEHE: 0.327526
2025-06-18 15:01:46,256 - INFO -   ATE Error: 0.018461
2025-06-18 15:01:46,256 - INFO -     - Main Losses (G): Factual=0.2342, Adversarial=0.7043, Decomp=0.7926
2025-06-18 15:01:46,257 - INFO -     - Discriminator Loss (D): 0.6868
2025-06-18 15:01:46,257 - INFO -     - Balance Loss (Weights): 0.4384
2025-06-18 15:01:46,366 - INFO - Model saved to ./results\models\stage1_model_epoch_100
2025-06-18 15:02:22,024 - INFO - Epoch 120:
2025-06-18 15:02:22,024 - INFO -   Total Loss: 4.023723
2025-06-18 15:02:22,025 - INFO -   PEHE: 0.336610
2025-06-18 15:02:22,025 - INFO -   ATE Error: 0.001267
2025-06-18 15:02:22,026 - INFO -     - Main Losses (G): Factual=0.2256, Adversarial=0.7063, Decomp=0.7874
2025-06-18 15:02:22,026 - INFO -     - Discriminator Loss (D): 0.6853
2025-06-18 15:02:22,027 - INFO -     - Balance Loss (Weights): 0.3535
2025-06-18 15:02:57,516 - INFO - Epoch 140:
2025-06-18 15:02:57,517 - INFO -   Total Loss: 3.982537
2025-06-18 15:02:57,518 - INFO -   PEHE: 0.341461
2025-06-18 15:02:57,518 - INFO -   ATE Error: 0.006062
2025-06-18 15:02:57,518 - INFO -     - Main Losses (G): Factual=0.2094, Adversarial=0.7064, Decomp=0.7827
2025-06-18 15:02:57,519 - INFO -     - Discriminator Loss (D): 0.6858
2025-06-18 15:02:57,519 - INFO -     - Balance Loss (Weights): 0.2777
2025-06-18 15:03:15,200 - INFO - Model saved to ./results\models\stage1_model_epoch_150
2025-06-18 15:03:32,903 - INFO - Epoch 160:
2025-06-18 15:03:32,904 - INFO -   Total Loss: 3.967489
2025-06-18 15:03:32,904 - INFO -   PEHE: 0.348110
2025-06-18 15:03:32,904 - INFO -   ATE Error: 0.012292
2025-06-18 15:03:32,905 - INFO -     - Main Losses (G): Factual=0.2031, Adversarial=0.7078, Decomp=0.7795
2025-06-18 15:03:32,905 - INFO -     - Discriminator Loss (D): 0.6851
2025-06-18 15:03:32,905 - INFO -     - Balance Loss (Weights): 0.2107
2025-06-18 15:04:08,450 - INFO - Epoch 180:
2025-06-18 15:04:08,451 - INFO -   Total Loss: 3.973362
2025-06-18 15:04:08,452 - INFO -   PEHE: 0.356023
2025-06-18 15:04:08,452 - INFO -   ATE Error: 0.012547
2025-06-18 15:04:08,453 - INFO -     - Main Losses (G): Factual=0.2113, Adversarial=0.7090, Decomp=0.7795
2025-06-18 15:04:08,453 - INFO -     - Discriminator Loss (D): 0.6847
2025-06-18 15:04:08,454 - INFO -     - Balance Loss (Weights): 0.1840
2025-06-18 15:04:43,942 - INFO - Epoch 200:
2025-06-18 15:04:43,942 - INFO -   Total Loss: 3.944569
2025-06-18 15:04:43,943 - INFO -   PEHE: 0.356185
2025-06-18 15:04:43,943 - INFO -   ATE Error: 0.003951
2025-06-18 15:04:43,943 - INFO -     - Main Losses (G): Factual=0.1914, Adversarial=0.7087, Decomp=0.7756
2025-06-18 15:04:43,944 - INFO -     - Discriminator Loss (D): 0.6854
2025-06-18 15:04:43,944 - INFO -     - Balance Loss (Weights): 0.1583
2025-06-18 15:04:44,057 - INFO - Model saved to ./results\models\stage1_model_epoch_200
2025-06-18 15:05:19,588 - INFO - Epoch 220:
2025-06-18 15:05:19,589 - INFO -   Total Loss: 3.942438
2025-06-18 15:05:19,589 - INFO -   PEHE: 0.363530
2025-06-18 15:05:19,590 - INFO -   ATE Error: 0.008261
2025-06-18 15:05:19,590 - INFO -     - Main Losses (G): Factual=0.1889, Adversarial=0.7102, Decomp=0.7730
2025-06-18 15:05:19,591 - INFO -     - Discriminator Loss (D): 0.6846
2025-06-18 15:05:19,591 - INFO -     - Balance Loss (Weights): 0.1597
2025-06-18 15:05:55,010 - INFO - Epoch 240:
2025-06-18 15:05:55,010 - INFO -   Total Loss: 3.935833
2025-06-18 15:05:55,011 - INFO -   PEHE: 0.365089
2025-06-18 15:05:55,011 - INFO -   ATE Error: 0.005215
2025-06-18 15:05:55,011 - INFO -     - Main Losses (G): Factual=0.1827, Adversarial=0.7105, Decomp=0.7730
2025-06-18 15:05:55,011 - INFO -     - Discriminator Loss (D): 0.6847
2025-06-18 15:05:55,012 - INFO -     - Balance Loss (Weights): 0.1079
2025-06-18 15:06:12,696 - INFO - Model saved to ./results\models\stage1_model_epoch_250
2025-06-18 15:06:30,202 - INFO - Epoch 260:
2025-06-18 15:06:30,202 - INFO -   Total Loss: 3.947119
2025-06-18 15:06:30,203 - INFO -   PEHE: 0.364714
2025-06-18 15:06:30,203 - INFO -   ATE Error: 0.008371
2025-06-18 15:06:30,203 - INFO -     - Main Losses (G): Factual=0.1883, Adversarial=0.7123, Decomp=0.7725
2025-06-18 15:06:30,204 - INFO -     - Discriminator Loss (D): 0.6836
2025-06-18 15:06:30,204 - INFO -     - Balance Loss (Weights): 0.1132
2025-06-18 15:07:05,509 - INFO - Epoch 280:
2025-06-18 15:07:05,510 - INFO -   Total Loss: 3.937554
2025-06-18 15:07:05,510 - INFO -   PEHE: 0.374396
2025-06-18 15:07:05,512 - INFO -   ATE Error: 0.014091
2025-06-18 15:07:05,512 - INFO -     - Main Losses (G): Factual=0.1853, Adversarial=0.7113, Decomp=0.7697
2025-06-18 15:07:05,512 - INFO -     - Discriminator Loss (D): 0.6848
2025-06-18 15:07:05,513 - INFO -     - Balance Loss (Weights): 0.1039
2025-06-18 15:07:40,648 - INFO - Epoch 300:
2025-06-18 15:07:40,648 - INFO -   Total Loss: 3.929589
2025-06-18 15:07:40,649 - INFO -   PEHE: 0.369950
2025-06-18 15:07:40,649 - INFO -   ATE Error: 0.008635
2025-06-18 15:07:40,649 - INFO -     - Main Losses (G): Factual=0.1756, Adversarial=0.7119, Decomp=0.7695
2025-06-18 15:07:40,650 - INFO -     - Discriminator Loss (D): 0.6848
2025-06-18 15:07:40,651 - INFO -     - Balance Loss (Weights): 0.0900
2025-06-18 15:07:40,733 - INFO - Model saved to ./results\models\stage1_model_epoch_300
2025-06-18 15:08:16,607 - INFO - Epoch 320:
2025-06-18 15:08:16,607 - INFO -   Total Loss: 3.934966
2025-06-18 15:08:16,608 - INFO -   PEHE: 0.372156
2025-06-18 15:08:16,608 - INFO -   ATE Error: 0.018604
2025-06-18 15:08:16,609 - INFO -     - Main Losses (G): Factual=0.1775, Adversarial=0.7129, Decomp=0.7692
2025-06-18 15:08:16,609 - INFO -     - Discriminator Loss (D): 0.6844
2025-06-18 15:08:16,610 - INFO -     - Balance Loss (Weights): 0.0904
2025-06-18 15:08:52,429 - INFO - Epoch 340:
2025-06-18 15:08:52,429 - INFO -   Total Loss: 3.950986
2025-06-18 15:08:52,429 - INFO -   PEHE: 0.380424
2025-06-18 15:08:52,429 - INFO -   ATE Error: 0.006204
2025-06-18 15:08:52,429 - INFO -     - Main Losses (G): Factual=0.1881, Adversarial=0.7137, Decomp=0.7708
2025-06-18 15:08:52,429 - INFO -     - Discriminator Loss (D): 0.6841
2025-06-18 15:08:52,429 - INFO -     - Balance Loss (Weights): 0.0727
2025-06-18 15:09:10,354 - INFO - Model saved to ./results\models\stage1_model_epoch_350
2025-06-18 15:09:28,273 - INFO - Epoch 360:
2025-06-18 15:09:28,273 - INFO -   Total Loss: 3.938080
2025-06-18 15:09:28,273 - INFO -   PEHE: 0.392935
2025-06-18 15:09:28,273 - INFO -   ATE Error: 0.016082
2025-06-18 15:09:28,273 - INFO -     - Main Losses (G): Factual=0.1798, Adversarial=0.7132, Decomp=0.7678
2025-06-18 15:09:28,273 - INFO -     - Discriminator Loss (D): 0.6850
2025-06-18 15:09:28,273 - INFO -     - Balance Loss (Weights): 0.0794
2025-06-18 15:10:05,266 - INFO - Epoch 380:
2025-06-18 15:10:05,267 - INFO -   Total Loss: 3.948622
2025-06-18 15:10:05,267 - INFO -   PEHE: 0.385134
2025-06-18 15:10:05,268 - INFO -   ATE Error: 0.003429
2025-06-18 15:10:05,269 - INFO -     - Main Losses (G): Factual=0.1883, Adversarial=0.7134, Decomp=0.7686
2025-06-18 15:10:05,269 - INFO -     - Discriminator Loss (D): 0.6852
2025-06-18 15:10:05,270 - INFO -     - Balance Loss (Weights): 0.0674
2025-06-18 15:10:42,870 - INFO - Epoch 400:
2025-06-18 15:10:42,871 - INFO -   Total Loss: 3.935865
2025-06-18 15:10:42,871 - INFO -   PEHE: 0.394913
2025-06-18 15:10:42,872 - INFO -   ATE Error: 0.036341
2025-06-18 15:10:42,873 - INFO -     - Main Losses (G): Factual=0.1775, Adversarial=0.7136, Decomp=0.7651
2025-06-18 15:10:42,873 - INFO -     - Discriminator Loss (D): 0.6854
2025-06-18 15:10:42,874 - INFO -     - Balance Loss (Weights): 0.0663
2025-06-18 15:10:42,976 - INFO - Model saved to ./results\models\stage1_model_epoch_400
2025-06-18 15:11:20,338 - INFO - Epoch 420:
2025-06-18 15:11:20,338 - INFO -   Total Loss: 3.936171
2025-06-18 15:11:20,338 - INFO -   PEHE: 0.402840
2025-06-18 15:11:20,339 - INFO -   ATE Error: 0.020833
2025-06-18 15:11:20,339 - INFO -     - Main Losses (G): Factual=0.1775, Adversarial=0.7134, Decomp=0.7657
2025-06-18 15:11:20,339 - INFO -     - Discriminator Loss (D): 0.6859
2025-06-18 15:11:20,339 - INFO -     - Balance Loss (Weights): 0.0652
2025-06-18 15:11:57,089 - INFO - Epoch 440:
2025-06-18 15:11:57,089 - INFO -   Total Loss: 3.942539
2025-06-18 15:11:57,090 - INFO -   PEHE: 0.403140
2025-06-18 15:11:57,090 - INFO -   ATE Error: 0.013621
2025-06-18 15:11:57,091 - INFO -     - Main Losses (G): Factual=0.1806, Adversarial=0.7136, Decomp=0.7674
2025-06-18 15:11:57,092 - INFO -     - Discriminator Loss (D): 0.6860
2025-06-18 15:11:57,093 - INFO -     - Balance Loss (Weights): 0.0671
2025-06-18 15:20:00,969 - INFO - Starting Stage 1 training...
2025-06-18 15:20:00,969 - INFO - Training samples: 9120
2025-06-18 15:20:00,969 - INFO - Test samples: 2280
2025-06-18 15:20:00,969 - INFO - Sample weights initialized: shape=(9120, 1)
2025-06-18 15:20:19,047 - INFO - Epoch 0:
2025-06-18 15:20:19,047 - INFO -   Total Loss: 55.531116
2025-06-18 15:20:19,048 - INFO -   PEHE: 0.318956
2025-06-18 15:20:19,048 - INFO -   ATE Error: 0.050882
2025-06-18 15:20:19,048 - INFO -     - Main Losses (G): Factual=0.5259, Adversarial=0.6941, Decomp=50.8416
2025-06-18 15:20:19,049 - INFO -     - Discriminator Loss (D): 0.6973
2025-06-18 15:20:19,049 - INFO -     - Balance Loss (Weights): 3.0905
2025-06-18 15:20:19,131 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-18 15:20:19,193 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-18 15:20:54,746 - INFO - Epoch 20:
2025-06-18 15:20:54,746 - INFO -   Total Loss: 5.947442
2025-06-18 15:20:54,747 - INFO -   PEHE: 0.311538
2025-06-18 15:20:54,747 - INFO -   ATE Error: 0.012206
2025-06-18 15:20:54,748 - INFO -     - Main Losses (G): Factual=0.2765, Adversarial=0.6958, Decomp=2.0035
2025-06-18 15:20:54,749 - INFO -     - Discriminator Loss (D): 0.6945
2025-06-18 15:20:54,749 - INFO -     - Balance Loss (Weights): 0.9886
2025-06-18 15:20:54,974 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-18 15:21:30,081 - INFO - Epoch 40:
2025-06-18 15:21:30,081 - INFO -   Total Loss: 4.622875
2025-06-18 15:21:30,082 - INFO -   PEHE: 0.313818
2025-06-18 15:21:30,082 - INFO -   ATE Error: 0.009343
2025-06-18 15:21:30,083 - INFO -     - Main Losses (G): Factual=0.2537, Adversarial=0.6980, Decomp=0.9921
2025-06-18 15:21:30,083 - INFO -     - Discriminator Loss (D): 0.6920
2025-06-18 15:21:30,084 - INFO -     - Balance Loss (Weights): 0.5471
2025-06-18 15:21:47,896 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-18 15:22:05,781 - INFO - Epoch 60:
2025-06-18 15:22:05,781 - INFO -   Total Loss: 4.233121
2025-06-18 15:22:05,781 - INFO -   PEHE: 0.317298
2025-06-18 15:22:05,781 - INFO -   ATE Error: 0.002752
2025-06-18 15:22:05,781 - INFO -     - Main Losses (G): Factual=0.2406, Adversarial=0.7006, Decomp=0.8061
2025-06-18 15:22:05,781 - INFO -     - Discriminator Loss (D): 0.6895
2025-06-18 15:22:05,781 - INFO -     - Balance Loss (Weights): 0.3486
2025-06-18 15:22:41,160 - INFO - Epoch 80:
2025-06-18 15:22:41,160 - INFO -   Total Loss: 4.063644
2025-06-18 15:22:41,160 - INFO -   PEHE: 0.326487
2025-06-18 15:22:41,160 - INFO -   ATE Error: 0.005995
2025-06-18 15:22:41,160 - INFO -     - Main Losses (G): Factual=0.2250, Adversarial=0.7020, Decomp=0.7855
2025-06-18 15:22:41,160 - INFO -     - Discriminator Loss (D): 0.6883
2025-06-18 15:22:41,160 - INFO -     - Balance Loss (Weights): 0.2370
2025-06-18 15:23:16,638 - INFO - Epoch 100:
2025-06-18 15:23:16,638 - INFO -   Total Loss: 3.986702
2025-06-18 15:23:16,638 - INFO -   PEHE: 0.334146
2025-06-18 15:23:16,638 - INFO -   ATE Error: 0.003242
2025-06-18 15:23:16,638 - INFO -     - Main Losses (G): Factual=0.2060, Adversarial=0.7030, Decomp=0.7803
2025-06-18 15:23:16,638 - INFO -     - Discriminator Loss (D): 0.6878
2025-06-18 15:23:16,638 - INFO -     - Balance Loss (Weights): 0.1967
2025-06-18 15:23:16,749 - INFO - Model saved to ./results\models\stage1_model_epoch_100
2025-06-18 15:23:52,070 - INFO - Epoch 120:
2025-06-18 15:23:52,070 - INFO -   Total Loss: 3.962224
2025-06-18 15:23:52,070 - INFO -   PEHE: 0.356006
2025-06-18 15:23:52,070 - INFO -   ATE Error: 0.031005
2025-06-18 15:23:52,070 - INFO -     - Main Losses (G): Factual=0.2036, Adversarial=0.7046, Decomp=0.7769
2025-06-18 15:23:52,070 - INFO -     - Discriminator Loss (D): 0.6867
2025-06-18 15:23:52,085 - INFO -     - Balance Loss (Weights): 0.1390
2025-06-18 15:24:27,162 - INFO - Epoch 140:
2025-06-18 15:24:27,162 - INFO -   Total Loss: 3.936984
2025-06-18 15:24:27,163 - INFO -   PEHE: 0.354193
2025-06-18 15:24:27,163 - INFO -   ATE Error: 0.017171
2025-06-18 15:24:27,163 - INFO -     - Main Losses (G): Factual=0.1897, Adversarial=0.7053, Decomp=0.7756
2025-06-18 15:24:27,164 - INFO -     - Discriminator Loss (D): 0.6865
2025-06-18 15:24:27,164 - INFO -     - Balance Loss (Weights): 0.1135
2025-06-18 15:24:45,069 - INFO - Model saved to ./results\models\stage1_model_epoch_150
2025-06-18 15:25:02,813 - INFO - Epoch 160:
2025-06-18 15:25:02,814 - INFO -   Total Loss: 3.928073
2025-06-18 15:25:02,815 - INFO -   PEHE: 0.353248
2025-06-18 15:25:02,815 - INFO -   ATE Error: 0.017642
2025-06-18 15:25:02,815 - INFO -     - Main Losses (G): Factual=0.1861, Adversarial=0.7068, Decomp=0.7725
2025-06-18 15:25:02,815 - INFO -     - Discriminator Loss (D): 0.6858
2025-06-18 15:25:02,816 - INFO -     - Balance Loss (Weights): 0.0897
2025-06-18 15:25:38,397 - INFO - Epoch 180:
2025-06-18 15:25:38,398 - INFO -   Total Loss: 3.935583
2025-06-18 15:25:38,398 - INFO -   PEHE: 0.355692
2025-06-18 15:25:38,399 - INFO -   ATE Error: 0.003703
2025-06-18 15:25:38,399 - INFO -     - Main Losses (G): Factual=0.1945, Adversarial=0.7076, Decomp=0.7727
2025-06-18 15:25:38,400 - INFO -     - Discriminator Loss (D): 0.6856
2025-06-18 15:25:38,400 - INFO -     - Balance Loss (Weights): 0.0888
2025-06-18 15:26:15,359 - INFO - Epoch 200:
2025-06-18 15:26:15,360 - INFO -   Total Loss: 3.902315
2025-06-18 15:26:15,361 - INFO -   PEHE: 0.365541
2025-06-18 15:26:15,361 - INFO -   ATE Error: 0.011168
2025-06-18 15:26:15,362 - INFO -     - Main Losses (G): Factual=0.1664, Adversarial=0.7077, Decomp=0.7693
2025-06-18 15:26:15,363 - INFO -     - Discriminator Loss (D): 0.6860
2025-06-18 15:26:15,363 - INFO -     - Balance Loss (Weights): 0.0814
2025-06-18 15:26:15,474 - INFO - Model saved to ./results\models\stage1_model_epoch_200
2025-06-18 15:26:51,632 - INFO - Epoch 220:
2025-06-18 15:26:51,632 - INFO -   Total Loss: 3.890416
2025-06-18 15:26:51,632 - INFO -   PEHE: 0.370523
2025-06-18 15:26:51,632 - INFO -   ATE Error: 0.008823
2025-06-18 15:26:51,632 - INFO -     - Main Losses (G): Factual=0.1560, Adversarial=0.7084, Decomp=0.7671
2025-06-18 15:26:51,632 - INFO -     - Discriminator Loss (D): 0.6855
2025-06-18 15:26:51,632 - INFO -     - Balance Loss (Weights): 0.0780
2025-06-18 15:27:28,261 - INFO - Epoch 240:
2025-06-18 15:27:28,261 - INFO -   Total Loss: 3.907171
2025-06-18 15:27:28,261 - INFO -   PEHE: 0.383544
2025-06-18 15:27:28,261 - INFO -   ATE Error: 0.001487
2025-06-18 15:27:28,261 - INFO -     - Main Losses (G): Factual=0.1697, Adversarial=0.7093, Decomp=0.7680
2025-06-18 15:27:28,261 - INFO -     - Discriminator Loss (D): 0.6852
2025-06-18 15:27:28,261 - INFO -     - Balance Loss (Weights): 0.0682
2025-06-18 15:27:47,552 - INFO - Model saved to ./results\models\stage1_model_epoch_250
2025-06-18 15:28:06,259 - INFO - Epoch 260:
2025-06-18 15:28:06,260 - INFO -   Total Loss: 3.899988
2025-06-18 15:28:06,260 - INFO -   PEHE: 0.370988
2025-06-18 15:28:06,261 - INFO -   ATE Error: 0.000566
2025-06-18 15:28:06,262 - INFO -     - Main Losses (G): Factual=0.1672, Adversarial=0.7087, Decomp=0.7660
2025-06-18 15:28:06,262 - INFO -     - Discriminator Loss (D): 0.6861
2025-06-18 15:28:06,263 - INFO -     - Balance Loss (Weights): 0.0694
2025-06-18 15:28:42,187 - INFO - Epoch 280:
2025-06-18 15:28:42,187 - INFO -   Total Loss: 3.899956
2025-06-18 15:28:42,187 - INFO -   PEHE: 0.370504
2025-06-18 15:28:42,187 - INFO -   ATE Error: 0.002633
2025-06-18 15:28:42,187 - INFO -     - Main Losses (G): Factual=0.1681, Adversarial=0.7088, Decomp=0.7650
2025-06-18 15:28:42,187 - INFO -     - Discriminator Loss (D): 0.6865
2025-06-18 15:28:42,187 - INFO -     - Balance Loss (Weights): 0.0627
2025-06-18 15:29:18,208 - INFO - Epoch 300:
2025-06-18 15:29:18,208 - INFO -   Total Loss: 3.898414
2025-06-18 15:29:18,208 - INFO -   PEHE: 0.375229
2025-06-18 15:29:18,208 - INFO -   ATE Error: 0.011083
2025-06-18 15:29:18,208 - INFO -     - Main Losses (G): Factual=0.1609, Adversarial=0.7106, Decomp=0.7638
2025-06-18 15:29:18,208 - INFO -     - Discriminator Loss (D): 0.6853
2025-06-18 15:29:18,208 - INFO -     - Balance Loss (Weights): 0.0636
2025-06-18 15:29:18,318 - INFO - Model saved to ./results\models\stage1_model_epoch_300
2025-06-18 15:33:22,952 - INFO - Starting Stage 1 training...
2025-06-18 15:33:22,953 - INFO - Training samples: 9120
2025-06-18 15:33:22,953 - INFO - Test samples: 2280
2025-06-18 15:33:22,954 - INFO - Sample weights initialized: shape=(9120, 1)
2025-06-18 15:33:39,909 - INFO - Epoch 0:
2025-06-18 15:33:39,910 - INFO -   Total Loss: 6167.933594
2025-06-18 15:33:39,910 - INFO -   PEHE: 0.316918
2025-06-18 15:33:39,910 - INFO -   ATE Error: 0.058420
2025-06-18 15:33:39,911 - INFO -     - Main Losses (G): Factual=0.6677, Adversarial=0.6940, Decomp=22.6830
2025-06-18 15:33:39,911 - INFO -     - Discriminator Loss (D): 25.6081
2025-06-18 15:33:39,911 - INFO -     - Balance Loss (Weights): 3.0328
2025-06-18 15:33:39,997 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-18 15:33:40,073 - INFO - Model saved to ./results\models\stage1_model_epoch_0
2025-06-18 15:34:15,731 - INFO - Epoch 20:
2025-06-18 15:34:15,732 - INFO -   Total Loss: 633.015808
2025-06-18 15:34:15,733 - INFO -   PEHE: 0.310614
2025-06-18 15:34:15,733 - INFO -   ATE Error: 0.012771
2025-06-18 15:34:15,733 - INFO -     - Main Losses (G): Factual=0.6825, Adversarial=0.6947, Decomp=15.0032
2025-06-18 15:34:15,734 - INFO -     - Discriminator Loss (D): 18.4469
2025-06-18 15:34:15,735 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 15:34:15,960 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-18 15:34:52,465 - INFO - Epoch 40:
2025-06-18 15:34:52,466 - INFO -   Total Loss: 30.733646
2025-06-18 15:34:52,466 - INFO -   PEHE: 0.310626
2025-06-18 15:34:52,467 - INFO -   ATE Error: 0.013044
2025-06-18 15:34:52,468 - INFO -     - Main Losses (G): Factual=0.6825, Adversarial=0.6941, Decomp=15.0032
2025-06-18 15:34:52,468 - INFO -     - Discriminator Loss (D): 12.9594
2025-06-18 15:34:52,469 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 15:35:10,426 - INFO - Model saved to ./results\models\stage1_model_epoch_50
2025-06-18 15:35:28,151 - INFO - Epoch 60:
2025-06-18 15:35:28,152 - INFO -   Total Loss: 26.631338
2025-06-18 15:35:28,152 - INFO -   PEHE: 0.310639
2025-06-18 15:35:28,153 - INFO -   ATE Error: 0.013349
2025-06-18 15:35:28,153 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6932, Decomp=15.0032
2025-06-18 15:35:28,153 - INFO -     - Discriminator Loss (D): 8.8607
2025-06-18 15:35:28,154 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 15:36:03,105 - INFO - Epoch 80:
2025-06-18 15:36:03,106 - INFO -   Total Loss: 23.624521
2025-06-18 15:36:03,107 - INFO -   PEHE: 0.310606
2025-06-18 15:36:03,107 - INFO -   ATE Error: 0.012557
2025-06-18 15:36:03,108 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6932, Decomp=15.0032
2025-06-18 15:36:03,108 - INFO -     - Discriminator Loss (D): 5.8535
2025-06-18 15:36:03,108 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 15:36:03,212 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-18 15:36:38,541 - INFO - Epoch 100:
2025-06-18 15:36:38,542 - INFO -   Total Loss: 21.485773
2025-06-18 15:36:38,542 - INFO -   PEHE: 0.310631
2025-06-18 15:36:38,543 - INFO -   ATE Error: 0.013179
2025-06-18 15:36:38,543 - INFO -     - Main Losses (G): Factual=0.6827, Adversarial=0.6931, Decomp=15.0032
2025-06-18 15:36:38,544 - INFO -     - Discriminator Loss (D): 3.7143
2025-06-18 15:36:38,544 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 15:36:38,656 - INFO - Model saved to ./results\models\stage1_model_epoch_100
2025-06-18 15:37:13,961 - INFO - Epoch 120:
2025-06-18 15:37:13,961 - INFO -   Total Loss: 20.059788
2025-06-18 15:37:13,962 - INFO -   PEHE: 0.310633
2025-06-18 15:37:13,962 - INFO -   ATE Error: 0.013221
2025-06-18 15:37:13,962 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6932, Decomp=15.0032
2025-06-18 15:37:13,963 - INFO -     - Discriminator Loss (D): 2.2878
2025-06-18 15:37:13,963 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 15:37:49,314 - INFO - Epoch 140:
2025-06-18 15:37:49,315 - INFO -   Total Loss: 19.198402
2025-06-18 15:37:49,315 - INFO -   PEHE: 0.310635
2025-06-18 15:37:49,316 - INFO -   ATE Error: 0.013259
2025-06-18 15:37:49,316 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6932, Decomp=15.0032
2025-06-18 15:37:49,317 - INFO -     - Discriminator Loss (D): 1.4262
2025-06-18 15:37:49,317 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 15:38:07,286 - INFO - Model saved to ./results\models\stage1_model_epoch_150
2025-06-18 15:38:25,107 - INFO - Epoch 160:
2025-06-18 15:38:25,107 - INFO -   Total Loss: 18.760118
2025-06-18 15:38:25,108 - INFO -   PEHE: 0.310616
2025-06-18 15:38:25,108 - INFO -   ATE Error: 0.012821
2025-06-18 15:38:25,109 - INFO -     - Main Losses (G): Factual=0.6827, Adversarial=0.6931, Decomp=15.0032
2025-06-18 15:38:25,109 - INFO -     - Discriminator Loss (D): 0.9877
2025-06-18 15:38:25,110 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 15:39:00,837 - INFO - Epoch 180:
2025-06-18 15:39:00,838 - INFO -   Total Loss: 18.570486
2025-06-18 15:39:00,838 - INFO -   PEHE: 0.310611
2025-06-18 15:39:00,838 - INFO -   ATE Error: 0.012688
2025-06-18 15:39:00,839 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6931, Decomp=15.0032
2025-06-18 15:39:00,839 - INFO -     - Discriminator Loss (D): 0.7979
2025-06-18 15:39:00,840 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 15:39:37,077 - INFO - Epoch 200:
2025-06-18 15:39:37,077 - INFO -   Total Loss: 18.485622
2025-06-18 15:39:37,077 - INFO -   PEHE: 0.310603
2025-06-18 15:39:37,077 - INFO -   ATE Error: 0.012482
2025-06-18 15:39:37,077 - INFO -     - Main Losses (G): Factual=0.6827, Adversarial=0.6931, Decomp=15.0032
2025-06-18 15:39:37,077 - INFO -     - Discriminator Loss (D): 0.7130
2025-06-18 15:39:37,077 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 15:39:37,171 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-18 15:39:37,265 - INFO - Model saved to ./results\models\stage1_model_epoch_200
2025-06-18 15:40:13,303 - INFO - Epoch 220:
2025-06-18 15:40:13,304 - INFO -   Total Loss: 18.465797
2025-06-18 15:40:13,305 - INFO -   PEHE: 0.310632
2025-06-18 15:40:13,305 - INFO -   ATE Error: 0.013203
2025-06-18 15:40:13,305 - INFO -     - Main Losses (G): Factual=0.6825, Adversarial=0.6931, Decomp=15.0032
2025-06-18 15:40:13,305 - INFO -     - Discriminator Loss (D): 0.6932
2025-06-18 15:40:13,306 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 15:40:48,707 - INFO - Epoch 240:
2025-06-18 15:40:48,709 - INFO -   Total Loss: 18.465803
2025-06-18 15:40:48,709 - INFO -   PEHE: 0.310629
2025-06-18 15:40:48,709 - INFO -   ATE Error: 0.013116
2025-06-18 15:40:48,714 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6931, Decomp=15.0032
2025-06-18 15:40:48,720 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 15:40:48,721 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 15:41:06,191 - INFO - Model saved to ./results\models\stage1_model_epoch_250
2025-06-18 15:41:23,688 - INFO - Epoch 260:
2025-06-18 15:41:23,688 - INFO -   Total Loss: 18.465803
2025-06-18 15:41:23,688 - INFO -   PEHE: 0.310608
2025-06-18 15:41:23,688 - INFO -   ATE Error: 0.012629
2025-06-18 15:41:23,688 - INFO -     - Main Losses (G): Factual=0.6827, Adversarial=0.6931, Decomp=15.0032
2025-06-18 15:41:23,704 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 15:41:23,705 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 15:41:58,737 - INFO - Epoch 280:
2025-06-18 15:41:58,737 - INFO -   Total Loss: 18.465799
2025-06-18 15:41:58,741 - INFO -   PEHE: 0.310612
2025-06-18 15:41:58,741 - INFO -   ATE Error: 0.012707
2025-06-18 15:41:58,741 - INFO -     - Main Losses (G): Factual=0.6825, Adversarial=0.6931, Decomp=15.0032
2025-06-18 15:41:58,741 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 15:41:58,742 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 15:42:34,304 - INFO - Epoch 300:
2025-06-18 15:42:34,304 - INFO -   Total Loss: 18.465799
2025-06-18 15:42:34,304 - INFO -   PEHE: 0.310611
2025-06-18 15:42:34,304 - INFO -   ATE Error: 0.012695
2025-06-18 15:42:34,304 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6931, Decomp=15.0032
2025-06-18 15:42:34,304 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 15:42:34,304 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 15:42:34,414 - INFO - Model saved to ./results\models\stage1_model_epoch_300
2025-06-18 15:43:09,935 - INFO - Epoch 320:
2025-06-18 15:43:09,935 - INFO -   Total Loss: 18.465784
2025-06-18 15:43:09,935 - INFO -   PEHE: 0.310614
2025-06-18 15:43:09,935 - INFO -   ATE Error: 0.012768
2025-06-18 15:43:09,935 - INFO -     - Main Losses (G): Factual=0.6825, Adversarial=0.6931, Decomp=15.0032
2025-06-18 15:43:09,935 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 15:43:09,935 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 15:43:45,560 - INFO - Epoch 340:
2025-06-18 15:43:45,560 - INFO -   Total Loss: 18.465815
2025-06-18 15:43:45,560 - INFO -   PEHE: 0.310630
2025-06-18 15:43:45,560 - INFO -   ATE Error: 0.013137
2025-06-18 15:43:45,560 - INFO -     - Main Losses (G): Factual=0.6827, Adversarial=0.6931, Decomp=15.0032
2025-06-18 15:43:45,560 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 15:43:45,560 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 15:44:03,410 - INFO - Model saved to ./results\models\stage1_model_epoch_350
2025-06-18 15:44:20,812 - INFO - Epoch 360:
2025-06-18 15:44:20,812 - INFO -   Total Loss: 18.465799
2025-06-18 15:44:20,812 - INFO -   PEHE: 0.310609
2025-06-18 15:44:20,812 - INFO -   ATE Error: 0.012641
2025-06-18 15:44:20,812 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6931, Decomp=15.0032
2025-06-18 15:44:20,812 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 15:44:20,812 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 15:44:56,784 - INFO - Epoch 380:
2025-06-18 15:44:56,785 - INFO -   Total Loss: 18.465794
2025-06-18 15:44:56,785 - INFO -   PEHE: 0.310603
2025-06-18 15:44:56,785 - INFO -   ATE Error: 0.012490
2025-06-18 15:44:56,786 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6931, Decomp=15.0032
2025-06-18 15:44:56,786 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 15:44:56,787 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 15:45:32,457 - INFO - Epoch 400:
2025-06-18 15:45:32,457 - INFO -   Total Loss: 18.465782
2025-06-18 15:45:32,457 - INFO -   PEHE: 0.310606
2025-06-18 15:45:32,457 - INFO -   ATE Error: 0.012569
2025-06-18 15:45:32,457 - INFO -     - Main Losses (G): Factual=0.6827, Adversarial=0.6931, Decomp=15.0032
2025-06-18 15:45:32,457 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 15:45:32,457 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 15:45:32,598 - INFO - Model saved to ./results\models\stage1_model_epoch_400
2025-06-18 15:46:07,917 - INFO - Epoch 420:
2025-06-18 15:46:07,917 - INFO -   Total Loss: 18.465809
2025-06-18 15:46:07,917 - INFO -   PEHE: 0.310602
2025-06-18 15:46:07,917 - INFO -   ATE Error: 0.012471
2025-06-18 15:46:07,917 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6931, Decomp=15.0032
2025-06-18 15:46:07,917 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 15:46:07,917 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 15:46:08,050 - INFO - Model saved to ./results\models\best_stage1_model
2025-06-18 15:46:43,576 - INFO - Epoch 440:
2025-06-18 15:46:43,576 - INFO -   Total Loss: 18.465782
2025-06-18 15:46:43,576 - INFO -   PEHE: 0.310615
2025-06-18 15:46:43,576 - INFO -   ATE Error: 0.012792
2025-06-18 15:46:43,576 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6931, Decomp=15.0032
2025-06-18 15:46:43,576 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 15:46:43,576 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 15:47:01,566 - INFO - Model saved to ./results\models\stage1_model_epoch_450
2025-06-18 15:47:19,474 - INFO - Epoch 460:
2025-06-18 15:47:19,475 - INFO -   Total Loss: 18.465801
2025-06-18 15:47:19,475 - INFO -   PEHE: 0.310622
2025-06-18 15:47:19,476 - INFO -   ATE Error: 0.012956
2025-06-18 15:47:19,477 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6931, Decomp=15.0032
2025-06-18 15:47:19,477 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 15:47:19,478 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 15:47:55,505 - INFO - Epoch 480:
2025-06-18 15:47:55,506 - INFO -   Total Loss: 18.465805
2025-06-18 15:47:55,507 - INFO -   PEHE: 0.310620
2025-06-18 15:47:55,507 - INFO -   ATE Error: 0.012923
2025-06-18 15:47:55,508 - INFO -     - Main Losses (G): Factual=0.6827, Adversarial=0.6931, Decomp=15.0032
2025-06-18 15:47:55,508 - INFO -     - Discriminator Loss (D): 0.6931
2025-06-18 15:47:55,508 - INFO -     - Balance Loss (Weights): 0.0000
2025-06-18 15:48:29,260 - INFO - Stage 1 training completed!
2025-06-18 15:48:29,683 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-06-18 15:48:29,683 - INFO - Stage 2 training data shapes:
2025-06-18 15:48:29,683 - INFO -   x_train: (9120, 30)
2025-06-18 15:48:29,683 - INFO -   y_bar_target: (9120, 2)
2025-06-18 15:48:48,677 - INFO - Stage 2 - Iter: 0/500, ITE_C Loss: 5.8677 (Wass: -0.0000, GP: 0.5868), ITE_G Loss: 1.8766 (Adv: -0.0179, Sup: 0.0253)
2025-06-18 15:49:54,456 - INFO - Stage 2 - Iter: 100/500, ITE_C Loss: 5.3992 (Wass: -0.0000, GP: 0.5399), ITE_G Loss: 0.0765 (Adv: 0.0574, Sup: 0.0003)
2025-06-18 15:51:01,079 - INFO - Stage 2 - Iter: 200/500, ITE_C Loss: 4.8239 (Wass: -0.0000, GP: 0.4824), ITE_G Loss: -0.3012 (Adv: -0.3055, Sup: 0.0001)
2025-06-18 15:52:04,885 - INFO - Stage 2 - Iter: 300/500, ITE_C Loss: 4.6249 (Wass: 0.0000, GP: 0.4625), ITE_G Loss: -0.1460 (Adv: -0.1526, Sup: 0.0001)
2025-06-18 15:53:13,287 - INFO - Stage 2 - Iter: 400/500, ITE_C Loss: 4.6548 (Wass: 0.0000, GP: 0.4655), ITE_G Loss: -0.3709 (Adv: -0.3745, Sup: 0.0000)
2025-06-18 15:54:24,889 - INFO - Stage 2 - Iter: 499/500, ITE_C Loss: 5.2350 (Wass: -0.0000, GP: 0.5235), ITE_G Loss: -0.0490 (Adv: -0.0525, Sup: 0.0000)
2025-06-18 15:54:24,890 - INFO - Stage 2 training completed!
2025-06-18 15:54:25,150 - INFO - Stage2 model save skipped for quick test
2025-07-20 10:03:33,962 - INFO - Starting Stage 1 training...
2025-07-20 10:03:33,964 - INFO - Training samples: 9120
2025-07-20 10:03:33,964 - INFO - Test samples: 2280
2025-07-20 10:03:33,964 - INFO - Sample weights initialized: shape=(9120, 1)
2025-07-20 10:03:34,340 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-07-20 10:03:35,667 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-07-20 10:03:43,352 - INFO - Epoch 0:
2025-07-20 10:03:43,352 - INFO -   Total Loss: 6178.942871
2025-07-20 10:03:43,352 - INFO -   PEHE: 0.335217
2025-07-20 10:03:43,352 - INFO -   ATE Error: 0.113223
2025-07-20 10:03:43,352 - INFO -     - Main Losses (G): Factual=0.6974, Adversarial=0.7085, Decomp=21.3441
2025-07-20 10:03:43,352 - INFO -     - Discriminator Loss (D): 31.3628
2025-07-20 10:03:43,352 - INFO -     - Balance Loss (Weights): 2.9960
2025-07-20 10:10:16,753 - INFO - Starting Stage 1 training...
2025-07-20 10:10:16,753 - INFO - Training samples: 9120
2025-07-20 10:10:16,753 - INFO - Test samples: 2280
2025-07-20 10:10:16,753 - INFO - Sample weights initialized: shape=(9120, 1)
2025-07-20 10:10:17,058 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-07-20 10:10:18,438 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-07-20 10:10:26,172 - INFO - Epoch 0:
2025-07-20 10:10:26,172 - INFO -   Total Loss: 6168.641602
2025-07-20 10:10:26,172 - INFO -   PEHE: 0.361015
2025-07-20 10:10:26,172 - INFO -   ATE Error: 0.159847
2025-07-20 10:10:26,172 - INFO -     - Main Losses (G): Factual=0.7669, Adversarial=0.6796, Decomp=19.9905
2025-07-20 10:10:26,172 - INFO -     - Discriminator Loss (D): 27.5178
2025-07-20 10:10:26,172 - INFO -     - Balance Loss (Weights): 3.0389
2025-07-20 10:11:20,507 - INFO - Starting Stage 1 training...
2025-07-20 10:11:20,507 - INFO - Training samples: 9120
2025-07-20 10:11:20,507 - INFO - Test samples: 2280
2025-07-20 10:11:20,507 - INFO - Sample weights initialized: shape=(9120, 1)
2025-07-20 10:11:20,814 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-07-20 10:11:22,123 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-07-20 10:11:29,751 - INFO - Epoch 0:
2025-07-20 10:11:29,752 - INFO -   Total Loss: 6175.944824
2025-07-20 10:11:29,752 - INFO -   PEHE: 0.323136
2025-07-20 10:11:29,752 - INFO -   ATE Error: 0.078022
2025-07-20 10:11:29,753 - INFO -     - Main Losses (G): Factual=0.9112, Adversarial=0.7014, Decomp=22.4552
2025-07-20 10:11:29,753 - INFO -     - Discriminator Loss (D): 33.0597
2025-07-20 10:11:29,753 - INFO -     - Balance Loss (Weights): 2.9100
2025-07-20 10:12:55,031 - INFO - Starting Stage 1 training...
2025-07-20 10:12:55,031 - INFO - Training samples: 9120
2025-07-20 10:12:55,031 - INFO - Test samples: 2280
2025-07-20 10:12:55,031 - INFO - Sample weights initialized: shape=(9120, 1)
2025-07-20 10:12:55,360 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-07-20 10:12:56,682 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-07-20 10:13:04,723 - INFO - Epoch 0:
2025-07-20 10:13:04,723 - INFO -   Total Loss: 6172.095703
2025-07-20 10:13:04,723 - INFO -   PEHE: 0.324319
2025-07-20 10:13:04,723 - INFO -   ATE Error: 0.012791
2025-07-20 10:13:04,723 - INFO -     - Main Losses (G): Factual=0.6656, Adversarial=0.6925, Decomp=20.3801
2025-07-20 10:13:04,726 - INFO -     - Discriminator Loss (D): 35.4774
2025-07-20 10:13:04,726 - INFO -     - Balance Loss (Weights): 2.8625
2025-07-20 10:13:57,482 - INFO - Starting Stage 1 training...
2025-07-20 10:13:57,483 - INFO - Training samples: 9120
2025-07-20 10:13:57,483 - INFO - Test samples: 2280
2025-07-20 10:13:57,484 - INFO - Sample weights initialized: shape=(9120, 1)
2025-07-20 10:13:57,790 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-07-20 10:13:59,290 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-07-20 10:14:07,938 - INFO - Epoch 0:
2025-07-20 10:14:07,939 - INFO -   Total Loss: 6166.609375
2025-07-20 10:14:07,939 - INFO -   PEHE: 0.325790
2025-07-20 10:14:07,939 - INFO -   ATE Error: 0.010164
2025-07-20 10:14:07,939 - INFO -     - Main Losses (G): Factual=0.7032, Adversarial=0.6997, Decomp=21.3986
2025-07-20 10:14:07,939 - INFO -     - Discriminator Loss (D): 31.4576
2025-07-20 10:14:07,939 - INFO -     - Balance Loss (Weights): 3.0689
2025-07-20 10:14:07,940 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 10:14:08,029 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-07-20 10:14:08,030 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 10:14:08,077 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-07-20 10:14:51,999 - INFO - Epoch 50:
2025-07-20 10:14:51,999 - INFO -   Total Loss: 31.416174
2025-07-20 10:14:51,999 - INFO -   PEHE: 0.319665
2025-07-20 10:14:51,999 - INFO -   ATE Error: 0.010615
2025-07-20 10:14:51,999 - INFO -     - Main Losses (G): Factual=0.6829, Adversarial=0.6927, Decomp=15.0032
2025-07-20 10:14:51,999 - INFO -     - Discriminator Loss (D): 13.6490
2025-07-20 10:14:51,999 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 10:14:52,003 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 10:14:52,043 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-07-20 10:15:34,834 - INFO - Stage 1 training completed!
2025-07-20 10:15:35,034 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-07-20 10:15:35,034 - INFO - Stage 2 training data shapes:
2025-07-20 10:15:35,034 - INFO -   x_train: (9120, 30)
2025-07-20 10:15:35,034 - INFO -   y_bar_target: (9120, 2)
2025-07-20 10:15:35,362 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\layers\normalization\layer_normalization.py:328: The name tf.nn.fused_batch_norm is deprecated. Please use tf.compat.v1.nn.fused_batch_norm instead.

2025-07-20 10:15:44,082 - INFO - Stage 2 - Iter: 0/50, ITE_C Loss: 5.6449 (Wass: 0.0037, GP: 0.5641), ITE_G Loss: 3.4252 (Adv: 1.3430, Sup: 0.0278)
2025-07-20 10:15:52,020 - INFO - Stage 2 - Iter: 49/50, ITE_C Loss: 6.1045 (Wass: -0.0000, GP: 0.6105), ITE_G Loss: 1.3214 (Adv: 1.2289, Sup: 0.0012)
2025-07-20 10:15:52,020 - INFO - Stage 2 training completed!
2025-07-20 10:15:52,036 - INFO - Stage2 model save skipped for quick test
2025-07-20 10:24:35,095 - INFO - Starting Stage 1 training...
2025-07-20 10:24:35,095 - INFO - Training samples: 9120
2025-07-20 10:24:35,104 - INFO - Test samples: 2280
2025-07-20 10:24:35,104 - INFO - Sample weights initialized: shape=(9120, 1)
2025-07-20 10:24:35,442 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-07-20 10:24:36,856 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-07-20 10:24:45,300 - INFO - Epoch 0:
2025-07-20 10:24:45,300 - INFO -   Total Loss: 6167.781738
2025-07-20 10:24:45,300 - INFO -   PEHE: 0.330077
2025-07-20 10:24:45,306 - INFO -   ATE Error: 0.067626
2025-07-20 10:24:45,306 - INFO -     - Main Losses (G): Factual=0.7636, Adversarial=0.6984, Decomp=21.4605
2025-07-20 10:24:45,306 - INFO -     - Discriminator Loss (D): 32.6439
2025-07-20 10:24:45,306 - INFO -     - Balance Loss (Weights): 2.8572
2025-07-20 10:24:45,308 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 10:24:45,379 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-07-20 10:24:45,379 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 10:24:45,428 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-07-20 10:25:06,437 - INFO - Epoch 25:
2025-07-20 10:25:06,437 - INFO -   Total Loss: 304.773682
2025-07-20 10:25:06,437 - INFO -   PEHE: 0.319741
2025-07-20 10:25:06,437 - INFO -   ATE Error: 0.012718
2025-07-20 10:25:06,437 - INFO -     - Main Losses (G): Factual=0.6830, Adversarial=0.6943, Decomp=15.0032
2025-07-20 10:25:06,437 - INFO -     - Discriminator Loss (D): 22.4964
2025-07-20 10:25:06,437 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 10:25:06,437 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 10:25:06,487 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-07-20 10:25:27,816 - INFO - Stage 1 training completed!
2025-07-20 10:25:28,022 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-07-20 10:25:28,022 - INFO - Stage 2 training data shapes:
2025-07-20 10:25:28,022 - INFO -   x_train: (9120, 30)
2025-07-20 10:25:28,022 - INFO -   y_bar_target: (9120, 2)
2025-07-20 10:25:28,289 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\layers\normalization\layer_normalization.py:328: The name tf.nn.fused_batch_norm is deprecated. Please use tf.compat.v1.nn.fused_batch_norm instead.

2025-07-20 10:25:36,415 - INFO - Stage 2 - Iter: 0/25, ITE_C Loss: 4.1736 (Wass: -0.0003, GP: 0.4174), ITE_G Loss: 3.8118 (Adv: 2.0719, Sup: 0.0232)
2025-07-20 10:25:40,180 - INFO - Stage 2 - Iter: 24/25, ITE_C Loss: 3.8062 (Wass: -0.0001, GP: 0.3806), ITE_G Loss: 2.1111 (Adv: 1.9742, Sup: 0.0018)
2025-07-20 10:25:40,180 - INFO - Stage 2 training completed!
2025-07-20 10:25:40,202 - INFO - Stage2 model save skipped for quick test
2025-07-20 10:32:35,662 - INFO - Starting Stage 1 training...
2025-07-20 10:32:35,662 - INFO - Training samples: 9120
2025-07-20 10:32:35,662 - INFO - Test samples: 2280
2025-07-20 10:32:35,662 - INFO - Sample weights initialized: shape=(9120, 1)
2025-07-20 10:32:35,985 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-07-20 10:32:37,122 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-07-20 10:32:44,069 - INFO - Epoch 0:
2025-07-20 10:32:44,069 - INFO -   Total Loss: 6180.576172
2025-07-20 10:32:44,069 - INFO -   PEHE: 0.327878
2025-07-20 10:32:44,069 - INFO -   ATE Error: 0.049693
2025-07-20 10:32:44,069 - INFO -     - Main Losses (G): Factual=0.7951, Adversarial=0.8222, Decomp=21.7605
2025-07-20 10:32:44,069 - INFO -     - Discriminator Loss (D): 32.7002
2025-07-20 10:32:44,069 - INFO -     - Balance Loss (Weights): 2.9355
2025-07-20 10:32:44,085 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 10:32:44,205 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-07-20 10:32:44,205 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 10:32:44,257 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-07-20 10:34:17,642 - INFO - Epoch 100:
2025-07-20 10:34:17,642 - INFO -   Total Loss: 22.729761
2025-07-20 10:34:17,642 - INFO -   PEHE: 0.319648
2025-07-20 10:34:17,642 - INFO -   ATE Error: 0.010109
2025-07-20 10:34:17,642 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6962, Decomp=15.0032
2025-07-20 10:34:17,642 - INFO -     - Discriminator Loss (D): 4.9433
2025-07-20 10:34:17,642 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 10:34:17,642 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 10:34:17,685 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-07-20 10:35:45,496 - INFO - Epoch 200:
2025-07-20 10:35:45,496 - INFO -   Total Loss: 18.625904
2025-07-20 10:35:45,496 - INFO -   PEHE: 0.319662
2025-07-20 10:35:45,496 - INFO -   ATE Error: 0.010546
2025-07-20 10:35:45,496 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=15.0032
2025-07-20 10:35:45,496 - INFO -     - Discriminator Loss (D): 0.8532
2025-07-20 10:35:45,496 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 10:37:11,204 - INFO - Epoch 300:
2025-07-20 10:37:11,204 - INFO -   Total Loss: 18.465900
2025-07-20 10:37:11,204 - INFO -   PEHE: 0.319666
2025-07-20 10:37:11,204 - INFO -   ATE Error: 0.010672
2025-07-20 10:37:11,204 - INFO -     - Main Losses (G): Factual=0.6829, Adversarial=0.6931, Decomp=15.0032
2025-07-20 10:37:11,204 - INFO -     - Discriminator Loss (D): 0.6931
2025-07-20 10:37:11,204 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 10:38:42,215 - INFO - Epoch 400:
2025-07-20 10:38:42,215 - INFO -   Total Loss: 18.465889
2025-07-20 10:38:42,215 - INFO -   PEHE: 0.319681
2025-07-20 10:38:42,215 - INFO -   ATE Error: 0.011109
2025-07-20 10:38:42,215 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=15.0032
2025-07-20 10:38:42,215 - INFO -     - Discriminator Loss (D): 0.6931
2025-07-20 10:38:42,215 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 10:40:10,807 - INFO - Stage 1 training completed!
2025-07-20 10:40:11,048 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-07-20 10:40:11,048 - INFO - Stage 2 training data shapes:
2025-07-20 10:40:11,048 - INFO -   x_train: (9120, 30)
2025-07-20 10:40:11,048 - INFO -   y_bar_target: (9120, 2)
2025-07-20 10:40:11,436 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\layers\normalization\layer_normalization.py:328: The name tf.nn.fused_batch_norm is deprecated. Please use tf.compat.v1.nn.fused_batch_norm instead.

2025-07-20 10:40:20,150 - INFO - Stage 2 - Iter: 0/200, ITE_C Loss: 6.1390 (Wass: -0.0001, GP: 0.6139), ITE_G Loss: 1.2370 (Adv: -0.2309, Sup: 0.0196)
2025-07-20 10:40:39,719 - INFO - Stage 2 - Iter: 100/200, ITE_C Loss: 5.8148 (Wass: 0.0000, GP: 0.5815), ITE_G Loss: -0.1715 (Adv: -0.1931, Sup: 0.0003)
2025-07-20 10:40:56,835 - INFO - Stage 2 - Iter: 199/200, ITE_C Loss: 5.8003 (Wass: -0.0000, GP: 0.5800), ITE_G Loss: -0.0261 (Adv: -0.0365, Sup: 0.0001)
2025-07-20 10:40:56,835 - INFO - Stage 2 training completed!
2025-07-20 10:40:56,855 - INFO - Stage2 model save skipped for quick test
2025-07-20 13:06:15,586 - INFO - Starting Stage 1 training...
2025-07-20 13:06:15,587 - INFO - Training samples: 9120
2025-07-20 13:06:15,588 - INFO - Test samples: 2280
2025-07-20 13:06:15,588 - INFO - Sample weights initialized: shape=(9120, 1)
2025-07-20 13:06:15,874 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-07-20 13:06:17,255 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-07-20 13:06:25,268 - INFO - Epoch 0:
2025-07-20 13:06:25,268 - INFO -   Total Loss: 6172.007324
2025-07-20 13:06:25,268 - INFO -   PEHE: 0.327869
2025-07-20 13:06:25,268 - INFO -   ATE Error: 0.058465
2025-07-20 13:06:25,268 - INFO -     - Main Losses (G): Factual=0.7046, Adversarial=0.7009, Decomp=20.8282
2025-07-20 13:06:25,268 - INFO -     - Discriminator Loss (D): 33.5735
2025-07-20 13:06:25,283 - INFO -     - Balance Loss (Weights): 2.8445
2025-07-20 13:06:25,283 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 13:06:25,401 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-07-20 13:06:25,401 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 13:06:25,438 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-07-20 13:08:18,485 - INFO - Epoch 100:
2025-07-20 13:08:18,485 - INFO -   Total Loss: 22.643965
2025-07-20 13:08:18,485 - INFO -   PEHE: 0.319655
2025-07-20 13:08:18,485 - INFO -   ATE Error: 0.010327
2025-07-20 13:08:18,485 - INFO -     - Main Losses (G): Factual=0.6830, Adversarial=0.6937, Decomp=15.0032
2025-07-20 13:08:18,485 - INFO -     - Discriminator Loss (D): 4.8704
2025-07-20 13:08:18,485 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 13:08:18,485 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 13:08:18,529 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-07-20 13:09:57,326 - INFO - Epoch 200:
2025-07-20 13:09:57,326 - INFO -   Total Loss: 18.494446
2025-07-20 13:09:57,326 - INFO -   PEHE: 0.319662
2025-07-20 13:09:57,326 - INFO -   ATE Error: 0.010551
2025-07-20 13:09:57,326 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=15.0032
2025-07-20 13:09:57,326 - INFO -     - Discriminator Loss (D): 0.7217
2025-07-20 13:09:57,326 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 13:21:05,552 - INFO - Starting Stage 1 training...
2025-07-20 13:21:05,552 - INFO - Training samples: 9120
2025-07-20 13:21:05,552 - INFO - Test samples: 2280
2025-07-20 13:21:05,552 - INFO - Sample weights initialized: shape=(9120, 1)
2025-07-20 13:21:05,885 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-07-20 13:21:07,285 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-07-20 13:21:16,516 - INFO - Epoch 0:
2025-07-20 13:21:16,516 - INFO -   Total Loss: 6168.531738
2025-07-20 13:21:16,516 - INFO -   PEHE: 0.326879
2025-07-20 13:21:16,516 - INFO -   ATE Error: 0.031199
2025-07-20 13:21:16,516 - INFO -     - Main Losses (G): Factual=0.6317, Adversarial=0.7002, Decomp=21.0635
2025-07-20 13:21:16,516 - INFO -     - Discriminator Loss (D): 29.4328
2025-07-20 13:21:16,516 - INFO -     - Balance Loss (Weights): 3.0667
2025-07-20 13:21:16,525 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 13:21:16,630 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-07-20 13:21:16,630 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 13:21:16,668 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-07-20 13:21:39,259 - INFO - Epoch 25:
2025-07-20 13:21:39,259 - INFO -   Total Loss: 301.999054
2025-07-20 13:21:39,259 - INFO -   PEHE: 0.319676
2025-07-20 13:21:39,259 - INFO -   ATE Error: 0.010941
2025-07-20 13:21:39,259 - INFO -     - Main Losses (G): Factual=0.6827, Adversarial=0.6952, Decomp=15.0032
2025-07-20 13:21:39,259 - INFO -     - Discriminator Loss (D): 19.7169
2025-07-20 13:21:39,259 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 13:21:39,275 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 13:21:39,362 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-07-20 13:22:01,643 - INFO - Stage 1 training completed!
2025-07-20 13:22:01,893 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-07-20 13:22:01,893 - INFO - Stage 2 training data shapes:
2025-07-20 13:22:01,893 - INFO -   x_train: (9120, 30)
2025-07-20 13:22:01,893 - INFO -   y_bar_target: (9120, 2)
2025-07-20 13:22:02,327 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\layers\normalization\layer_normalization.py:328: The name tf.nn.fused_batch_norm is deprecated. Please use tf.compat.v1.nn.fused_batch_norm instead.

2025-07-20 13:22:10,822 - INFO - Stage 2 - Iter: 0/100, ITE_C Loss: 4.4772 (Wass: 0.0012, GP: 0.4476), ITE_G Loss: 1.9486 (Adv: 0.1162, Sup: 0.0244)
2025-07-20 13:22:25,419 - INFO - Stage 2 - Iter: 99/100, ITE_C Loss: 4.4487 (Wass: 0.0000, GP: 0.4449), ITE_G Loss: 0.0248 (Adv: 0.0005, Sup: 0.0003)
2025-07-20 13:22:25,419 - INFO - Stage 2 training completed!
2025-07-20 13:22:25,444 - INFO - Stage2 model save skipped for quick test
2025-07-20 13:23:40,271 - INFO - Starting Stage 1 training...
2025-07-20 13:23:40,271 - INFO - Training samples: 9120
2025-07-20 13:23:40,271 - INFO - Test samples: 2280
2025-07-20 13:23:40,271 - INFO - Sample weights initialized: shape=(9120, 1)
2025-07-20 13:23:40,564 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-07-20 13:23:41,715 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-07-20 13:23:48,669 - INFO - Epoch 0:
2025-07-20 13:23:48,669 - INFO -   Total Loss: 6144.169434
2025-07-20 13:23:48,669 - INFO -   PEHE: 0.343272
2025-07-20 13:23:48,669 - INFO -   ATE Error: 0.103968
2025-07-20 13:23:48,669 - INFO -     - Main Losses (G): Factual=0.7555, Adversarial=0.6844, Decomp=18.3618
2025-07-20 13:23:48,669 - INFO -     - Discriminator Loss (D): 29.8361
2025-07-20 13:23:48,669 - INFO -     - Balance Loss (Weights): 2.8116
2025-07-20 13:23:48,669 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 13:23:48,751 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-07-20 13:23:48,752 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 13:23:48,787 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-07-20 13:25:50,194 - INFO - Epoch 100:
2025-07-20 13:25:50,194 - INFO -   Total Loss: 21.711926
2025-07-20 13:25:50,194 - INFO -   PEHE: 0.319662
2025-07-20 13:25:50,194 - INFO -   ATE Error: 0.010550
2025-07-20 13:25:50,194 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6937, Decomp=15.0032
2025-07-20 13:25:50,194 - INFO -     - Discriminator Loss (D): 3.9379
2025-07-20 13:25:50,194 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 13:25:50,194 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 13:25:50,267 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-07-20 13:27:43,387 - INFO - Epoch 200:
2025-07-20 13:27:43,387 - INFO -   Total Loss: 18.632385
2025-07-20 13:27:43,387 - INFO -   PEHE: 0.319663
2025-07-20 13:27:43,387 - INFO -   ATE Error: 0.010553
2025-07-20 13:27:43,391 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=15.0032
2025-07-20 13:27:43,391 - INFO -     - Discriminator Loss (D): 0.8597
2025-07-20 13:27:43,391 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 13:34:26,840 - INFO - Starting Stage 1 training...
2025-07-20 13:34:26,840 - INFO - Training samples: 9120
2025-07-20 13:34:26,840 - INFO - Test samples: 2280
2025-07-20 13:34:26,840 - INFO - Sample weights initialized: shape=(9120, 1)
2025-07-20 13:34:27,177 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-07-20 13:34:28,599 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-07-20 13:34:36,306 - INFO - Epoch 0:
2025-07-20 13:34:36,306 - INFO -   Total Loss: 6162.319336
2025-07-20 13:34:36,306 - INFO -   PEHE: 0.337747
2025-07-20 13:34:36,306 - INFO -   ATE Error: 0.104267
2025-07-20 13:34:36,306 - INFO -     - Main Losses (G): Factual=0.6934, Adversarial=0.7008, Decomp=18.9249
2025-07-20 13:34:36,307 - INFO -     - Discriminator Loss (D): 32.6065
2025-07-20 13:34:36,307 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 13:34:36,308 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 13:34:36,386 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-07-20 13:34:36,389 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 13:34:36,431 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-07-20 13:35:14,594 - INFO - Epoch 25:
2025-07-20 13:35:14,594 - INFO -   Total Loss: 304.387573
2025-07-20 13:35:14,594 - INFO -   PEHE: 0.319787
2025-07-20 13:35:14,594 - INFO -   ATE Error: 0.013825
2025-07-20 13:35:14,594 - INFO -     - Main Losses (G): Factual=0.6823, Adversarial=0.7061, Decomp=15.0032
2025-07-20 13:35:14,626 - INFO -     - Discriminator Loss (D): 22.0539
2025-07-20 13:35:14,626 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 13:35:14,630 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 13:35:14,761 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-07-20 13:36:01,246 - INFO - Epoch 50:
2025-07-20 13:36:01,246 - INFO -   Total Loss: 31.897619
2025-07-20 13:36:01,246 - INFO -   PEHE: 0.319734
2025-07-20 13:36:01,250 - INFO -   ATE Error: 0.012529
2025-07-20 13:36:01,250 - INFO -     - Main Losses (G): Factual=0.6825, Adversarial=0.7014, Decomp=15.0032
2025-07-20 13:36:01,250 - INFO -     - Discriminator Loss (D): 14.0879
2025-07-20 13:36:01,250 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 13:36:01,254 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 13:36:01,399 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-07-20 13:36:32,133 - INFO - Epoch 75:
2025-07-20 13:36:32,133 - INFO -   Total Loss: 26.154339
2025-07-20 13:36:32,133 - INFO -   PEHE: 0.319694
2025-07-20 13:36:32,133 - INFO -   ATE Error: 0.011455
2025-07-20 13:36:32,133 - INFO -     - Main Losses (G): Factual=0.6824, Adversarial=0.6977, Decomp=15.0032
2025-07-20 13:36:32,133 - INFO -     - Discriminator Loss (D): 8.3614
2025-07-20 13:36:32,133 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 13:36:32,143 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 13:36:32,221 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-07-20 13:36:47,905 - INFO - Stage 1 training completed!
2025-07-20 13:36:48,032 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-07-20 13:36:48,032 - INFO - Stage 2 training data shapes:
2025-07-20 13:36:48,032 - INFO -   x_train: (9120, 30)
2025-07-20 13:36:48,032 - INFO -   y_bar_target: (9120, 2)
2025-07-20 13:36:48,362 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\layers\normalization\layer_normalization.py:328: The name tf.nn.fused_batch_norm is deprecated. Please use tf.compat.v1.nn.fused_batch_norm instead.

2025-07-20 13:36:56,513 - INFO - Stage 2 - Iter: 0/50, ITE_C Loss: 5.7051 (Wass: -0.0011, GP: 0.5706), ITE_G Loss: 0.0238 (Adv: -1.5911, Sup: 0.0215)
2025-07-20 13:37:04,212 - INFO - Stage 2 - Iter: 49/50, ITE_C Loss: 6.3040 (Wass: -0.0000, GP: 0.6304), ITE_G Loss: -1.3484 (Adv: -1.4008, Sup: 0.0007)
2025-07-20 13:37:04,212 - INFO - Stage 2 training completed!
2025-07-20 13:37:04,232 - INFO - Stage2 model save skipped for quick test
2025-07-20 13:50:13,094 - INFO - Starting Stage 1 training...
2025-07-20 13:50:13,107 - INFO - Training samples: 9120
2025-07-20 13:50:13,108 - INFO - Test samples: 2280
2025-07-20 13:50:13,108 - INFO - Sample weights initialized: shape=(9120, 1)
2025-07-20 13:50:13,457 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-07-20 13:50:14,989 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-07-20 13:50:22,767 - INFO - Epoch 0:
2025-07-20 13:50:22,767 - INFO -   Total Loss: 1.944770
2025-07-20 13:50:22,767 - INFO -   PEHE: 0.324997
2025-07-20 13:50:22,767 - INFO -   ATE Error: 0.002876
2025-07-20 13:50:22,767 - INFO -     - Main Losses (G): Factual=0.5051, Adversarial=0.7370, Decomp=0.0603
2025-07-20 13:50:22,767 - INFO -     - Discriminator Loss (D): 0.6604
2025-07-20 13:50:22,767 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 13:50:22,771 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 13:50:22,857 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-07-20 13:50:22,857 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 13:50:22,913 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-07-20 13:50:39,566 - INFO - Epoch 25:
2025-07-20 13:50:39,566 - INFO -   Total Loss: 0.729863
2025-07-20 13:50:39,566 - INFO -   PEHE: 0.324168
2025-07-20 13:50:39,566 - INFO -   ATE Error: 0.014983
2025-07-20 13:50:39,566 - INFO -     - Main Losses (G): Factual=0.2748, Adversarial=0.7385, Decomp=0.0120
2025-07-20 13:50:39,566 - INFO -     - Discriminator Loss (D): 0.6637
2025-07-20 13:50:39,566 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 13:50:39,566 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 13:50:39,604 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-07-20 13:50:56,764 - INFO - Epoch 50:
2025-07-20 13:50:56,765 - INFO -   Total Loss: 0.475075
2025-07-20 13:50:56,765 - INFO -   PEHE: 0.327342
2025-07-20 13:50:56,765 - INFO -   ATE Error: 0.001654
2025-07-20 13:50:56,765 - INFO -     - Main Losses (G): Factual=0.2539, Adversarial=0.7663, Decomp=0.0054
2025-07-20 13:50:56,765 - INFO -     - Discriminator Loss (D): 0.6450
2025-07-20 13:50:56,765 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 13:51:13,726 - INFO - Epoch 75:
2025-07-20 13:51:13,726 - INFO -   Total Loss: 0.393664
2025-07-20 13:51:13,726 - INFO -   PEHE: 0.332321
2025-07-20 13:51:13,726 - INFO -   ATE Error: 0.000167
2025-07-20 13:51:13,726 - INFO -     - Main Losses (G): Factual=0.2417, Adversarial=0.7965, Decomp=0.0038
2025-07-20 13:51:13,741 - INFO -     - Discriminator Loss (D): 0.6275
2025-07-20 13:51:13,741 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 13:51:30,318 - INFO - Stage 1 training completed!
2025-07-20 13:51:30,526 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-07-20 13:51:30,526 - INFO - Stage 2 training data shapes:
2025-07-20 13:51:30,526 - INFO -   x_train: (9120, 30)
2025-07-20 13:51:30,526 - INFO -   y_bar_target: (9120, 2)
2025-07-20 13:51:30,959 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\layers\normalization\layer_normalization.py:328: The name tf.nn.fused_batch_norm is deprecated. Please use tf.compat.v1.nn.fused_batch_norm instead.

2025-07-20 13:51:39,815 - INFO - Stage 2 - Iter: 0/50, ITE_C Loss: 5.8536 (Wass: -0.0158, GP: 0.5869), ITE_G Loss: 24.8337 (Adv: 0.2392, Sup: 0.3279)
2025-07-20 13:51:47,020 - INFO - Stage 2 - Iter: 49/50, ITE_C Loss: 6.0573 (Wass: 0.0022, GP: 0.6055), ITE_G Loss: 3.9429 (Adv: 0.3076, Sup: 0.0485)
2025-07-20 13:51:47,020 - INFO - Stage 2 training completed!
2025-07-20 13:51:47,056 - INFO - Stage2 model save skipped for quick test
2025-07-20 14:13:14,131 - INFO - Starting Stage 1 training...
2025-07-20 14:13:14,132 - INFO - Training samples: 9120
2025-07-20 14:13:14,132 - INFO - Test samples: 2280
2025-07-20 14:13:14,133 - INFO - Sample weights initialized: shape=(9120, 1)
2025-07-20 14:13:14,567 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-07-20 14:13:15,971 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-07-20 14:13:22,822 - INFO - Epoch 0:
2025-07-20 14:13:22,822 - INFO -   Total Loss: 2.109441
2025-07-20 14:13:22,822 - INFO -   PEHE: 0.326559
2025-07-20 14:13:22,822 - INFO -   ATE Error: 0.004997
2025-07-20 14:13:22,822 - INFO -     - Main Losses (G): Factual=0.6703, Adversarial=0.6669, Decomp=0.0589
2025-07-20 14:13:22,822 - INFO -     - Discriminator Loss (D): 0.7512
2025-07-20 14:13:22,822 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:13:22,822 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 14:13:22,892 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-07-20 14:13:22,892 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 14:13:22,929 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-07-20 14:13:58,130 - INFO - Epoch 50:
2025-07-20 14:13:58,130 - INFO -   Total Loss: 0.470573
2025-07-20 14:13:58,130 - INFO -   PEHE: 0.330714
2025-07-20 14:13:58,130 - INFO -   ATE Error: 0.021374
2025-07-20 14:13:58,130 - INFO -     - Main Losses (G): Factual=0.2588, Adversarial=0.7369, Decomp=0.0053
2025-07-20 14:13:58,130 - INFO -     - Discriminator Loss (D): 0.6615
2025-07-20 14:13:58,130 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:14:32,062 - INFO - Epoch 100:
2025-07-20 14:14:32,062 - INFO -   Total Loss: 0.334643
2025-07-20 14:14:32,062 - INFO -   PEHE: 0.341343
2025-07-20 14:14:32,062 - INFO -   ATE Error: 0.004941
2025-07-20 14:14:32,062 - INFO -     - Main Losses (G): Factual=0.2199, Adversarial=0.7686, Decomp=0.0033
2025-07-20 14:14:32,062 - INFO -     - Discriminator Loss (D): 0.6405
2025-07-20 14:14:32,062 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:15:31,019 - INFO - Epoch 150:
2025-07-20 14:15:31,019 - INFO -   Total Loss: 0.298960
2025-07-20 14:15:31,019 - INFO -   PEHE: 0.352268
2025-07-20 14:15:31,019 - INFO -   ATE Error: 0.006110
2025-07-20 14:15:31,019 - INFO -     - Main Losses (G): Factual=0.2019, Adversarial=0.8031, Decomp=0.0030
2025-07-20 14:15:31,019 - INFO -     - Discriminator Loss (D): 0.6224
2025-07-20 14:15:31,019 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:16:51,349 - INFO - Epoch 200:
2025-07-20 14:16:51,349 - INFO -   Total Loss: 0.268066
2025-07-20 14:16:51,349 - INFO -   PEHE: 0.354016
2025-07-20 14:16:51,349 - INFO -   ATE Error: 0.019004
2025-07-20 14:16:51,349 - INFO -     - Main Losses (G): Factual=0.1740, Adversarial=0.8453, Decomp=0.0029
2025-07-20 14:16:51,349 - INFO -     - Discriminator Loss (D): 0.6037
2025-07-20 14:16:51,349 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:18:08,867 - INFO - Epoch 250:
2025-07-20 14:18:08,867 - INFO -   Total Loss: 0.254405
2025-07-20 14:18:08,867 - INFO -   PEHE: 0.358232
2025-07-20 14:18:08,868 - INFO -   ATE Error: 0.004924
2025-07-20 14:18:08,868 - INFO -     - Main Losses (G): Factual=0.1594, Adversarial=0.8875, Decomp=0.0028
2025-07-20 14:18:08,868 - INFO -     - Discriminator Loss (D): 0.5887
2025-07-20 14:18:08,868 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:18:41,598 - INFO - Epoch 300:
2025-07-20 14:18:41,598 - INFO -   Total Loss: 0.242534
2025-07-20 14:18:41,599 - INFO -   PEHE: 0.376086
2025-07-20 14:18:41,599 - INFO -   ATE Error: 0.022124
2025-07-20 14:18:41,599 - INFO -     - Main Losses (G): Factual=0.1455, Adversarial=0.9522, Decomp=0.0028
2025-07-20 14:18:41,599 - INFO -     - Discriminator Loss (D): 0.5684
2025-07-20 14:18:41,599 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:19:14,654 - INFO - Epoch 350:
2025-07-20 14:19:14,654 - INFO -   Total Loss: 0.240863
2025-07-20 14:19:14,655 - INFO -   PEHE: 0.375709
2025-07-20 14:19:14,655 - INFO -   ATE Error: 0.011140
2025-07-20 14:19:14,655 - INFO -     - Main Losses (G): Factual=0.1414, Adversarial=1.0175, Decomp=0.0027
2025-07-20 14:19:14,656 - INFO -     - Discriminator Loss (D): 0.5516
2025-07-20 14:19:14,656 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:19:48,583 - INFO - Epoch 400:
2025-07-20 14:19:48,583 - INFO -   Total Loss: 0.233896
2025-07-20 14:19:48,583 - INFO -   PEHE: 0.378386
2025-07-20 14:19:48,583 - INFO -   ATE Error: 0.007424
2025-07-20 14:19:48,583 - INFO -     - Main Losses (G): Factual=0.1314, Adversarial=1.0806, Decomp=0.0027
2025-07-20 14:19:48,583 - INFO -     - Discriminator Loss (D): 0.5416
2025-07-20 14:19:48,583 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:22:50,726 - INFO - Starting Stage 1 training...
2025-07-20 14:22:50,726 - INFO - Training samples: 9120
2025-07-20 14:22:50,726 - INFO - Test samples: 2280
2025-07-20 14:22:50,726 - INFO - Sample weights initialized: shape=(9120, 1)
2025-07-20 14:22:51,068 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-07-20 14:22:52,515 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-07-20 14:23:00,040 - INFO - Epoch 0:
2025-07-20 14:23:00,040 - INFO -   Total Loss: 1.993592
2025-07-20 14:23:00,040 - INFO -   PEHE: 0.329010
2025-07-20 14:23:00,040 - INFO -   ATE Error: 0.032390
2025-07-20 14:23:00,040 - INFO -     - Main Losses (G): Factual=0.5509, Adversarial=0.6725, Decomp=0.0608
2025-07-20 14:23:00,040 - INFO -     - Discriminator Loss (D): 0.7245
2025-07-20 14:23:00,048 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:23:00,051 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 14:23:00,156 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-07-20 14:23:00,156 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 14:23:00,207 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-07-20 14:23:37,161 - INFO - Epoch 50:
2025-07-20 14:23:37,161 - INFO -   Total Loss: 0.467620
2025-07-20 14:23:37,161 - INFO -   PEHE: 0.332255
2025-07-20 14:23:37,161 - INFO -   ATE Error: 0.014238
2025-07-20 14:23:37,161 - INFO -     - Main Losses (G): Factual=0.2553, Adversarial=0.7140, Decomp=0.0052
2025-07-20 14:23:37,161 - INFO -     - Discriminator Loss (D): 0.6789
2025-07-20 14:23:37,161 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:24:13,321 - INFO - Epoch 100:
2025-07-20 14:24:13,321 - INFO -   Total Loss: 0.340020
2025-07-20 14:24:13,321 - INFO -   PEHE: 0.342343
2025-07-20 14:24:13,321 - INFO -   ATE Error: 0.000572
2025-07-20 14:24:13,321 - INFO -     - Main Losses (G): Factual=0.2260, Adversarial=0.7329, Decomp=0.0034
2025-07-20 14:24:13,321 - INFO -     - Discriminator Loss (D): 0.6651
2025-07-20 14:24:13,321 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:30:26,981 - INFO - Starting Stage 1 training...
2025-07-20 14:30:26,981 - INFO - Training samples: 9120
2025-07-20 14:30:26,981 - INFO - Test samples: 2280
2025-07-20 14:30:26,981 - INFO - Sample weights initialized: shape=(9120, 1)
2025-07-20 14:30:27,377 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-07-20 14:30:28,929 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-07-20 14:30:36,542 - INFO - Epoch 0:
2025-07-20 14:30:36,542 - INFO -   Total Loss: 2.131310
2025-07-20 14:30:36,542 - INFO -   PEHE: 0.328153
2025-07-20 14:30:36,542 - INFO -   ATE Error: 0.029357
2025-07-20 14:30:36,542 - INFO -     - Main Losses (G): Factual=0.6492, Adversarial=0.7214, Decomp=0.0361, Diversity=1.9119
2025-07-20 14:30:36,542 - INFO -     - Discriminator Loss (D): 0.6746
2025-07-20 14:30:36,542 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:30:36,542 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 14:30:36,706 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-07-20 14:30:36,707 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 14:30:36,763 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-07-20 14:30:54,088 - INFO - Epoch 25:
2025-07-20 14:30:54,089 - INFO -   Total Loss: 0.847530
2025-07-20 14:30:54,089 - INFO -   PEHE: 0.337144
2025-07-20 14:30:54,089 - INFO -   ATE Error: 0.003854
2025-07-20 14:30:54,089 - INFO -     - Main Losses (G): Factual=0.2761, Adversarial=0.7124, Decomp=0.0119, Diversity=2.5307
2025-07-20 14:30:54,089 - INFO -     - Discriminator Loss (D): 0.6831
2025-07-20 14:30:54,089 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:31:10,614 - INFO - Epoch 50:
2025-07-20 14:31:10,614 - INFO -   Total Loss: 0.591751
2025-07-20 14:31:10,621 - INFO -   PEHE: 0.353552
2025-07-20 14:31:10,621 - INFO -   ATE Error: 0.032744
2025-07-20 14:31:10,622 - INFO -     - Main Losses (G): Factual=0.2571, Adversarial=0.7193, Decomp=0.0052, Diversity=2.2557
2025-07-20 14:31:10,622 - INFO -     - Discriminator Loss (D): 0.6765
2025-07-20 14:31:10,622 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:31:27,685 - INFO - Epoch 75:
2025-07-20 14:31:27,685 - INFO -   Total Loss: 0.500598
2025-07-20 14:31:27,685 - INFO -   PEHE: 0.356108
2025-07-20 14:31:27,685 - INFO -   ATE Error: 0.000625
2025-07-20 14:31:27,685 - INFO -     - Main Losses (G): Factual=0.2456, Adversarial=0.7271, Decomp=0.0036, Diversity=2.0563
2025-07-20 14:31:27,685 - INFO -     - Discriminator Loss (D): 0.6698
2025-07-20 14:31:27,685 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:31:44,480 - INFO - Epoch 100:
2025-07-20 14:31:44,480 - INFO -   Total Loss: 0.442846
2025-07-20 14:31:44,480 - INFO -   PEHE: 0.364233
2025-07-20 14:31:44,480 - INFO -   ATE Error: 0.012312
2025-07-20 14:31:44,480 - INFO -     - Main Losses (G): Factual=0.2229, Adversarial=0.7363, Decomp=0.0029, Diversity=1.9211
2025-07-20 14:31:44,480 - INFO -     - Discriminator Loss (D): 0.6629
2025-07-20 14:31:44,480 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:32:01,231 - INFO - Epoch 125:
2025-07-20 14:32:01,231 - INFO -   Total Loss: 0.410767
2025-07-20 14:32:01,231 - INFO -   PEHE: 0.395667
2025-07-20 14:32:01,231 - INFO -   ATE Error: 0.018788
2025-07-20 14:32:01,231 - INFO -     - Main Losses (G): Factual=0.2083, Adversarial=0.7449, Decomp=0.0026, Diversity=1.8113
2025-07-20 14:32:01,231 - INFO -     - Discriminator Loss (D): 0.6566
2025-07-20 14:32:01,231 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:32:17,021 - INFO - Stage 1 training completed!
2025-07-20 14:32:17,199 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-07-20 14:32:17,199 - INFO - Stage 2 training data shapes:
2025-07-20 14:32:17,199 - INFO -   x_train: (9120, 30)
2025-07-20 14:32:17,199 - INFO -   y_bar_target: (9120, 2)
2025-07-20 14:32:17,546 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\layers\normalization\layer_normalization.py:328: The name tf.nn.fused_batch_norm is deprecated. Please use tf.compat.v1.nn.fused_batch_norm instead.

2025-07-20 14:37:06,691 - INFO - Starting Stage 1 training...
2025-07-20 14:37:06,693 - INFO - Training samples: 9120
2025-07-20 14:37:06,693 - INFO - Test samples: 2280
2025-07-20 14:37:06,693 - INFO - Sample weights initialized: shape=(9120, 1)
2025-07-20 14:37:06,983 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-07-20 14:37:08,417 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-07-20 14:37:16,390 - INFO - Epoch 0:
2025-07-20 14:37:16,390 - INFO -   Total Loss: 2.060014
2025-07-20 14:37:16,390 - INFO -   PEHE: 0.326386
2025-07-20 14:37:16,390 - INFO -   ATE Error: 0.016313
2025-07-20 14:37:16,390 - INFO -     - Main Losses (G): Factual=0.6204, Adversarial=0.7085, Decomp=0.0599, Diversity=2.1396
2025-07-20 14:37:16,390 - INFO -     - Discriminator Loss (D): 0.6947
2025-07-20 14:37:16,390 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:37:16,390 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 14:37:16,485 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-07-20 14:37:16,486 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 14:37:16,511 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-07-20 14:37:32,805 - INFO - Epoch 25:
2025-07-20 14:37:32,805 - INFO -   Total Loss: 0.721876
2025-07-20 14:37:32,805 - INFO -   PEHE: 0.325553
2025-07-20 14:37:32,805 - INFO -   ATE Error: 0.003756
2025-07-20 14:37:32,805 - INFO -     - Main Losses (G): Factual=0.2736, Adversarial=0.7005, Decomp=0.0118, Diversity=3.2062
2025-07-20 14:37:32,821 - INFO -     - Discriminator Loss (D): 0.6922
2025-07-20 14:37:32,821 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:37:32,821 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 14:37:32,884 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-07-20 14:37:49,717 - INFO - Epoch 50:
2025-07-20 14:37:49,717 - INFO -   Total Loss: 0.478479
2025-07-20 14:37:49,717 - INFO -   PEHE: 0.330871
2025-07-20 14:37:49,717 - INFO -   ATE Error: 0.018560
2025-07-20 14:37:49,717 - INFO -     - Main Losses (G): Factual=0.2590, Adversarial=0.7046, Decomp=0.0054, Diversity=2.9724
2025-07-20 14:37:49,717 - INFO -     - Discriminator Loss (D): 0.6869
2025-07-20 14:37:49,717 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:38:06,220 - INFO - Epoch 75:
2025-07-20 14:38:06,220 - INFO -   Total Loss: 0.388406
2025-07-20 14:38:06,220 - INFO -   PEHE: 0.334082
2025-07-20 14:38:06,220 - INFO -   ATE Error: 0.007217
2025-07-20 14:38:06,220 - INFO -     - Main Losses (G): Factual=0.2403, Adversarial=0.7100, Decomp=0.0039, Diversity=2.6354
2025-07-20 14:38:06,220 - INFO -     - Discriminator Loss (D): 0.6818
2025-07-20 14:38:06,220 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:38:21,995 - INFO - Stage 1 training completed!
2025-07-20 14:38:22,210 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-07-20 14:38:22,210 - INFO - Stage 2 training data shapes:
2025-07-20 14:38:22,210 - INFO -   x_train: (9120, 30)
2025-07-20 14:38:22,211 - INFO -   y_bar_target: (9120, 2)
2025-07-20 14:38:22,639 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\layers\normalization\layer_normalization.py:328: The name tf.nn.fused_batch_norm is deprecated. Please use tf.compat.v1.nn.fused_batch_norm instead.

2025-07-20 14:42:04,976 - INFO - Starting Stage 1 training...
2025-07-20 14:42:04,976 - INFO - Training samples: 9120
2025-07-20 14:42:04,991 - INFO - Test samples: 2280
2025-07-20 14:42:04,991 - INFO - Sample weights initialized: shape=(9120, 1)
2025-07-20 14:42:05,343 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-07-20 14:42:06,891 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-07-20 14:42:14,539 - INFO - Epoch 0:
2025-07-20 14:42:14,539 - INFO -   Total Loss: 2.034313
2025-07-20 14:42:14,555 - INFO -   PEHE: 0.361844
2025-07-20 14:42:14,555 - INFO -   ATE Error: 0.151569
2025-07-20 14:42:14,555 - INFO -     - Main Losses (G): Factual=0.5958, Adversarial=0.7185, Decomp=0.0576, Diversity=0.0000
2025-07-20 14:42:14,555 - INFO -     - Discriminator Loss (D): 0.6754
2025-07-20 14:42:14,555 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:42:14,555 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 14:42:14,640 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-07-20 14:42:14,640 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 14:42:14,681 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-07-20 14:42:55,576 - INFO - Epoch 50:
2025-07-20 14:42:55,576 - INFO -   Total Loss: 0.481807
2025-07-20 14:42:55,576 - INFO -   PEHE: 0.331651
2025-07-20 14:42:55,576 - INFO -   ATE Error: 0.017571
2025-07-20 14:42:55,576 - INFO -     - Main Losses (G): Factual=0.2609, Adversarial=0.7416, Decomp=0.0053, Diversity=0.0000
2025-07-20 14:42:55,576 - INFO -     - Discriminator Loss (D): 0.6611
2025-07-20 14:42:55,576 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:42:55,576 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 14:42:55,618 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-07-20 14:43:36,223 - INFO - Epoch 100:
2025-07-20 14:43:36,224 - INFO -   Total Loss: 0.342806
2025-07-20 14:43:36,224 - INFO -   PEHE: 0.338051
2025-07-20 14:43:36,224 - INFO -   ATE Error: 0.001769
2025-07-20 14:43:36,224 - INFO -     - Main Losses (G): Factual=0.2229, Adversarial=0.7859, Decomp=0.0032, Diversity=0.0000
2025-07-20 14:43:36,225 - INFO -     - Discriminator Loss (D): 0.6389
2025-07-20 14:43:36,225 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:44:12,467 - INFO - Epoch 150:
2025-07-20 14:44:12,467 - INFO -   Total Loss: 0.299885
2025-07-20 14:44:12,467 - INFO -   PEHE: 0.348813
2025-07-20 14:44:12,468 - INFO -   ATE Error: 0.005198
2025-07-20 14:44:12,468 - INFO -     - Main Losses (G): Factual=0.1986, Adversarial=0.8175, Decomp=0.0030, Diversity=0.0000
2025-07-20 14:44:12,468 - INFO -     - Discriminator Loss (D): 0.6279
2025-07-20 14:44:12,468 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:44:49,498 - INFO - Epoch 200:
2025-07-20 14:44:49,499 - INFO -   Total Loss: 0.270145
2025-07-20 14:44:49,499 - INFO -   PEHE: 0.361427
2025-07-20 14:44:49,499 - INFO -   ATE Error: 0.000345
2025-07-20 14:44:49,499 - INFO -     - Main Losses (G): Factual=0.1731, Adversarial=0.8510, Decomp=0.0029, Diversity=0.0000
2025-07-20 14:44:49,499 - INFO -     - Discriminator Loss (D): 0.6133
2025-07-20 14:44:49,500 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:45:24,264 - INFO - Epoch 250:
2025-07-20 14:45:24,264 - INFO -   Total Loss: 0.254435
2025-07-20 14:45:24,264 - INFO -   PEHE: 0.367178
2025-07-20 14:45:24,264 - INFO -   ATE Error: 0.000171
2025-07-20 14:45:24,264 - INFO -     - Main Losses (G): Factual=0.1565, Adversarial=0.8881, Decomp=0.0028, Diversity=0.0000
2025-07-20 14:45:24,264 - INFO -     - Discriminator Loss (D): 0.6001
2025-07-20 14:45:24,264 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:45:59,000 - INFO - Epoch 300:
2025-07-20 14:45:59,001 - INFO -   Total Loss: 0.250732
2025-07-20 14:45:59,001 - INFO -   PEHE: 0.377850
2025-07-20 14:45:59,001 - INFO -   ATE Error: 0.023710
2025-07-20 14:45:59,003 - INFO -     - Main Losses (G): Factual=0.1508, Adversarial=0.9448, Decomp=0.0028, Diversity=0.0000
2025-07-20 14:45:59,004 - INFO -     - Discriminator Loss (D): 0.5814
2025-07-20 14:45:59,004 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:46:33,991 - INFO - Epoch 350:
2025-07-20 14:46:33,991 - INFO -   Total Loss: 0.250457
2025-07-20 14:46:33,991 - INFO -   PEHE: 0.377711
2025-07-20 14:46:33,991 - INFO -   ATE Error: 0.029789
2025-07-20 14:46:33,991 - INFO -     - Main Losses (G): Factual=0.1468, Adversarial=1.0236, Decomp=0.0027, Diversity=0.0000
2025-07-20 14:46:33,991 - INFO -     - Discriminator Loss (D): 0.5598
2025-07-20 14:46:33,991 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:47:07,388 - INFO - Epoch 400:
2025-07-20 14:47:07,388 - INFO -   Total Loss: 0.245422
2025-07-20 14:47:07,388 - INFO -   PEHE: 0.393285
2025-07-20 14:47:07,388 - INFO -   ATE Error: 0.019708
2025-07-20 14:47:07,403 - INFO -     - Main Losses (G): Factual=0.1378, Adversarial=1.0649, Decomp=0.0027, Diversity=0.0000
2025-07-20 14:47:07,403 - INFO -     - Discriminator Loss (D): 0.5560
2025-07-20 14:47:07,403 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:53:04,379 - INFO - Starting Stage 1 training...
2025-07-20 14:53:04,379 - INFO - Training samples: 9120
2025-07-20 14:53:04,379 - INFO - Test samples: 2280
2025-07-20 14:53:04,379 - INFO - Sample weights initialized: shape=(9120, 1)
2025-07-20 14:53:04,759 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-07-20 14:53:06,277 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-07-20 14:53:13,990 - INFO - Epoch 0:
2025-07-20 14:53:13,990 - INFO -   Total Loss: 1.987815
2025-07-20 14:53:13,990 - INFO -   PEHE: 0.324580
2025-07-20 14:53:13,990 - INFO -   ATE Error: 0.025794
2025-07-20 14:53:13,990 - INFO -     - Main Losses (G): Factual=0.5470, Adversarial=0.6851, Decomp=0.0621, Diversity=0.0000
2025-07-20 14:53:13,990 - INFO -     - Discriminator Loss (D): 0.7159
2025-07-20 14:53:13,990 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:53:13,990 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 14:53:14,075 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-07-20 14:53:14,075 - WARNING - Failed to save model: ./results\models is not a directory
2025-07-20 14:53:14,115 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-07-20 14:53:49,062 - INFO - Epoch 50:
2025-07-20 14:53:49,062 - INFO -   Total Loss: 0.473663
2025-07-20 14:53:49,062 - INFO -   PEHE: 0.331292
2025-07-20 14:53:49,062 - INFO -   ATE Error: 0.014152
2025-07-20 14:53:49,062 - INFO -     - Main Losses (G): Factual=0.2568, Adversarial=0.7209, Decomp=0.0054, Diversity=0.0000
2025-07-20 14:53:49,062 - INFO -     - Discriminator Loss (D): 0.6745
2025-07-20 14:53:49,062 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:54:23,647 - INFO - Epoch 100:
2025-07-20 14:54:23,647 - INFO -   Total Loss: 0.341261
2025-07-20 14:54:23,647 - INFO -   PEHE: 0.342560
2025-07-20 14:54:23,647 - INFO -   ATE Error: 0.019253
2025-07-20 14:54:23,647 - INFO -     - Main Losses (G): Factual=0.2237, Adversarial=0.7445, Decomp=0.0033, Diversity=0.0000
2025-07-20 14:54:23,647 - INFO -     - Discriminator Loss (D): 0.6589
2025-07-20 14:54:23,647 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:54:57,800 - INFO - Epoch 150:
2025-07-20 14:54:57,800 - INFO -   Total Loss: 0.295507
2025-07-20 14:54:57,800 - INFO -   PEHE: 0.350981
2025-07-20 14:54:57,800 - INFO -   ATE Error: 0.013645
2025-07-20 14:54:57,800 - INFO -     - Main Losses (G): Factual=0.1983, Adversarial=0.7689, Decomp=0.0030, Diversity=0.0000
2025-07-20 14:54:57,816 - INFO -     - Discriminator Loss (D): 0.6459
2025-07-20 14:54:57,816 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:55:31,535 - INFO - Epoch 200:
2025-07-20 14:55:31,535 - INFO -   Total Loss: 0.266174
2025-07-20 14:55:31,535 - INFO -   PEHE: 0.359188
2025-07-20 14:55:31,535 - INFO -   ATE Error: 0.011153
2025-07-20 14:55:31,535 - INFO -     - Main Losses (G): Factual=0.1744, Adversarial=0.7972, Decomp=0.0029, Diversity=0.0000
2025-07-20 14:55:31,535 - INFO -     - Discriminator Loss (D): 0.6327
2025-07-20 14:55:31,535 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:56:04,832 - INFO - Epoch 250:
2025-07-20 14:56:04,832 - INFO -   Total Loss: 0.252118
2025-07-20 14:56:04,848 - INFO -   PEHE: 0.366297
2025-07-20 14:56:04,848 - INFO -   ATE Error: 0.013533
2025-07-20 14:56:04,848 - INFO -     - Main Losses (G): Factual=0.1617, Adversarial=0.8172, Decomp=0.0028, Diversity=0.0000
2025-07-20 14:56:04,848 - INFO -     - Discriminator Loss (D): 0.6255
2025-07-20 14:56:04,848 - INFO -     - Balance Loss (Weights): 0.0000
2025-07-20 14:56:39,247 - INFO - Stage 1 training completed!
2025-07-20 14:56:39,461 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-07-20 14:56:39,461 - INFO - Stage 2 training data shapes:
2025-07-20 14:56:39,461 - INFO -   x_train: (9120, 30)
2025-07-20 14:56:39,461 - INFO -   y_bar_target: (9120, 2)
2025-07-20 14:56:39,802 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\layers\normalization\layer_normalization.py:328: The name tf.nn.fused_batch_norm is deprecated. Please use tf.compat.v1.nn.fused_batch_norm instead.

2025-07-20 14:56:48,096 - INFO - Stage 2 - Iter: 0/100, ITE_C Loss: 7.0356 (Wass: 0.0333, GP: 0.7002), ITE_G Loss: 26.2669 (Adv: -0.4294, Sup: 0.3559)
2025-07-20 14:57:02,434 - INFO - Stage 2 - Iter: 99/100, ITE_C Loss: 6.9026 (Wass: -0.0100, GP: 0.6913), ITE_G Loss: 6.2581 (Adv: -0.2060, Sup: 0.0862)
2025-07-20 14:57:02,435 - INFO - Stage 2 training completed!
2025-07-20 14:57:02,458 - INFO - Stage2 model save skipped for quick test
2025-08-02 14:39:34,187 - INFO - Starting Stage 1 training...
2025-08-02 14:39:34,187 - INFO - Training samples: 9120
2025-08-02 14:39:34,188 - INFO - Test samples: 2280
2025-08-02 14:39:34,188 - INFO - Sample weights initialized: shape=(9120, 1)
2025-08-02 14:39:34,604 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-08-02 14:39:36,447 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-08-02 14:39:45,893 - INFO - Epoch 0:
2025-08-02 14:39:45,893 - INFO -   Total Loss: 6176.609375
2025-08-02 14:39:45,893 - INFO -   PEHE: 0.331023
2025-08-02 14:39:45,893 - INFO -   ATE Error: 0.053287
2025-08-02 14:39:45,897 - INFO -     - Main Losses (G): Factual=0.7525, Adversarial=0.7657, Decomp=21.8002
2025-08-02 14:39:45,897 - INFO -     - Discriminator Loss (D): 30.6440
2025-08-02 14:39:45,897 - INFO -     - Balance Loss (Weights): 3.0175
2025-08-02 14:39:45,902 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-02 14:39:46,001 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-02 14:39:46,002 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-02 14:39:46,035 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-08-02 14:40:07,973 - INFO - Epoch 25:
2025-08-02 14:40:07,973 - INFO -   Total Loss: 302.938171
2025-08-02 14:40:07,973 - INFO -   PEHE: 0.319494
2025-08-02 14:40:07,973 - INFO -   ATE Error: 0.001868
2025-08-02 14:40:07,973 - INFO -     - Main Losses (G): Factual=0.6802, Adversarial=0.7257, Decomp=15.0032
2025-08-02 14:40:07,973 - INFO -     - Discriminator Loss (D): 20.5180
2025-08-02 14:40:07,976 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-02 14:40:07,976 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-02 14:40:08,019 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-02 14:40:28,118 - INFO - Stage 1 training completed!
2025-08-02 14:40:28,329 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-08-02 14:40:28,330 - INFO - Stage 2 training data shapes:
2025-08-02 14:40:28,330 - INFO -   x_train: (9120, 30)
2025-08-02 14:40:28,331 - INFO -   y_bar_target: (9120, 2)
2025-08-02 14:40:28,718 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\layers\normalization\layer_normalization.py:328: The name tf.nn.fused_batch_norm is deprecated. Please use tf.compat.v1.nn.fused_batch_norm instead.

2025-08-02 14:40:37,355 - INFO - Stage 2 - Iter: 0/25, ITE_C Loss: 4.5116 (Wass: 0.0025, GP: 0.4509), ITE_G Loss: 2.2533 (Adv: 0.7155, Sup: 0.0205)
2025-08-02 14:40:40,657 - INFO - Stage 2 - Iter: 24/25, ITE_C Loss: 4.0620 (Wass: -0.0002, GP: 0.4062), ITE_G Loss: 0.7264 (Adv: 0.6065, Sup: 0.0016)
2025-08-02 14:40:40,661 - INFO - Stage 2 training completed!
2025-08-02 14:40:40,679 - INFO - Stage2 model save skipped for quick test
2025-08-04 13:55:17,948 - INFO - Starting Stage 1 training...
2025-08-04 13:55:17,949 - INFO - Training samples: 9120
2025-08-04 13:55:17,949 - INFO - Test samples: 2280
2025-08-04 13:55:17,949 - INFO - Sample weights initialized: shape=(9120, 1)
2025-08-04 13:55:18,324 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-08-04 13:55:20,064 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-08-04 13:55:28,600 - INFO - Epoch 0:
2025-08-04 13:55:28,600 - INFO -   Total Loss: 6147.980957
2025-08-04 13:55:28,600 - INFO -   PEHE: 0.323419
2025-08-04 13:55:28,600 - INFO -   ATE Error: 0.022869
2025-08-04 13:55:28,600 - INFO -     - Main Losses (G): Factual=0.7333, Adversarial=0.6920, Decomp=21.3474
2025-08-04 13:55:28,602 - INFO -     - Discriminator Loss (D): 24.5563
2025-08-04 13:55:28,602 - INFO -     - Balance Loss (Weights): 2.9089
2025-08-04 13:55:28,606 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-04 13:55:28,700 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-04 13:55:28,704 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-04 13:55:28,734 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-08-04 13:55:57,788 - INFO - Epoch 25:
2025-08-04 13:55:57,788 - INFO -   Total Loss: 298.496765
2025-08-04 13:55:57,789 - INFO -   PEHE: 0.319748
2025-08-04 13:55:57,789 - INFO -   ATE Error: 0.012898
2025-08-04 13:55:57,790 - INFO -     - Main Losses (G): Factual=0.6829, Adversarial=0.6985, Decomp=15.0032
2025-08-04 13:55:57,790 - INFO -     - Discriminator Loss (D): 16.1985
2025-08-04 13:55:57,791 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-04 13:55:57,792 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-04 13:55:57,937 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-04 13:56:45,961 - INFO - Epoch 50:
2025-08-04 13:56:45,963 - INFO -   Total Loss: 28.043278
2025-08-04 13:56:45,963 - INFO -   PEHE: 0.319737
2025-08-04 13:56:45,963 - INFO -   ATE Error: 0.012616
2025-08-04 13:56:45,964 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6988, Decomp=15.0032
2025-08-04 13:56:45,964 - INFO -     - Discriminator Loss (D): 10.2455
2025-08-04 13:56:45,965 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-04 13:56:45,970 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-04 13:56:46,133 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-04 13:57:25,560 - INFO - Epoch 75:
2025-08-04 13:57:25,560 - INFO -   Total Loss: 24.026321
2025-08-04 13:57:25,560 - INFO -   PEHE: 0.319712
2025-08-04 13:57:25,560 - INFO -   ATE Error: 0.011963
2025-08-04 13:57:25,560 - INFO -     - Main Losses (G): Factual=0.6824, Adversarial=0.6976, Decomp=15.0032
2025-08-04 13:57:25,560 - INFO -     - Discriminator Loss (D): 6.2334
2025-08-04 13:57:25,560 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-04 13:57:25,567 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-04 13:57:25,617 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-04 13:57:47,687 - INFO - Stage 1 training completed!
2025-08-04 13:57:47,891 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-08-04 13:57:47,892 - INFO - Stage 2 training data shapes:
2025-08-04 13:57:47,892 - INFO -   x_train: (9120, 30)
2025-08-04 13:57:47,892 - INFO -   y_bar_target: (9120, 2)
2025-08-04 13:57:48,287 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\layers\normalization\layer_normalization.py:328: The name tf.nn.fused_batch_norm is deprecated. Please use tf.compat.v1.nn.fused_batch_norm instead.

2025-08-04 13:57:56,760 - INFO - Stage 2 - Iter: 0/100, ITE_C Loss: 5.2393 (Wass: 0.0008, GP: 0.5238), ITE_G Loss: 0.5046 (Adv: -0.9557, Sup: 0.0195)
2025-08-04 13:58:11,602 - INFO - Stage 2 - Iter: 99/100, ITE_C Loss: 5.9159 (Wass: -0.0000, GP: 0.5916), ITE_G Loss: -0.6915 (Adv: -0.7080, Sup: 0.0002)
2025-08-04 13:58:11,602 - INFO - Stage 2 training completed!
2025-08-04 13:58:11,624 - INFO - Stage2 model save skipped for quick test
2025-08-04 14:18:44,026 - INFO - Starting Stage 1 training...
2025-08-04 14:18:44,026 - INFO - Training samples: 9120
2025-08-04 14:18:44,026 - INFO - Test samples: 2280
2025-08-04 14:18:44,026 - INFO - Sample weights initialized: shape=(9120, 1)
2025-08-04 14:18:44,372 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-08-04 14:18:45,789 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-08-04 14:18:54,256 - INFO - Epoch 0:
2025-08-04 14:18:54,256 - INFO -   Total Loss: 6163.968262
2025-08-04 14:18:54,257 - INFO -   PEHE: 0.328730
2025-08-04 14:18:54,257 - INFO -   ATE Error: 0.046245
2025-08-04 14:18:54,257 - INFO -     - Main Losses (G): Factual=0.7289, Adversarial=0.6889, Decomp=20.5799
2025-08-04 14:18:54,257 - INFO -     - Discriminator Loss (D): 27.2116
2025-08-04 14:18:54,257 - INFO -     - Balance Loss (Weights): 2.9724
2025-08-04 14:18:54,258 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-04 14:18:54,344 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-04 14:18:54,344 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-04 14:18:54,377 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-08-04 14:19:16,487 - INFO - Epoch 25:
2025-08-04 14:19:16,487 - INFO -   Total Loss: 300.242126
2025-08-04 14:19:16,487 - INFO -   PEHE: 0.319673
2025-08-04 14:19:16,487 - INFO -   ATE Error: 0.010875
2025-08-04 14:19:16,487 - INFO -     - Main Losses (G): Factual=0.6832, Adversarial=0.6923, Decomp=15.0032
2025-08-04 14:19:16,487 - INFO -     - Discriminator Loss (D): 17.9741
2025-08-04 14:19:16,487 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-04 14:19:16,487 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-04 14:19:16,527 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-04 14:19:57,522 - INFO - Epoch 50:
2025-08-04 14:19:57,523 - INFO -   Total Loss: 29.149799
2025-08-04 14:19:57,523 - INFO -   PEHE: 0.319669
2025-08-04 14:19:57,524 - INFO -   ATE Error: 0.010756
2025-08-04 14:19:57,525 - INFO -     - Main Losses (G): Factual=0.6830, Adversarial=0.6926, Decomp=15.0032
2025-08-04 14:19:57,525 - INFO -     - Discriminator Loss (D): 11.3828
2025-08-04 14:19:57,525 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-04 14:19:57,528 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-04 14:19:57,677 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-04 14:21:05,170 - INFO - Epoch 75:
2025-08-04 14:21:05,171 - INFO -   Total Loss: 24.663715
2025-08-04 14:21:05,171 - INFO -   PEHE: 0.319669
2025-08-04 14:21:05,172 - INFO -   ATE Error: 0.010734
2025-08-04 14:21:05,173 - INFO -     - Main Losses (G): Factual=0.6829, Adversarial=0.6927, Decomp=15.0032
2025-08-04 14:21:05,173 - INFO -     - Discriminator Loss (D): 6.8952
2025-08-04 14:21:05,174 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-04 14:21:05,175 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-04 14:21:05,264 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-04 14:22:08,893 - INFO - Stage 1 training completed!
2025-08-04 14:22:09,738 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-08-04 14:22:09,740 - INFO - Stage 2 training data shapes:
2025-08-04 14:22:09,741 - INFO -   x_train: (9120, 30)
2025-08-04 14:22:09,741 - INFO -   y_bar_target: (9120, 2)
2025-08-04 14:22:10,656 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\layers\normalization\layer_normalization.py:328: The name tf.nn.fused_batch_norm is deprecated. Please use tf.compat.v1.nn.fused_batch_norm instead.

2025-08-04 14:22:36,756 - INFO - Stage 2 - Iter: 0/100, ITE_C Loss: 3.8260 (Wass: -0.0006, GP: 0.3827), ITE_G Loss: 1.3109 (Adv: -0.1731, Sup: 0.0198)
2025-08-04 14:23:14,655 - INFO - Stage 2 - Iter: 99/100, ITE_C Loss: 4.3801 (Wass: -0.0000, GP: 0.4380), ITE_G Loss: -0.4735 (Adv: -0.5004, Sup: 0.0004)
2025-08-04 14:23:14,656 - INFO - Stage 2 training completed!
2025-08-04 14:23:14,689 - INFO - Stage2 model save skipped for quick test
2025-08-04 14:51:17,561 - INFO - Starting Stage 1 training...
2025-08-04 14:51:17,561 - INFO - Training samples: 9120
2025-08-04 14:51:17,562 - INFO - Test samples: 2280
2025-08-04 14:51:17,562 - INFO - Sample weights initialized: shape=(9120, 1)
2025-08-04 14:51:17,943 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-08-04 14:51:19,545 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-08-04 14:51:28,658 - INFO - Epoch 0:
2025-08-04 14:51:28,658 - INFO -   Total Loss: 6170.787598
2025-08-04 14:51:28,659 - INFO -   PEHE: 0.340051
2025-08-04 14:51:28,659 - INFO -   ATE Error: 0.102193
2025-08-04 14:51:28,659 - INFO -     - Main Losses (G): Factual=0.7582, Adversarial=0.6824, Decomp=21.1868
2025-08-04 14:51:28,659 - INFO -     - Discriminator Loss (D): 27.0408
2025-08-04 14:51:28,659 - INFO -     - Balance Loss (Weights): 2.9682
2025-08-04 14:51:28,659 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-04 14:51:28,737 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-04 14:51:28,737 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-04 14:51:28,772 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-08-04 14:51:33,220 - INFO - Epoch 5:
2025-08-04 14:51:33,220 - INFO -   Total Loss: 2485.067627
2025-08-04 14:51:33,220 - INFO -   PEHE: 0.319860
2025-08-04 14:51:33,220 - INFO -   ATE Error: 0.014500
2025-08-04 14:51:33,220 - INFO -     - Main Losses (G): Factual=0.5800, Adversarial=0.6882, Decomp=14.9392
2025-08-04 14:51:33,220 - INFO -     - Discriminator Loss (D): 24.9850
2025-08-04 14:51:33,220 - INFO -     - Balance Loss (Weights): 0.7535
2025-08-04 14:51:33,220 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-04 14:51:33,262 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-04 14:51:36,790 - INFO - Stage 1 training completed!
2025-08-04 14:51:37,060 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-08-04 14:51:37,064 - INFO - Stage 2 training data shapes:
2025-08-04 14:51:37,064 - INFO -   x_train: (9120, 30)
2025-08-04 14:51:37,064 - INFO -   y_bar_target: (9120, 2)
2025-08-04 14:51:37,365 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\layers\normalization\layer_normalization.py:328: The name tf.nn.fused_batch_norm is deprecated. Please use tf.compat.v1.nn.fused_batch_norm instead.

2025-08-04 14:51:46,343 - INFO - Stage 2 - Iter: 0/10, ITE_C Loss: 6.3699 (Wass: 0.0005, GP: 0.6369), ITE_G Loss: 0.8933 (Adv: -0.6852, Sup: 0.0210)
2025-08-04 14:51:47,587 - INFO - Stage 2 - Iter: 9/10, ITE_C Loss: 6.6892 (Wass: -0.0000, GP: 0.6689), ITE_G Loss: -0.2924 (Adv: -0.6135, Sup: 0.0043)
2025-08-04 14:51:47,587 - INFO - Stage 2 training completed!
2025-08-04 14:51:47,605 - INFO - Stage2 model save skipped for quick test
2025-08-04 14:57:09,578 - INFO - Starting Stage 1 training...
2025-08-04 14:57:09,578 - INFO - Training samples: 9120
2025-08-04 14:57:09,578 - INFO - Test samples: 2280
2025-08-04 14:57:09,579 - INFO - Sample weights initialized: shape=(9120, 1)
2025-08-04 14:57:09,873 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-08-04 14:57:11,235 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-08-04 14:57:19,588 - INFO - Epoch 0:
2025-08-04 14:57:19,588 - INFO -   Total Loss: 6179.405762
2025-08-04 14:57:19,588 - INFO -   PEHE: 0.324692
2025-08-04 14:57:19,588 - INFO -   ATE Error: 0.002449
2025-08-04 14:57:19,588 - INFO -     - Main Losses (G): Factual=0.8639, Adversarial=0.7162, Decomp=21.7980
2025-08-04 14:57:19,588 - INFO -     - Discriminator Loss (D): 28.6991
2025-08-04 14:57:19,592 - INFO -     - Balance Loss (Weights): 2.9672
2025-08-04 14:57:19,592 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-04 14:57:19,671 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-04 14:57:19,675 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-04 14:57:19,711 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-08-04 14:57:43,380 - INFO - Epoch 25:
2025-08-04 14:57:43,380 - INFO -   Total Loss: 301.553558
2025-08-04 14:57:43,380 - INFO -   PEHE: 0.319644
2025-08-04 14:57:43,380 - INFO -   ATE Error: 0.009972
2025-08-04 14:57:43,380 - INFO -     - Main Losses (G): Factual=0.6823, Adversarial=0.6995, Decomp=15.0032
2025-08-04 14:57:43,383 - INFO -     - Discriminator Loss (D): 19.2497
2025-08-04 14:57:43,383 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-04 14:57:43,383 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-04 14:57:43,440 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-04 14:58:04,589 - INFO - Epoch 50:
2025-08-04 14:58:04,593 - INFO -   Total Loss: 30.093863
2025-08-04 14:58:04,593 - INFO -   PEHE: 0.319667
2025-08-04 14:58:04,593 - INFO -   ATE Error: 0.010677
2025-08-04 14:58:04,593 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6949, Decomp=15.0032
2025-08-04 14:58:04,593 - INFO -     - Discriminator Loss (D): 12.3155
2025-08-04 14:58:04,593 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-04 14:58:28,137 - INFO - Epoch 75:
2025-08-04 14:58:28,137 - INFO -   Total Loss: 25.223049
2025-08-04 14:58:28,137 - INFO -   PEHE: 0.319675
2025-08-04 14:58:28,139 - INFO -   ATE Error: 0.010924
2025-08-04 14:58:28,139 - INFO -     - Main Losses (G): Factual=0.6827, Adversarial=0.6932, Decomp=15.0032
2025-08-04 14:58:28,139 - INFO -     - Discriminator Loss (D): 7.4524
2025-08-04 14:58:28,139 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-04 14:58:50,696 - INFO - Stage 1 training completed!
2025-08-04 14:58:50,879 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-08-04 14:58:50,879 - INFO - Stage 2 training data shapes:
2025-08-04 14:58:50,879 - INFO -   x_train: (9120, 30)
2025-08-04 14:58:50,879 - INFO -   y_bar_target: (9120, 2)
2025-08-04 14:58:51,174 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\layers\normalization\layer_normalization.py:328: The name tf.nn.fused_batch_norm is deprecated. Please use tf.compat.v1.nn.fused_batch_norm instead.

2025-08-04 14:59:00,652 - INFO - Stage 2 - Iter: 0/100, ITE_C Loss: 5.0488 (Wass: 0.0010, GP: 0.5048), ITE_G Loss: 1.1447 (Adv: -0.1293, Sup: 0.0170)
2025-08-04 14:59:15,995 - INFO - Stage 2 - Iter: 99/100, ITE_C Loss: 4.1824 (Wass: -0.0000, GP: 0.4182), ITE_G Loss: 0.2424 (Adv: 0.2075, Sup: 0.0005)
2025-08-04 14:59:15,996 - INFO - Stage 2 training completed!
2025-08-04 14:59:16,014 - INFO - Stage2 model save skipped for quick test
2025-08-04 15:38:49,345 - INFO - Starting Stage 1 training...
2025-08-04 15:38:49,347 - INFO - Training samples: 9120
2025-08-04 15:38:49,348 - INFO - Test samples: 2280
2025-08-04 15:38:49,349 - INFO - Sample weights initialized: shape=(9120, 1)
2025-08-04 15:38:50,616 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-08-04 15:38:55,755 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-08-04 15:39:25,410 - INFO - Epoch 0:
2025-08-04 15:39:25,411 - INFO -   Total Loss: 6167.207031
2025-08-04 15:39:25,412 - INFO -   PEHE: 0.328387
2025-08-04 15:39:25,413 - INFO -   ATE Error: 0.023199
2025-08-04 15:39:25,414 - INFO -     - Main Losses (G): Factual=0.7371, Adversarial=0.7057, Decomp=21.1310
2025-08-04 15:39:25,416 - INFO -     - Discriminator Loss (D): 28.3568
2025-08-04 15:39:25,418 - INFO -     - Balance Loss (Weights): 3.0211
2025-08-04 15:39:25,439 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-04 15:39:25,724 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-04 15:39:25,727 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-04 15:39:25,828 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-08-04 15:39:37,247 - INFO - Stage 1 training completed!
2025-08-04 15:39:37,992 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-08-04 15:39:37,993 - INFO - Stage 2 training data shapes:
2025-08-04 15:39:37,995 - INFO -   x_train: (9120, 30)
2025-08-04 15:39:37,995 - INFO -   y_bar_target: (9120, 2)
2025-08-04 15:39:38,978 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\layers\normalization\layer_normalization.py:328: The name tf.nn.fused_batch_norm is deprecated. Please use tf.compat.v1.nn.fused_batch_norm instead.

2025-08-04 15:40:06,353 - INFO - Stage 2 - Iter: 0/5, ITE_C Loss: 6.5420 (Wass: -0.0012, GP: 0.6543), ITE_G Loss: 1.7169 (Adv: -0.1986, Sup: 0.0255)
2025-08-04 15:40:07,666 - INFO - Stage 2 - Iter: 4/5, ITE_C Loss: 6.7680 (Wass: -0.0003, GP: 0.6768), ITE_G Loss: 0.6304 (Adv: -0.2133, Sup: 0.0112)
2025-08-04 15:40:07,666 - INFO - Stage 2 training completed!
2025-08-04 15:40:07,701 - INFO - Stage2 model save skipped for quick test
2025-08-05 16:26:38,856 - INFO - Starting Stage 1 training...
2025-08-05 16:26:38,860 - INFO - Training samples: 9120
2025-08-05 16:26:38,860 - INFO - Test samples: 2280
2025-08-05 16:26:38,860 - INFO - Sample weights initialized: shape=(9120, 1)
2025-08-05 16:26:39,272 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-08-05 16:26:41,246 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-08-05 16:26:52,285 - INFO - Epoch 0:
2025-08-05 16:26:52,286 - INFO -   Total Loss: 6172.451660
2025-08-05 16:26:52,286 - INFO -   PEHE: 0.326521
2025-08-05 16:26:52,287 - INFO -   ATE Error: 0.037707
2025-08-05 16:26:52,287 - INFO -     - Main Losses (G): Factual=0.8562, Adversarial=0.7382, Decomp=18.6318
2025-08-05 16:26:52,288 - INFO -     - Discriminator Loss (D): 32.9017
2025-08-05 16:26:52,288 - INFO -     - Balance Loss (Weights): 2.9561
2025-08-05 16:26:52,295 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-05 16:26:52,401 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-05 16:26:52,402 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-05 16:26:52,455 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-08-05 16:27:41,201 - INFO - Epoch 50:
2025-08-05 16:27:41,205 - INFO -   Total Loss: 32.025135
2025-08-05 16:27:41,205 - INFO -   PEHE: 0.319642
2025-08-05 16:27:41,205 - INFO -   ATE Error: 0.009899
2025-08-05 16:27:41,205 - INFO -     - Main Losses (G): Factual=0.6815, Adversarial=0.7078, Decomp=15.0032
2025-08-05 16:27:41,205 - INFO -     - Discriminator Loss (D): 14.1836
2025-08-05 16:27:41,205 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-05 16:27:41,209 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-05 16:27:41,278 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-05 16:28:29,459 - INFO - Epoch 100:
2025-08-05 16:28:29,459 - INFO -   Total Loss: 22.473021
2025-08-05 16:28:29,459 - INFO -   PEHE: 0.319654
2025-08-05 16:28:29,459 - INFO -   ATE Error: 0.010293
2025-08-05 16:28:29,459 - INFO -     - Main Losses (G): Factual=0.6827, Adversarial=0.6955, Decomp=15.0032
2025-08-05 16:28:29,459 - INFO -     - Discriminator Loss (D): 4.6903
2025-08-05 16:28:29,459 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-05 16:29:17,774 - INFO - Epoch 150:
2025-08-05 16:29:17,774 - INFO -   Total Loss: 19.106653
2025-08-05 16:29:17,774 - INFO -   PEHE: 0.319670
2025-08-05 16:29:17,775 - INFO -   ATE Error: 0.010777
2025-08-05 16:29:17,775 - INFO -     - Main Losses (G): Factual=0.6827, Adversarial=0.6932, Decomp=15.0032
2025-08-05 16:29:17,775 - INFO -     - Discriminator Loss (D): 1.3342
2025-08-05 16:29:17,775 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-05 16:30:04,648 - INFO - Epoch 200:
2025-08-05 16:30:04,648 - INFO -   Total Loss: 18.569435
2025-08-05 16:30:04,648 - INFO -   PEHE: 0.319662
2025-08-05 16:30:04,648 - INFO -   ATE Error: 0.010552
2025-08-05 16:30:04,648 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=15.0032
2025-08-05 16:30:04,648 - INFO -     - Discriminator Loss (D): 0.7967
2025-08-05 16:30:04,648 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-05 16:30:51,740 - INFO - Epoch 250:
2025-08-05 16:30:51,740 - INFO -   Total Loss: 18.467260
2025-08-05 16:30:51,740 - INFO -   PEHE: 0.319662
2025-08-05 16:30:51,741 - INFO -   ATE Error: 0.010541
2025-08-05 16:30:51,741 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=15.0032
2025-08-05 16:30:51,741 - INFO -     - Discriminator Loss (D): 0.6945
2025-08-05 16:30:51,741 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-05 16:31:39,078 - INFO - Epoch 300:
2025-08-05 16:31:39,078 - INFO -   Total Loss: 18.465899
2025-08-05 16:31:39,078 - INFO -   PEHE: 0.319667
2025-08-05 16:31:39,078 - INFO -   ATE Error: 0.010677
2025-08-05 16:31:39,078 - INFO -     - Main Losses (G): Factual=0.6829, Adversarial=0.6931, Decomp=15.0032
2025-08-05 16:31:39,078 - INFO -     - Discriminator Loss (D): 0.6931
2025-08-05 16:31:39,078 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-05 16:32:26,759 - INFO - Epoch 350:
2025-08-05 16:32:26,759 - INFO -   Total Loss: 18.465906
2025-08-05 16:32:26,759 - INFO -   PEHE: 0.319672
2025-08-05 16:32:26,759 - INFO -   ATE Error: 0.010846
2025-08-05 16:32:26,759 - INFO -     - Main Losses (G): Factual=0.6829, Adversarial=0.6931, Decomp=15.0032
2025-08-05 16:32:26,760 - INFO -     - Discriminator Loss (D): 0.6931
2025-08-05 16:32:26,760 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-05 16:33:14,602 - INFO - Stage 1 training completed!
2025-08-05 16:33:14,881 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-08-05 16:33:14,885 - INFO - Stage 2 training data shapes:
2025-08-05 16:33:14,885 - INFO -   x_train: (9120, 30)
2025-08-05 16:33:14,885 - INFO -   y_bar_target: (9120, 2)
2025-08-05 16:33:15,302 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\layers\normalization\layer_normalization.py:328: The name tf.nn.fused_batch_norm is deprecated. Please use tf.compat.v1.nn.fused_batch_norm instead.

2025-08-05 16:33:24,225 - INFO - Stage 2 - Iter: 0/400, ITE_C Loss: 5.0651 (Wass: 0.0025, GP: 0.5063), ITE_G Loss: 3.2608 (Adv: 1.2831, Sup: 0.0264)
2025-08-05 16:33:40,053 - INFO - Stage 2 - Iter: 100/400, ITE_C Loss: 5.4112 (Wass: 0.0000, GP: 0.5411), ITE_G Loss: 1.1866 (Adv: 1.1805, Sup: 0.0001)
2025-08-05 16:33:55,068 - INFO - Stage 2 - Iter: 200/400, ITE_C Loss: 4.9863 (Wass: 0.0000, GP: 0.4986), ITE_G Loss: 1.1845 (Adv: 1.1752, Sup: 0.0001)
2025-08-05 16:34:10,774 - INFO - Stage 2 - Iter: 300/400, ITE_C Loss: 5.3028 (Wass: -0.0000, GP: 0.5303), ITE_G Loss: 1.2643 (Adv: 1.2593, Sup: 0.0001)
2025-08-05 16:34:25,894 - INFO - Stage 2 - Iter: 399/400, ITE_C Loss: 4.4718 (Wass: 0.0000, GP: 0.4472), ITE_G Loss: 0.9465 (Adv: 0.9442, Sup: 0.0000)
2025-08-05 16:34:25,894 - INFO - Stage 2 training completed!
2025-08-05 16:34:25,916 - INFO - Stage2 model save skipped for quick test
2025-08-05 16:49:25,320 - INFO - Starting Stage 1 training...
2025-08-05 16:49:25,321 - INFO - Training samples: 9120
2025-08-05 16:49:25,321 - INFO - Test samples: 2280
2025-08-05 16:49:25,322 - INFO - Sample weights initialized: shape=(9120, 1)
2025-08-05 16:49:25,603 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-08-05 16:49:26,830 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-08-05 16:49:35,039 - INFO - Epoch 0:
2025-08-05 16:49:35,040 - INFO -   Total Loss: 6165.723145
2025-08-05 16:49:35,040 - INFO -   PEHE: 0.327004
2025-08-05 16:49:35,040 - INFO -   ATE Error: 0.057889
2025-08-05 16:49:35,040 - INFO -     - Main Losses (G): Factual=0.8492, Adversarial=0.6843, Decomp=22.2245
2025-08-05 16:49:35,040 - INFO -     - Discriminator Loss (D): 24.2568
2025-08-05 16:49:35,040 - INFO -     - Balance Loss (Weights): 2.9116
2025-08-05 16:49:35,040 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-05 16:49:35,128 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-05 16:49:35,129 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-05 16:49:35,177 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-08-05 16:50:17,966 - INFO - Epoch 50:
2025-08-05 16:50:17,970 - INFO -   Total Loss: 27.140379
2025-08-05 16:50:17,970 - INFO -   PEHE: 0.319725
2025-08-05 16:50:17,970 - INFO -   ATE Error: 0.012289
2025-08-05 16:50:17,970 - INFO -     - Main Losses (G): Factual=0.6832, Adversarial=0.6919, Decomp=15.0032
2025-08-05 16:50:17,970 - INFO -     - Discriminator Loss (D): 9.3768
2025-08-05 16:50:17,970 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-05 16:50:17,970 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-05 16:50:18,033 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-05 16:51:03,630 - INFO - Epoch 100:
2025-08-05 16:51:03,630 - INFO -   Total Loss: 20.753168
2025-08-05 16:51:03,630 - INFO -   PEHE: 0.319669
2025-08-05 16:51:03,630 - INFO -   ATE Error: 0.010755
2025-08-05 16:51:03,634 - INFO -     - Main Losses (G): Factual=0.6831, Adversarial=0.6927, Decomp=15.0032
2025-08-05 16:51:03,634 - INFO -     - Discriminator Loss (D): 2.9837
2025-08-05 16:51:03,634 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-05 16:51:03,634 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-05 16:51:03,671 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-05 16:51:49,678 - INFO - Epoch 150:
2025-08-05 16:51:49,678 - INFO -   Total Loss: 18.799377
2025-08-05 16:51:49,678 - INFO -   PEHE: 0.319670
2025-08-05 16:51:49,678 - INFO -   ATE Error: 0.010775
2025-08-05 16:51:49,678 - INFO -     - Main Losses (G): Factual=0.6827, Adversarial=0.6932, Decomp=15.0032
2025-08-05 16:51:49,678 - INFO -     - Discriminator Loss (D): 1.0268
2025-08-05 16:51:49,678 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-05 16:53:04,372 - INFO - Starting Stage 1 training...
2025-08-05 16:53:04,372 - INFO - Training samples: 9120
2025-08-05 16:53:04,372 - INFO - Test samples: 2280
2025-08-05 16:53:04,372 - INFO - Sample weights initialized: shape=(9120, 1)
2025-08-05 16:53:04,746 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-08-05 16:53:06,186 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-08-05 16:53:16,185 - INFO - Epoch 0:
2025-08-05 16:53:16,185 - INFO -   Total Loss: 6160.091797
2025-08-05 16:53:16,189 - INFO -   PEHE: 0.407740
2025-08-05 16:53:16,189 - INFO -   ATE Error: 0.247735
2025-08-05 16:53:16,189 - INFO -     - Main Losses (G): Factual=1.0392, Adversarial=0.6944, Decomp=20.1095
2025-08-05 16:53:16,189 - INFO -     - Discriminator Loss (D): 29.0875
2025-08-05 16:53:16,189 - INFO -     - Balance Loss (Weights): 2.8392
2025-08-05 16:53:16,189 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-05 16:53:16,264 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-05 16:53:16,268 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-05 16:53:16,300 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-08-05 16:53:28,339 - INFO - Epoch 10:
2025-08-05 16:53:28,340 - INFO -   Total Loss: 1708.856079
2025-08-05 16:53:28,340 - INFO -   PEHE: 0.319662
2025-08-05 16:53:28,340 - INFO -   ATE Error: 0.010527
2025-08-05 16:53:28,341 - INFO -     - Main Losses (G): Factual=0.6827, Adversarial=0.6943, Decomp=15.0032
2025-08-05 16:53:28,341 - INFO -     - Discriminator Loss (D): 24.8575
2025-08-05 16:53:28,341 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-05 16:53:28,342 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-05 16:53:28,378 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-05 16:53:37,588 - INFO - Epoch 20:
2025-08-05 16:53:37,588 - INFO -   Total Loss: 635.639465
2025-08-05 16:53:37,589 - INFO -   PEHE: 0.319661
2025-08-05 16:53:37,589 - INFO -   ATE Error: 0.010511
2025-08-05 16:53:37,589 - INFO -     - Main Losses (G): Factual=0.6826, Adversarial=0.6937, Decomp=15.0032
2025-08-05 16:53:37,589 - INFO -     - Discriminator Loss (D): 21.0752
2025-08-05 16:53:37,589 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-05 16:53:37,590 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-05 16:53:37,626 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-05 16:53:46,105 - INFO - Epoch 30:
2025-08-05 16:53:46,105 - INFO -   Total Loss: 101.982193
2025-08-05 16:53:46,105 - INFO -   PEHE: 0.319659
2025-08-05 16:53:46,105 - INFO -   ATE Error: 0.010433
2025-08-05 16:53:46,105 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6934, Decomp=15.0032
2025-08-05 16:53:46,105 - INFO -     - Discriminator Loss (D): 17.7180
2025-08-05 16:53:46,105 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-05 16:53:46,109 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-05 16:53:46,146 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-05 16:53:55,588 - INFO - Epoch 40:
2025-08-05 16:53:55,588 - INFO -   Total Loss: 32.531963
2025-08-05 16:53:55,588 - INFO -   PEHE: 0.319661
2025-08-05 16:53:55,588 - INFO -   ATE Error: 0.010520
2025-08-05 16:53:55,588 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6932, Decomp=15.0032
2025-08-05 16:53:55,588 - INFO -     - Discriminator Loss (D): 14.7629
2025-08-05 16:53:55,588 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-05 16:54:04,791 - INFO - Epoch 50:
2025-08-05 16:54:04,795 - INFO -   Total Loss: 29.936445
2025-08-05 16:54:04,795 - INFO -   PEHE: 0.319676
2025-08-05 16:54:04,795 - INFO -   ATE Error: 0.010945
2025-08-05 16:54:04,795 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6932, Decomp=15.0032
2025-08-05 16:54:04,795 - INFO -     - Discriminator Loss (D): 12.1671
2025-08-05 16:54:04,795 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-05 16:54:13,867 - INFO - Epoch 60:
2025-08-05 16:54:13,867 - INFO -   Total Loss: 27.668655
2025-08-05 16:54:13,867 - INFO -   PEHE: 0.319667
2025-08-05 16:54:13,871 - INFO -   ATE Error: 0.010685
2025-08-05 16:54:13,871 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=15.0032
2025-08-05 16:54:13,871 - INFO -     - Discriminator Loss (D): 9.8989
2025-08-05 16:54:13,871 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-05 16:54:23,096 - INFO - Epoch 70:
2025-08-05 16:54:23,100 - INFO -   Total Loss: 25.708309
2025-08-05 16:54:23,100 - INFO -   PEHE: 0.319652
2025-08-05 16:54:23,100 - INFO -   ATE Error: 0.010239
2025-08-05 16:54:23,100 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6931, Decomp=15.0032
2025-08-05 16:54:23,100 - INFO -     - Discriminator Loss (D): 7.9381
2025-08-05 16:54:23,100 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-05 16:54:23,100 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-05 16:54:23,140 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-05 16:54:32,821 - INFO - Epoch 80:
2025-08-05 16:54:32,822 - INFO -   Total Loss: 24.044790
2025-08-05 16:54:32,822 - INFO -   PEHE: 0.319686
2025-08-05 16:54:32,822 - INFO -   ATE Error: 0.011238
2025-08-05 16:54:32,823 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6938, Decomp=15.0032
2025-08-05 16:54:32,823 - INFO -     - Discriminator Loss (D): 6.2711
2025-08-05 16:54:32,823 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-05 16:54:41,427 - INFO - Epoch 90:
2025-08-05 16:54:41,427 - INFO -   Total Loss: 22.661240
2025-08-05 16:54:41,427 - INFO -   PEHE: 0.319666
2025-08-05 16:54:41,427 - INFO -   ATE Error: 0.010656
2025-08-05 16:54:41,427 - INFO -     - Main Losses (G): Factual=0.6828, Adversarial=0.6935, Decomp=15.0032
2025-08-05 16:54:41,427 - INFO -     - Discriminator Loss (D): 4.8884
2025-08-05 16:54:41,427 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-05 16:54:48,983 - INFO - Stage 1 training completed!
2025-08-05 16:54:49,173 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-08-05 16:54:49,174 - INFO - Stage 2 training data shapes:
2025-08-05 16:54:49,174 - INFO -   x_train: (9120, 30)
2025-08-05 16:54:49,174 - INFO -   y_bar_target: (9120, 2)
2025-08-05 16:54:49,584 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\layers\normalization\layer_normalization.py:328: The name tf.nn.fused_batch_norm is deprecated. Please use tf.compat.v1.nn.fused_batch_norm instead.

2025-08-05 16:54:57,641 - INFO - Stage 2 - Iter: 0/100, ITE_C Loss: 5.7784 (Wass: 0.0022, GP: 0.5776), ITE_G Loss: 1.9059 (Adv: -0.0087, Sup: 0.0255)
2025-08-05 16:55:10,878 - INFO - Stage 2 - Iter: 99/100, ITE_C Loss: 6.0041 (Wass: 0.0000, GP: 0.6004), ITE_G Loss: 0.2326 (Adv: 0.2064, Sup: 0.0003)
2025-08-05 16:55:10,879 - INFO - Stage 2 training completed!
2025-08-05 16:55:10,897 - INFO - Stage2 model save skipped for quick test
2025-08-05 18:01:22,980 - INFO - Starting Stage 1 training...
2025-08-05 18:01:22,980 - INFO - Training samples: 9120
2025-08-05 18:01:22,980 - INFO - Test samples: 2280
2025-08-05 18:01:22,980 - INFO - Sample weights initialized: shape=(9120, 1)
2025-08-05 18:01:23,294 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-08-05 18:01:24,719 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-08-05 18:01:32,974 - INFO - Epoch 0:
2025-08-05 18:01:32,975 - INFO -   Total Loss: 6152.707520
2025-08-05 18:01:32,975 - INFO -   PEHE: 0.340926
2025-08-05 18:01:32,975 - INFO -   ATE Error: 0.109380
2025-08-05 18:01:32,975 - INFO -     - Main Losses (G): Factual=0.7562, Adversarial=0.7219, Decomp=20.9180
2025-08-05 18:01:32,975 - INFO -     - Discriminator Loss (D): 32.1280
2025-08-05 18:01:32,976 - INFO -     - Balance Loss (Weights): 3.0493
2025-08-05 18:01:32,977 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-05 18:01:33,059 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-05 18:01:33,063 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-05 18:01:33,098 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-08-05 18:02:17,119 - INFO - Epoch 50:
2025-08-05 18:02:17,122 - INFO -   Total Loss: 31.608652
2025-08-05 18:02:17,122 - INFO -   PEHE: 0.319756
2025-08-05 18:02:17,122 - INFO -   ATE Error: 0.013082
2025-08-05 18:02:17,122 - INFO -     - Main Losses (G): Factual=0.6818, Adversarial=0.7061, Decomp=15.0032
2025-08-05 18:02:17,122 - INFO -     - Discriminator Loss (D): 13.7760
2025-08-05 18:02:17,122 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-05 18:02:17,122 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-05 18:02:17,163 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-05 18:03:02,811 - INFO - Epoch 100:
2025-08-05 18:03:02,815 - INFO -   Total Loss: 22.577917
2025-08-05 18:03:02,816 - INFO -   PEHE: 0.319673
2025-08-05 18:03:02,816 - INFO -   ATE Error: 0.010877
2025-08-05 18:03:02,816 - INFO -     - Main Losses (G): Factual=0.6827, Adversarial=0.6949, Decomp=15.0032
2025-08-05 18:03:02,816 - INFO -     - Discriminator Loss (D): 4.7979
2025-08-05 18:03:02,816 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-05 18:03:02,819 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-05 18:03:02,878 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-05 18:03:47,867 - INFO - Epoch 150:
2025-08-05 18:03:47,867 - INFO -   Total Loss: 19.302004
2025-08-05 18:03:47,867 - INFO -   PEHE: 0.319670
2025-08-05 18:03:47,867 - INFO -   ATE Error: 0.010782
2025-08-05 18:03:47,867 - INFO -     - Main Losses (G): Factual=0.6827, Adversarial=0.6932, Decomp=15.0032
2025-08-05 18:03:47,867 - INFO -     - Discriminator Loss (D): 1.5294
2025-08-05 18:03:47,867 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-05 18:03:47,867 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-05 18:03:47,914 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-05 18:04:31,402 - INFO - Stage 1 training completed!
2025-08-05 18:04:31,564 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-08-05 18:04:31,564 - INFO - Stage 2 training data shapes:
2025-08-05 18:04:31,564 - INFO -   x_train: (9120, 30)
2025-08-05 18:04:31,564 - INFO -   y_bar_target: (9120, 2)
2025-08-05 18:04:31,852 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\layers\normalization\layer_normalization.py:328: The name tf.nn.fused_batch_norm is deprecated. Please use tf.compat.v1.nn.fused_batch_norm instead.

2025-08-05 18:04:39,638 - INFO - Stage 2 - Iter: 0/200, ITE_C Loss: 4.0279 (Wass: -0.0033, GP: 0.4031), ITE_G Loss: 1.5507 (Adv: -0.7330, Sup: 0.0305)
2025-08-05 18:04:56,143 - INFO - Stage 2 - Iter: 100/200, ITE_C Loss: 3.6712 (Wass: 0.0000, GP: 0.3671), ITE_G Loss: -0.7479 (Adv: -0.7768, Sup: 0.0004)
2025-08-05 18:05:13,815 - INFO - Stage 2 - Iter: 199/200, ITE_C Loss: 3.6285 (Wass: 0.0000, GP: 0.3629), ITE_G Loss: -0.6797 (Adv: -0.6852, Sup: 0.0001)
2025-08-05 18:05:13,815 - INFO - Stage 2 training completed!
2025-08-05 18:05:13,833 - INFO - Stage2 model save skipped for quick test
2025-08-05 18:07:36,441 - INFO - Starting Stage 1 training...
2025-08-05 18:07:36,441 - INFO - Training samples: 9120
2025-08-05 18:07:36,441 - INFO - Test samples: 2280
2025-08-05 18:07:36,441 - INFO - Sample weights initialized: shape=(9120, 1)
2025-08-05 18:07:36,743 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-08-05 18:07:37,952 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-08-05 18:07:44,877 - INFO - Epoch 0:
2025-08-05 18:07:44,877 - INFO -   Total Loss: 6164.765137
2025-08-05 18:07:44,877 - INFO -   PEHE: 0.327732
2025-08-05 18:07:44,877 - INFO -   ATE Error: 0.039276
2025-08-05 18:07:44,877 - INFO -     - Main Losses (G): Factual=0.7057, Adversarial=0.6810, Decomp=21.2744
2025-08-05 18:07:44,877 - INFO -     - Discriminator Loss (D): 31.1657
2025-08-05 18:07:44,877 - INFO -     - Balance Loss (Weights): 2.8876
2025-08-05 18:07:44,877 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-05 18:07:45,015 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-05 18:07:45,015 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-05 18:07:45,067 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-08-05 18:08:45,375 - INFO - Epoch 50:
2025-08-05 18:08:45,375 - INFO -   Total Loss: 30.916552
2025-08-05 18:08:45,375 - INFO -   PEHE: 0.319711
2025-08-05 18:08:45,375 - INFO -   ATE Error: 0.011945
2025-08-05 18:08:45,375 - INFO -     - Main Losses (G): Factual=0.6830, Adversarial=0.6932, Decomp=15.0032
2025-08-05 18:08:45,375 - INFO -     - Discriminator Loss (D): 13.1476
2025-08-05 18:08:45,375 - INFO -     - Balance Loss (Weights): 0.0000
2025-08-05 18:08:45,375 - WARNING - Failed to save model: ./results\models is not a directory
2025-08-05 18:08:45,435 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-08-05 18:09:37,642 - INFO - Stage 1 training completed!
2025-08-05 18:09:37,822 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-08-05 18:09:37,822 - INFO - Stage 2 training data shapes:
2025-08-05 18:09:37,822 - INFO -   x_train: (9120, 30)
2025-08-05 18:09:37,822 - INFO -   y_bar_target: (9120, 2)
2025-08-05 18:09:38,130 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\layers\normalization\layer_normalization.py:328: The name tf.nn.fused_batch_norm is deprecated. Please use tf.compat.v1.nn.fused_batch_norm instead.

2025-08-05 18:09:44,685 - INFO - Stage 2 - Iter: 0/100, ITE_C Loss: 5.2203 (Wass: 0.0006, GP: 0.5220), ITE_G Loss: 1.5458 (Adv: -0.2808, Sup: 0.0244)
2025-08-05 18:09:58,288 - INFO - Stage 2 - Iter: 99/100, ITE_C Loss: 4.7749 (Wass: -0.0000, GP: 0.4775), ITE_G Loss: -0.1092 (Adv: -0.1376, Sup: 0.0004)
2025-08-05 18:09:58,288 - INFO - Stage 2 training completed!
2025-08-05 18:09:58,307 - INFO - Stage2 model save skipped for quick test
2025-09-10 02:38:23,274 - INFO - Starting Stage 1 training...
2025-09-10 02:38:23,274 - INFO - Training samples: 597
2025-09-10 02:38:23,274 - INFO - Test samples: 150
2025-09-10 02:38:23,274 - INFO - Sample weights initialized: shape=(597, 1)
2025-09-10 02:38:23,928 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-09-10 02:38:26,194 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-09-10 02:38:34,527 - INFO - Epoch 0:
2025-09-10 02:38:34,527 - INFO -   Total Loss: 6995.951172
2025-09-10 02:38:34,527 - INFO -   PEHE: 4.216065
2025-09-10 02:38:34,527 - INFO -   ATE Error: 4.049089
2025-09-10 02:38:34,527 - INFO -     - Main Losses (G): Factual=3.2678, Adversarial=1.1766, Decomp=34.9261
2025-09-10 02:38:34,527 - INFO -     - Discriminator Loss (D): 25.9362
2025-09-10 02:38:34,530 - INFO -     - Balance Loss (Weights): 7.1809
2025-09-10 02:38:34,533 - WARNING - Failed to save model: ./results\models is not a directory
2025-09-10 02:38:34,688 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-09-10 02:38:34,688 - WARNING - Failed to save model: ./results\models is not a directory
2025-09-10 02:38:34,753 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-09-10 02:38:37,271 - INFO - Epoch 50:
2025-09-10 02:38:37,272 - INFO -   Total Loss: 3466.423340
2025-09-10 02:38:37,272 - INFO -   PEHE: 3.903276
2025-09-10 02:38:37,272 - INFO -   ATE Error: 3.752419
2025-09-10 02:38:37,272 - INFO -     - Main Losses (G): Factual=-10.6789, Adversarial=1.1201, Decomp=8.2856
2025-09-10 02:38:37,272 - INFO -     - Discriminator Loss (D): 24.7555
2025-09-10 02:38:37,273 - INFO -     - Balance Loss (Weights): 8.7381
2025-09-10 02:38:37,273 - WARNING - Failed to save model: ./results\models is not a directory
2025-09-10 02:38:37,381 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-09-10 02:38:39,942 - INFO - Epoch 100:
2025-09-10 02:38:39,942 - INFO -   Total Loss: 2451.721191
2025-09-10 02:38:39,942 - INFO -   PEHE: 4.069751
2025-09-10 02:38:39,942 - INFO -   ATE Error: 3.926875
2025-09-10 02:38:39,942 - INFO -     - Main Losses (G): Factual=-24.8278, Adversarial=1.0895, Decomp=15.1184
2025-09-10 02:38:39,942 - INFO -     - Discriminator Loss (D): 23.6045
2025-09-10 02:38:39,947 - INFO -     - Balance Loss (Weights): 17.6106
2025-09-10 02:38:42,504 - INFO - Epoch 150:
2025-09-10 02:38:42,504 - INFO -   Total Loss: 1978.808105
2025-09-10 02:38:42,504 - INFO -   PEHE: 4.153296
2025-09-10 02:38:42,504 - INFO -   ATE Error: 4.025447
2025-09-10 02:38:42,504 - INFO -     - Main Losses (G): Factual=-33.4779, Adversarial=1.0735, Decomp=15.2731
2025-09-10 02:38:42,504 - INFO -     - Discriminator Loss (D): 22.4918
2025-09-10 02:38:42,504 - INFO -     - Balance Loss (Weights): 14.9584
2025-09-10 02:38:44,914 - INFO - Stage 1 training completed!
2025-09-10 02:38:45,026 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-09-10 02:38:45,026 - INFO - Stage 2 training data shapes:
2025-09-10 02:38:45,026 - INFO -   x_train: (597, 25)
2025-09-10 02:38:45,026 - INFO -   y_bar_target: (597, 2)
2025-09-10 02:38:45,558 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\layers\normalization\layer_normalization.py:328: The name tf.nn.fused_batch_norm is deprecated. Please use tf.compat.v1.nn.fused_batch_norm instead.

2025-09-10 02:38:54,915 - INFO - Stage 2 - Iter: 0/200, ITE_C Loss: 6.3173 (Wass: 0.0116, GP: 0.6306), ITE_G Loss: 16.0782 (Adv: -0.0008, Sup: 0.2144)
2025-09-10 02:38:56,462 - INFO - Stage 2 - Iter: 100/200, ITE_C Loss: 6.2698 (Wass: -0.0091, GP: 0.6279), ITE_G Loss: 8.1127 (Adv: -0.0202, Sup: 0.1084)
2025-09-10 02:38:58,096 - INFO - Stage 2 - Iter: 199/200, ITE_C Loss: 6.0159 (Wass: -0.0118, GP: 0.6028), ITE_G Loss: 6.8920 (Adv: 0.1789, Sup: 0.0895)
2025-09-10 02:38:58,096 - INFO - Stage 2 training completed!
2025-09-10 02:38:58,115 - INFO - Stage2 model save skipped for quick test
2025-09-10 03:20:38,151 - INFO - Starting Stage 1 training...
2025-09-10 03:20:38,151 - INFO - Training samples: 597
2025-09-10 03:20:38,151 - INFO - Test samples: 150
2025-09-10 03:20:38,151 - INFO - Sample weights initialized: shape=(597, 1)
2025-09-10 03:20:38,524 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-09-10 03:20:40,088 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-09-10 03:20:47,897 - INFO - Epoch 0:
2025-09-10 03:20:47,897 - INFO -   Total Loss: 488.727234
2025-09-10 03:20:47,897 - INFO -   PEHE: 4.223054
2025-09-10 03:20:47,898 - INFO -   ATE Error: 4.054711
2025-09-10 03:20:47,898 - INFO -     - Main Losses (G): Factual=-0.0498, Adversarial=0.5319, Decomp=464.2184
2025-09-10 03:20:47,898 - INFO -     - Discriminator Loss (D): 0.9922
2025-09-10 03:20:47,898 - INFO -     - Balance Loss (Weights): 0.0277
2025-09-10 03:20:47,899 - WARNING - Failed to save model: ./results\models is not a directory
2025-09-10 03:20:47,998 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-09-10 03:20:47,999 - WARNING - Failed to save model: ./results\models is not a directory
2025-09-10 03:20:48,058 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-09-10 03:20:52,674 - INFO - Epoch 50:
2025-09-10 03:20:52,674 - INFO -   Total Loss: 115.668915
2025-09-10 03:20:52,674 - INFO -   PEHE: 4.033447
2025-09-10 03:20:52,674 - INFO -   ATE Error: 3.875150
2025-09-10 03:20:52,674 - INFO -     - Main Losses (G): Factual=-59.8119, Adversarial=0.5590, Decomp=156.2061
2025-09-10 03:20:52,674 - INFO -     - Discriminator Loss (D): 0.9343
2025-09-10 03:20:52,674 - INFO -     - Balance Loss (Weights): 0.0182
2025-09-10 03:20:52,674 - WARNING - Failed to save model: ./results\models is not a directory
2025-09-10 03:20:52,751 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-09-10 03:20:57,653 - INFO - Epoch 100:
2025-09-10 03:20:57,653 - INFO -   Total Loss: 37.161690
2025-09-10 03:20:57,653 - INFO -   PEHE: 4.084544
2025-09-10 03:20:57,653 - INFO -   ATE Error: 3.921469
2025-09-10 03:20:57,654 - INFO -     - Main Losses (G): Factual=-131.2083, Adversarial=0.5929, Decomp=150.4257
2025-09-10 03:20:57,654 - INFO -     - Discriminator Loss (D): 0.8780
2025-09-10 03:20:57,654 - INFO -     - Balance Loss (Weights): 0.0329
2025-09-10 03:21:02,104 - INFO - Epoch 150:
2025-09-10 03:21:02,104 - INFO -   Total Loss: -61.870567
2025-09-10 03:21:02,104 - INFO -   PEHE: 4.212059
2025-09-10 03:21:02,104 - INFO -   ATE Error: 4.048409
2025-09-10 03:21:02,104 - INFO -     - Main Losses (G): Factual=-225.3517, Adversarial=0.6225, Decomp=145.8360
2025-09-10 03:21:02,104 - INFO -     - Discriminator Loss (D): 0.8339
2025-09-10 03:21:02,104 - INFO -     - Balance Loss (Weights): 0.0339
2025-09-10 03:21:06,748 - INFO - Stage 1 training completed!
2025-09-10 03:21:06,857 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-09-10 03:21:06,857 - INFO - Stage 2 training data shapes:
2025-09-10 03:21:06,858 - INFO -   x_train: (597, 25)
2025-09-10 03:21:06,858 - INFO -   y_bar_target: (597, 2)
2025-09-10 03:21:07,233 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\layers\normalization\layer_normalization.py:328: The name tf.nn.fused_batch_norm is deprecated. Please use tf.compat.v1.nn.fused_batch_norm instead.

2025-09-10 03:21:16,398 - INFO - Stage 2 - Iter: 0/200, ITE_C Loss: 7.0951 (Wass: -0.0154, GP: 0.7111), ITE_G Loss: 35.7698 (Adv: -0.3371, Sup: 0.4814)
2025-09-10 03:21:20,222 - INFO - Stage 2 - Iter: 100/200, ITE_C Loss: 6.8918 (Wass: 0.0072, GP: 0.6885), ITE_G Loss: 4.2926 (Adv: -0.4302, Sup: 0.0630)
2025-09-10 03:21:23,902 - INFO - Stage 2 - Iter: 199/200, ITE_C Loss: 6.7214 (Wass: -0.0018, GP: 0.6723), ITE_G Loss: 2.2083 (Adv: -0.4614, Sup: 0.0356)
2025-09-10 03:21:23,902 - INFO - Stage 2 training completed!
2025-09-10 03:21:23,910 - INFO - Stage2 model save skipped for quick test
2025-09-10 03:23:27,657 - INFO - Starting Stage 1 training...
2025-09-10 03:23:27,657 - INFO - Training samples: 597
2025-09-10 03:23:27,657 - INFO - Test samples: 150
2025-09-10 03:23:27,657 - INFO - Sample weights initialized: shape=(597, 1)
2025-09-10 03:23:28,039 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-09-10 03:23:29,292 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-09-10 03:23:37,345 - INFO - Epoch 0:
2025-09-10 03:23:37,345 - INFO -   Total Loss: 425.811310
2025-09-10 03:23:37,345 - INFO -   PEHE: 4.220870
2025-09-10 03:23:37,345 - INFO -   ATE Error: 4.051518
2025-09-10 03:23:37,345 - INFO -     - Main Losses (G): Factual=1.1871, Adversarial=0.7510, Decomp=399.1279
2025-09-10 03:23:37,345 - INFO -     - Discriminator Loss (D): 0.7031
2025-09-10 03:23:37,345 - INFO -     - Balance Loss (Weights): 0.0151
2025-09-10 03:23:37,345 - WARNING - Failed to save model: ./results\models is not a directory
2025-09-10 03:23:37,456 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-09-10 03:23:37,456 - WARNING - Failed to save model: ./results\models is not a directory
2025-09-10 03:23:37,558 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-09-10 03:23:39,762 - INFO - Epoch 50:
2025-09-10 03:23:39,762 - INFO -   Total Loss: 175.020203
2025-09-10 03:23:39,762 - INFO -   PEHE: 3.514078
2025-09-10 03:23:39,763 - INFO -   ATE Error: 3.314595
2025-09-10 03:23:39,763 - INFO -     - Main Losses (G): Factual=-21.8697, Adversarial=0.7824, Decomp=172.9055
2025-09-10 03:23:39,763 - INFO -     - Discriminator Loss (D): 0.6800
2025-09-10 03:23:39,763 - INFO -     - Balance Loss (Weights): 0.0402
2025-09-10 03:23:39,763 - WARNING - Failed to save model: ./results\models is not a directory
2025-09-10 03:23:39,836 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-09-10 03:23:42,002 - INFO - Epoch 100:
2025-09-10 03:23:42,003 - INFO -   Total Loss: 143.531982
2025-09-10 03:23:42,003 - INFO -   PEHE: 3.771047
2025-09-10 03:23:42,003 - INFO -   ATE Error: 3.567884
2025-09-10 03:23:42,003 - INFO -     - Main Losses (G): Factual=-38.2884, Adversarial=0.8056, Decomp=159.7340
2025-09-10 03:23:42,003 - INFO -     - Discriminator Loss (D): 0.6623
2025-09-10 03:23:42,004 - INFO -     - Balance Loss (Weights): 0.0440
2025-09-10 03:23:44,281 - INFO - Epoch 150:
2025-09-10 03:23:44,282 - INFO -   Total Loss: 121.258278
2025-09-10 03:23:44,282 - INFO -   PEHE: 3.886114
2025-09-10 03:23:44,282 - INFO -   ATE Error: 3.694002
2025-09-10 03:23:44,282 - INFO -     - Main Losses (G): Factual=-55.1673, Adversarial=0.8222, Decomp=156.0913
2025-09-10 03:23:44,282 - INFO -     - Discriminator Loss (D): 0.6484
2025-09-10 03:23:44,282 - INFO -     - Balance Loss (Weights): 0.0781
2025-09-10 03:23:46,517 - INFO - Stage 1 training completed!
2025-09-10 03:23:46,673 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-09-10 03:23:46,673 - INFO - Stage 2 training data shapes:
2025-09-10 03:23:46,673 - INFO -   x_train: (597, 25)
2025-09-10 03:23:46,673 - INFO -   y_bar_target: (597, 2)
2025-09-10 03:23:47,033 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\layers\normalization\layer_normalization.py:328: The name tf.nn.fused_batch_norm is deprecated. Please use tf.compat.v1.nn.fused_batch_norm instead.

2025-09-10 03:23:52,516 - INFO - Stage 2 - Iter: 0/200, ITE_C Loss: 6.3584 (Wass: 0.0133, GP: 0.6345), ITE_G Loss: 36.2557 (Adv: 1.9223, Sup: 0.4578)
2025-09-10 03:23:54,610 - INFO - Stage 2 - Iter: 100/200, ITE_C Loss: 6.3672 (Wass: 0.0031, GP: 0.6364), ITE_G Loss: 14.2877 (Adv: 1.9324, Sup: 0.1647)
2025-09-10 03:23:56,776 - INFO - Stage 2 - Iter: 199/200, ITE_C Loss: 6.3586 (Wass: -0.0014, GP: 0.6360), ITE_G Loss: 8.1730 (Adv: 1.9370, Sup: 0.0831)
2025-09-10 03:23:56,776 - INFO - Stage 2 training completed!
2025-09-10 03:23:56,809 - INFO - Stage2 model save skipped for quick test
2025-09-10 10:35:15,520 - INFO - Starting Stage 1 training...
2025-09-10 10:35:15,525 - INFO - Training samples: 597
2025-09-10 10:35:15,525 - INFO - Test samples: 150
2025-09-10 10:35:15,525 - INFO - Sample weights initialized: shape=(597, 1)
2025-09-10 10:35:16,206 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-09-10 10:35:17,953 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-09-10 10:35:24,920 - INFO - Epoch 0:
2025-09-10 10:35:24,920 - INFO -   Total Loss: 426.476532
2025-09-10 10:35:24,920 - INFO -   PEHE: 4.504434
2025-09-10 10:35:24,920 - INFO -   ATE Error: 4.154603
2025-09-10 10:35:24,920 - INFO -     - Main Losses (G): Factual=-1.7999, Adversarial=0.5124, Decomp=403.7507
2025-09-10 10:35:24,920 - INFO -     - Discriminator Loss (D): 0.9917
2025-09-10 10:35:24,920 - INFO -     - Balance Loss (Weights): 0.0071
2025-09-10 10:35:24,927 - WARNING - Failed to save model: ./results\models is not a directory
2025-09-10 10:35:25,044 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-09-10 10:35:25,048 - WARNING - Failed to save model: ./results\models is not a directory
2025-09-10 10:35:25,115 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-09-10 10:35:27,030 - INFO - Epoch 50:
2025-09-10 10:35:27,030 - INFO -   Total Loss: 176.123337
2025-09-10 10:35:27,030 - INFO -   PEHE: 4.313612
2025-09-10 10:35:27,031 - INFO -   ATE Error: 3.961248
2025-09-10 10:35:27,031 - INFO -     - Main Losses (G): Factual=-18.8205, Adversarial=0.5363, Decomp=171.9784
2025-09-10 10:35:27,031 - INFO -     - Discriminator Loss (D): 0.9524
2025-09-10 10:35:27,031 - INFO -     - Balance Loss (Weights): 0.0178
2025-09-10 10:35:27,032 - WARNING - Failed to save model: ./results\models is not a directory
2025-09-10 10:35:27,105 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-09-10 10:35:29,126 - INFO - Epoch 100:
2025-09-10 10:35:29,126 - INFO -   Total Loss: 141.318039
2025-09-10 10:35:29,126 - INFO -   PEHE: 4.090378
2025-09-10 10:35:29,126 - INFO -   ATE Error: 3.745517
2025-09-10 10:35:29,126 - INFO -     - Main Losses (G): Factual=-39.9153, Adversarial=0.5663, Decomp=160.1152
2025-09-10 10:35:29,126 - INFO -     - Discriminator Loss (D): 0.9057
2025-09-10 10:35:29,126 - INFO -     - Balance Loss (Weights): 0.0347
2025-09-10 10:35:29,126 - WARNING - Failed to save model: ./results\models is not a directory
2025-09-10 10:35:29,215 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-09-10 10:35:31,081 - INFO - Epoch 150:
2025-09-10 10:35:31,081 - INFO -   Total Loss: 118.863800
2025-09-10 10:35:31,081 - INFO -   PEHE: 4.230215
2025-09-10 10:35:31,081 - INFO -   ATE Error: 3.874123
2025-09-10 10:35:31,081 - INFO -     - Main Losses (G): Factual=-58.1582, Adversarial=0.5941, Decomp=157.6846
2025-09-10 10:35:31,081 - INFO -     - Discriminator Loss (D): 0.8663
2025-09-10 10:35:31,081 - INFO -     - Balance Loss (Weights): 0.0763
2025-09-10 10:35:32,996 - INFO - Stage 1 training completed!
2025-09-10 10:35:33,089 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-09-10 10:35:33,090 - INFO - Stage 2 training data shapes:
2025-09-10 10:35:33,090 - INFO -   x_train: (597, 25)
2025-09-10 10:35:33,090 - INFO -   y_bar_target: (597, 2)
2025-09-10 10:35:33,547 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\layers\normalization\layer_normalization.py:328: The name tf.nn.fused_batch_norm is deprecated. Please use tf.compat.v1.nn.fused_batch_norm instead.

2025-09-10 10:35:39,224 - INFO - Stage 2 - Iter: 0/200, ITE_C Loss: 5.7191 (Wass: -0.0085, GP: 0.5728), ITE_G Loss: 34.3180 (Adv: -0.0092, Sup: 0.4577)
2025-09-10 10:35:41,033 - INFO - Stage 2 - Iter: 100/200, ITE_C Loss: 5.7712 (Wass: 0.0048, GP: 0.5766), ITE_G Loss: 11.0850 (Adv: -0.0225, Sup: 0.1481)
2025-09-10 10:35:42,774 - INFO - Stage 2 - Iter: 199/200, ITE_C Loss: 5.7604 (Wass: 0.0034, GP: 0.5757), ITE_G Loss: 4.8427 (Adv: -0.0211, Sup: 0.0649)
2025-09-10 10:35:42,774 - INFO - Stage 2 training completed!
2025-09-10 10:35:42,790 - INFO - Stage2 model save skipped for quick test
2025-09-10 10:40:22,565 - INFO - Starting Stage 1 training...
2025-09-10 10:40:22,566 - INFO - Training samples: 597
2025-09-10 10:40:22,566 - INFO - Test samples: 150
2025-09-10 10:40:22,566 - INFO - Sample weights initialized: shape=(597, 1)
2025-09-10 10:40:22,888 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-09-10 10:40:24,240 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-09-10 10:40:43,132 - INFO - Epoch 0:
2025-09-10 10:40:43,132 - INFO -   Total Loss: 347.407532
2025-09-10 10:40:43,133 - INFO -   PEHE: 1.836505
2025-09-10 10:40:43,133 - INFO -   ATE Error: 1.695346
2025-09-10 10:40:43,133 - INFO -     - Main Losses (G): Factual=0.7138, Adversarial=0.5235, Decomp=322.0695
2025-09-10 10:40:43,133 - INFO -     - Discriminator Loss (D): 1.1944
2025-09-10 10:40:43,134 - INFO -     - Balance Loss (Weights): 0.0340
2025-09-10 10:40:43,137 - WARNING - Failed to save model: ./results\models is not a directory
2025-09-10 10:40:43,390 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-09-10 10:40:43,391 - WARNING - Failed to save model: ./results\models is not a directory
2025-09-10 10:40:43,543 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-09-10 10:40:47,217 - INFO - Epoch 50:
2025-09-10 10:40:47,217 - INFO -   Total Loss: 78.299034
2025-09-10 10:40:47,217 - INFO -   PEHE: 1.447927
2025-09-10 10:40:47,217 - INFO -   ATE Error: 1.267920
2025-09-10 10:40:47,217 - INFO -     - Main Losses (G): Factual=-0.7792, Adversarial=0.4859, Decomp=57.8494
2025-09-10 10:40:47,217 - INFO -     - Discriminator Loss (D): 1.3314
2025-09-10 10:40:47,217 - INFO -     - Balance Loss (Weights): 0.0060
2025-09-10 10:40:47,217 - WARNING - Failed to save model: ./results\models is not a directory
2025-09-10 10:40:47,355 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-09-10 10:40:51,214 - INFO - Epoch 100:
2025-09-10 10:40:51,214 - INFO -   Total Loss: 66.889053
2025-09-10 10:40:51,214 - INFO -   PEHE: 1.178425
2025-09-10 10:40:51,214 - INFO -   ATE Error: 0.983147
2025-09-10 10:40:51,216 - INFO -     - Main Losses (G): Factual=-3.5639, Adversarial=0.4626, Decomp=51.9755
2025-09-10 10:40:51,217 - INFO -     - Discriminator Loss (D): 1.3708
2025-09-10 10:40:51,218 - INFO -     - Balance Loss (Weights): 0.0096
2025-09-10 10:40:51,220 - WARNING - Failed to save model: ./results\models is not a directory
2025-09-10 10:40:51,371 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-09-10 10:40:55,219 - INFO - Epoch 150:
2025-09-10 10:40:55,219 - INFO -   Total Loss: 57.868713
2025-09-10 10:40:55,219 - INFO -   PEHE: 1.146500
2025-09-10 10:40:55,219 - INFO -   ATE Error: 0.951241
2025-09-10 10:40:55,219 - INFO -     - Main Losses (G): Factual=-6.7684, Adversarial=0.4571, Decomp=48.1600
2025-09-10 10:40:55,219 - INFO -     - Discriminator Loss (D): 1.3578
2025-09-10 10:40:55,219 - INFO -     - Balance Loss (Weights): 0.0079
2025-09-10 10:40:55,233 - WARNING - Failed to save model: ./results\models is not a directory
2025-09-10 10:40:55,462 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-09-10 10:40:59,218 - INFO - Stage 1 training completed!
2025-09-10 10:40:59,461 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-09-10 10:40:59,465 - INFO - Stage 2 training data shapes:
2025-09-10 10:40:59,465 - INFO -   x_train: (597, 25)
2025-09-10 10:40:59,466 - INFO -   y_bar_target: (597, 2)
2025-09-10 10:41:00,403 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\layers\normalization\layer_normalization.py:328: The name tf.nn.fused_batch_norm is deprecated. Please use tf.compat.v1.nn.fused_batch_norm instead.

2025-09-10 10:41:14,534 - INFO - Stage 2 - Iter: 0/200, ITE_C Loss: 7.2426 (Wass: 0.0245, GP: 0.7218), ITE_G Loss: 31.9431 (Adv: 0.8895, Sup: 0.4140)
2025-09-10 10:41:25,356 - INFO - Stage 2 - Iter: 100/200, ITE_C Loss: 7.2490 (Wass: -0.0008, GP: 0.7250), ITE_G Loss: 5.0194 (Adv: 0.9149, Sup: 0.0547)
2025-09-10 10:41:28,853 - INFO - Stage 2 - Iter: 199/200, ITE_C Loss: 7.2414 (Wass: -0.0003, GP: 0.7242), ITE_G Loss: 3.6329 (Adv: 0.9144, Sup: 0.0362)
2025-09-10 10:41:28,853 - INFO - Stage 2 training completed!
2025-09-10 10:41:28,870 - INFO - Stage2 model save skipped for quick test
2025-09-10 10:50:28,429 - INFO - Starting Stage 1 training...
2025-09-10 10:50:28,429 - INFO - Training samples: 597
2025-09-10 10:50:28,429 - INFO - Test samples: 150
2025-09-10 10:50:28,429 - INFO - Sample weights initialized: shape=(597, 1)
2025-09-10 10:50:28,794 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\backend.py:806: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-09-10 10:50:29,977 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\engine\base_layer_utils.py:189: The name tf.placeholder is deprecated. Please use tf.compat.v1.placeholder instead.

2025-09-10 10:50:37,625 - INFO - Epoch 0:
2025-09-10 10:50:37,625 - INFO -   Total Loss: 285.776001
2025-09-10 10:50:37,625 - INFO -   PEHE: 0.278041
2025-09-10 10:50:37,625 - INFO -   ATE Error: 0.256876
2025-09-10 10:50:37,625 - INFO -     - Main Losses (G): Factual=0.8894, Adversarial=0.6999, Decomp=259.5893
2025-09-10 10:50:37,625 - INFO -     - Discriminator Loss (D): 0.7506
2025-09-10 10:50:37,625 - INFO -     - Balance Loss (Weights): 0.0013
2025-09-10 10:50:37,625 - WARNING - Failed to save model: ./results\models is not a directory
2025-09-10 10:50:37,755 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-09-10 10:50:37,755 - WARNING - Failed to save model: ./results\models is not a directory
2025-09-10 10:50:37,857 - INFO - Model saved to fallback path: stage1_model_epoch_0_fallback
2025-09-10 10:50:39,926 - INFO - Epoch 50:
2025-09-10 10:50:39,939 - INFO -   Total Loss: 71.419250
2025-09-10 10:50:39,939 - INFO -   PEHE: 0.257462
2025-09-10 10:50:39,939 - INFO -   ATE Error: 0.234387
2025-09-10 10:50:39,939 - INFO -     - Main Losses (G): Factual=0.6141, Adversarial=0.6902, Decomp=50.3994
2025-09-10 10:50:39,939 - INFO -     - Discriminator Loss (D): 0.7594
2025-09-10 10:50:39,939 - INFO -     - Balance Loss (Weights): 0.0002
2025-09-10 10:50:39,940 - WARNING - Failed to save model: ./results\models is not a directory
2025-09-10 10:50:40,007 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-09-10 10:50:41,941 - INFO - Epoch 100:
2025-09-10 10:50:41,941 - INFO -   Total Loss: 64.821243
2025-09-10 10:50:41,941 - INFO -   PEHE: 0.206326
2025-09-10 10:50:41,941 - INFO -   ATE Error: 0.176538
2025-09-10 10:50:41,941 - INFO -     - Main Losses (G): Factual=0.6081, Adversarial=0.6898, Decomp=47.1049
2025-09-10 10:50:41,941 - INFO -     - Discriminator Loss (D): 0.7592
2025-09-10 10:50:41,941 - INFO -     - Balance Loss (Weights): 0.0005
2025-09-10 10:50:41,941 - WARNING - Failed to save model: ./results\models is not a directory
2025-09-10 10:50:42,007 - INFO - Model saved to fallback path: best_stage1_model_fallback
2025-09-10 10:50:44,090 - INFO - Epoch 150:
2025-09-10 10:50:44,090 - INFO -   Total Loss: 61.550606
2025-09-10 10:50:44,090 - INFO -   PEHE: 0.228530
2025-09-10 10:50:44,090 - INFO -   ATE Error: 0.201355
2025-09-10 10:50:44,090 - INFO -     - Main Losses (G): Factual=0.6085, Adversarial=0.6911, Decomp=45.8803
2025-09-10 10:50:44,090 - INFO -     - Discriminator Loss (D): 0.7573
2025-09-10 10:50:44,090 - INFO -     - Balance Loss (Weights): 0.0003
2025-09-10 10:50:46,092 - INFO - Stage 1 training completed!
2025-09-10 10:50:46,198 - INFO - Starting Stage 2 training with Stage 1 outputs...
2025-09-10 10:50:46,198 - INFO - Stage 2 training data shapes:
2025-09-10 10:50:46,199 - INFO -   x_train: (597, 25)
2025-09-10 10:50:46,199 - INFO -   y_bar_target: (597, 2)
2025-09-10 10:50:46,634 - WARNING - From D:\ruanjian\pyth\anaconda\ana\lib\site-packages\keras\src\layers\normalization\layer_normalization.py:328: The name tf.nn.fused_batch_norm is deprecated. Please use tf.compat.v1.nn.fused_batch_norm instead.

2025-09-10 10:50:52,022 - INFO - Stage 2 - Iter: 0/200, ITE_C Loss: 3.2864 (Wass: 0.0252, GP: 0.3261), ITE_G Loss: 19.6950 (Adv: -0.1854, Sup: 0.2651)
2025-09-10 10:50:53,859 - INFO - Stage 2 - Iter: 100/200, ITE_C Loss: 3.0440 (Wass: 0.0011, GP: 0.3043), ITE_G Loss: 0.2726 (Adv: -0.1613, Sup: 0.0058)
2025-09-10 10:50:55,599 - INFO - Stage 2 - Iter: 199/200, ITE_C Loss: 3.0438 (Wass: 0.0018, GP: 0.3042), ITE_G Loss: 0.1258 (Adv: -0.1620, Sup: 0.0038)
2025-09-10 10:50:55,599 - INFO - Stage 2 training completed!
2025-09-10 10:50:55,599 - INFO - Stage2 model save skipped for quick test

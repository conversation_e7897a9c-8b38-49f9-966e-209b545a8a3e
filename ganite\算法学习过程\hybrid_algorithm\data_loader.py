"""
数据加载模块：使用VGANITE的数据加载函数
"""

import numpy as np
import tensorflow as tf
import sys
import os

# 添加vganite路径以导入数据加载函数
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'vganite'))

try:
    from datasets import data_loading_twin, IHDP, Syn
except ImportError:
    print("Warning: Could not import from vganite datasets. Using fallback implementation.")
    
    def data_loading_twin(train_rate=0.8):
        """Fallback implementation for twin data loading"""
        # 这里可以实现一个简化的数据加载函数
        raise NotImplementedError("Please ensure vganite datasets module is available")

def load_data(data_name='twin', train_rate=0.8, **kwargs):
    """
    统一的数据加载接口

    Args:
        data_name: 数据集名称 ('twin', 'ihdp', 'synthetic')
        train_rate: 训练集比例
        **kwargs: 其他参数

    Returns:
        train_x, train_t, train_y, train_potential_y,
        test_x, test_t, test_y, test_potential_y
    """

    if data_name.lower() in ['twin', 'twins']:
        # 切换到 vganite 目录来加载数据
        current_dir = os.getcwd()
        vganite_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'vganite'))

        print(f"当前目录: {current_dir}")
        print(f"切换到VGANITE目录: {vganite_dir}")

        try:
            os.chdir(vganite_dir)
            print(f"成功切换到: {os.getcwd()}")
            print(f"检查数据文件: {os.path.exists('Twin/Twin_Data.csv')}")

            result = data_loading_twin(train_rate=train_rate)
            print("数据加载成功！")
            return result
        finally:
            os.chdir(current_dir)
            print(f"恢复到原目录: {os.getcwd()}")
    
    elif data_name.lower() == 'ihdp':
        # IHDP数据集加载
        replications = kwargs.get('replications', 1)
        path_data = kwargs.get('path_data', './data/IHDP')
        train, test, contfeats, binfeats = IHDP(path_data=path_data, replications=replications)
        
        # 解包数据
        (train_x, train_t, train_y), train_ite, train_potential_y = train
        (test_x, test_t, test_y), test_ite, test_potential_y = test
        
        return train_x, train_t, train_y, train_potential_y, test_x, test_t, test_y, test_potential_y
    
    elif data_name.lower() == 'synthetic':
        # 合成数据集加载
        path = kwargs.get('path', './data/Syn_1.0_1.0_0/8_8_8')
        reps = kwargs.get('reps', 1)
        train, test = Syn(path=path, reps=reps)

        # 解包数据
        (train_x, train_t, train_y), train_ite, train_potential_y = train
        (test_x, test_t, test_y), test_ite, test_potential_y = test

        return train_x, train_t, train_y, train_potential_y, test_x, test_t, test_y, test_potential_y

    elif data_name.lower() == 'custom':
        # 自定义数据集加载
        return load_custom_dataset(**kwargs)

    else:
        raise ValueError(f"Unknown data_name: {data_name}")

def load_custom_dataset(data_path, train_rate=0.8, file_format='csv', **kwargs):
    """
    加载自定义数据集

    Args:
        data_path: 数据文件路径
        train_rate: 训练集比例
        file_format: 文件格式 ('csv', 'npz', 'npy')
        **kwargs: 其他参数
            - x_cols: 特征列索引或列名
            - t_col: 治疗列索引或列名
            - y0_col: Y(0)列索引或列名
            - y1_col: Y(1)列索引或列名
            - factual_y_col: 事实结果列索引或列名（可选）

    Returns:
        train_x, train_t, train_y, train_potential_y,
        test_x, test_t, test_y, test_potential_y
    """

    # 加载数据
    if file_format.lower() == 'csv':
        import pandas as pd
        data = pd.read_csv(data_path)
        data = data.values
    elif file_format.lower() == 'npz':
        data_dict = np.load(data_path)
        # 假设npz文件包含 'x', 't', 'y0', 'y1' 键
        x = data_dict['x']
        t = data_dict['t']
        y0 = data_dict['y0']
        y1 = data_dict['y1']
        potential_y = np.column_stack([y0, y1])

        # 生成观测结果
        y = t * y1 + (1 - t) * y0

        # 合并数据
        data = np.column_stack([x, t, y, potential_y])
    elif file_format.lower() == 'npy':
        data = np.load(data_path)
    else:
        raise ValueError(f"Unsupported file format: {file_format}")

    # 提取列
    x_cols = kwargs.get('x_cols', None)
    t_col = kwargs.get('t_col', -4)  # 默认倒数第4列
    y0_col = kwargs.get('y0_col', -2)  # 默认倒数第2列
    y1_col = kwargs.get('y1_col', -1)  # 默认最后一列
    factual_y_col = kwargs.get('factual_y_col', -3)  # 默认倒数第3列

    if x_cols is None:
        # 假设除了最后4列外都是特征
        x_cols = list(range(data.shape[1] - 4))

    # 提取数据
    x = data[:, x_cols].astype(np.float32)
    t = data[:, t_col].astype(np.float32)

    # 检查是否是IHDP格式的数据（有y_factual和y_cfactual列）
    if (factual_y_col is not None and
        hasattr(data, 'shape') and data.shape[1] > max(factual_y_col, y0_col, y1_col) and
        factual_y_col == 1 and y0_col == 3 and y1_col == 4):

        # IHDP数据的正确处理方式
        y_factual = data[:, 1].astype(np.float32)  # y_factual
        y_cfactual = data[:, 2].astype(np.float32)  # y_cfactual

        # 构造正确的潜在结果
        # 对于治疗组: Y(0) = y_cfactual, Y(1) = y_factual
        # 对于控制组: Y(0) = y_factual, Y(1) = y_cfactual
        y0 = np.where(t == 1, y_cfactual, y_factual)
        y1 = np.where(t == 1, y_factual, y_cfactual)

        # 事实结果就是y_factual
        y = y_factual

    else:
        # 其他数据格式的处理方式
        y0 = data[:, y0_col].astype(np.float32)
        y1 = data[:, y1_col].astype(np.float32)

        # 如果有事实结果列，使用它；否则根据治疗生成
        if factual_y_col is not None and factual_y_col < data.shape[1]:
            y = data[:, factual_y_col].astype(np.float32)
        else:
            y = t * y1 + (1 - t) * y0

    # 构造潜在结果矩阵
    potential_y = np.column_stack([y0, y1])

    # 数据划分
    n_samples = len(x)
    idx = np.random.permutation(n_samples)
    train_size = int(train_rate * n_samples)

    train_idx = idx[:train_size]
    test_idx = idx[train_size:]

    train_x = x[train_idx]
    train_t = t[train_idx]
    train_y = y[train_idx]
    train_potential_y = potential_y[train_idx]

    test_x = x[test_idx]
    test_t = t[test_idx]
    test_y = y[test_idx]
    test_potential_y = potential_y[test_idx]

    return train_x, train_t, train_y, train_potential_y, test_x, test_t, test_y, test_potential_y

def preprocess_data(train_x, train_t, train_y, train_potential_y,
                   test_x, test_t, test_y, test_potential_y,
                   normalize_x=True, normalize_y=False, normalization_method='minmax'):
    """
    数据预处理

    Args:
        数据集元组
        normalize_x: 是否标准化特征
        normalize_y: 是否标准化结果
        normalization_method: 标准化方法 ('zscore' 或 'minmax')

    Returns:
        预处理后的数据
    """
    
    # 转换为numpy数组
    train_x = np.array(train_x, dtype=np.float32)
    train_t = np.array(train_t, dtype=np.float32).reshape(-1, 1)
    train_y = np.array(train_y, dtype=np.float32).reshape(-1, 1)
    train_potential_y = np.array(train_potential_y, dtype=np.float32)
    
    test_x = np.array(test_x, dtype=np.float32)
    test_t = np.array(test_t, dtype=np.float32).reshape(-1, 1)
    test_y = np.array(test_y, dtype=np.float32).reshape(-1, 1)
    test_potential_y = np.array(test_potential_y, dtype=np.float32)
    
    # 特征标准化
    if normalize_x:
        if normalization_method == 'minmax':
            # Min-Max标准化: (x - min) / (max - min)
            x_min = np.min(train_x, axis=0, keepdims=True)
            x_max = np.max(train_x, axis=0, keepdims=True)
            x_range = x_max - x_min + 1e-8  # 避免除零

            train_x = (train_x - x_min) / x_range
            test_x = (test_x - x_min) / x_range
        else:
            # Z-score标准化: (x - mean) / std
            x_mean = np.mean(train_x, axis=0, keepdims=True)
            x_std = np.std(train_x, axis=0, keepdims=True) + 1e-8

            train_x = (train_x - x_mean) / x_std
            test_x = (test_x - x_mean) / x_std
    
    # 结果标准化
    if normalize_y:
        if normalization_method == 'minmax':
            # Min-Max标准化: (y - min) / (max - min)
            y_min = np.min(train_y)
            y_max = np.max(train_y)
            y_range = y_max - y_min + 1e-8  # 避免除零

            train_y = (train_y - y_min) / y_range
            test_y = (test_y - y_min) / y_range
            train_potential_y = (train_potential_y - y_min) / y_range
            test_potential_y = (test_potential_y - y_min) / y_range
        else:
            # Z-score标准化: (y - mean) / std
            y_mean = np.mean(train_y)
            y_std = np.std(train_y) + 1e-8

            train_y = (train_y - y_mean) / y_std
            test_y = (test_y - y_mean) / y_std
            train_potential_y = (train_potential_y - y_mean) / y_std
            test_potential_y = (test_potential_y - y_mean) / y_std
    
    return (train_x, train_t, train_y, train_potential_y,
            test_x, test_t, test_y, test_potential_y)

def create_tf_dataset(x, t, y, batch_size, shuffle=True, repeat=True):
    """
    创建TensorFlow数据集
    
    Args:
        x, t, y: 数据数组
        batch_size: 批次大小
        shuffle: 是否打乱
        repeat: 是否重复
    
    Returns:
        tf.data.Dataset
    """
    dataset = tf.data.Dataset.from_tensor_slices((x, t, y))
    
    if shuffle:
        dataset = dataset.shuffle(buffer_size=len(x))
    
    dataset = dataset.batch(batch_size)
    
    if repeat:
        dataset = dataset.repeat()
    
    dataset = dataset.prefetch(tf.data.AUTOTUNE)
    
    return dataset

def batch_generator(x, t, y, batch_size):
    """
    批次生成器（与VGANITE兼容）
    
    Args:
        x, t, y: 数据数组
        batch_size: 批次大小
    
    Returns:
        随机批次的数据
    """
    n_samples = len(x)
    indices = np.random.choice(n_samples, batch_size, replace=True)
    
    batch_x = x[indices]
    batch_t = t[indices]
    batch_y = y[indices]
    
    return batch_x, batch_t, batch_y

def get_data_info(train_x, train_t, train_y):
    """
    获取数据集信息
    
    Args:
        train_x, train_t, train_y: 训练数据
    
    Returns:
        数据集信息字典
    """
    info = {
        'n_samples': len(train_x),
        'x_dim': train_x.shape[1],
        'treatment_ratio': np.mean(train_t),
        'outcome_ratio': np.mean(train_y),
        'x_mean': np.mean(train_x, axis=0),
        'x_std': np.std(train_x, axis=0)
    }
    
    return info

if __name__ == "__main__":
    # 测试数据加载
    print("Testing data loading...")
    
    try:
        train_x, train_t, train_y, train_potential_y, test_x, test_t, test_y, test_potential_y = load_data('twin')
        
        print(f"Train data shape: X={train_x.shape}, T={train_t.shape}, Y={train_y.shape}")
        print(f"Test data shape: X={test_x.shape}, T={test_t.shape}, Y={test_y.shape}")
        
        # 预处理
        data = preprocess_data(train_x, train_t, train_y, train_potential_y,
                              test_x, test_t, test_y, test_potential_y)
        
        print("Data preprocessing completed successfully!")
        
        # 获取数据信息
        info = get_data_info(data[0], data[1], data[2])
        print("Data info:", info)
        
    except Exception as e:
        print(f"Error loading data: {e}")
